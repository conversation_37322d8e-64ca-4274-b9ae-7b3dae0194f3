export function formatDate(datestring: string) {
  const date = new Date(datestring);
  if (!isNaN(date.getTime())) {
    const year = date.getUTCFullYear();
    let month: any = date.getUTCMonth() + 1, // getUTCMonth() returns month (0-based)
      day: any = date.getUTCDate(); // getUTCDate() returns day in UTC

    if (month <= 9) {
      month = '0' + month;
    }
    if (day <= 9) {
      day = '0' + day;
    }
    return month + '/' + day + '/' + year;
  }
}

export function getPBMLogo(pbmIdentifier: string): string {
  const baseUrl = 'https://static-assets.rxbenefits.cloud/images/pbmLogos/';

  const matches = [
    { keyword: 'Caremark', logo: 'cvs-caremark-logo-transparent.png' },
    { keyword: 'Optum', logo: 'OPT-transparent.png' },
    { keyword: 'Express Scripts', logo: 'ESI-transparent.png' },
    { keyword: 'ESI', logo: 'ESI-transparent.png' },
    { keyword: 'IlluminateRx', logo: 'illuminate-rx.png' },
  ];

  const match = matches.find(({ keyword }) => pbmIdentifier?.includes(keyword));

  return `${baseUrl}${match?.logo || ''}`;
}

export function getPBMText(pbmIdentifier: string) {
  let pbmLink;

  let pbmText;

  if (pbmIdentifier?.includes('Caremark')) {
    pbmLink =
      'https://rxbenefits-prod.us.auth0.com/samlp/e5V7UmViDlM99eEwTCO1lASn4LWo1M4y';

    pbmText = 'CVS/Caremark Member Portal';
  } else if (pbmIdentifier?.includes('Optum')) {
    pbmLink =
      'https://rxbenefits-prod.us.auth0.com/samlp/d75x0Tz6FdLwJzkORzNj4J1xY9YQ6Wy9';
    pbmText = 'OptumRx Member Portal';
  } else if (
    pbmIdentifier?.includes('Express Scripts') ||
    pbmIdentifier?.includes('ESI')
  ) {
    pbmLink =
      'https://rxbenefits-prod.us.auth0.com/samlp/ShWecDZ4ZWaAq86dydu5oHuotv90H35s';
    pbmText = 'Express Scripts Member Portal';
  } else {
    pbmLink = '';
    pbmText = '';
  }

  return { linkText: pbmText, linkURL: pbmLink };
}

export const getProductStrength = (str: string | number) => {
  const parsed = parseFloat(str as string);
  if (parsed > -1 && !parsed) {
    return '';
  }
  return str;
};

export const calculateAge = (birthdate: string) => {
  const birthDate = new Date(birthdate);
  const ageDiff = Date.now() - birthDate.getTime();
  const ageDate = new Date(ageDiff);
  return Math.abs(ageDate.getUTCFullYear() - 1970);
};
