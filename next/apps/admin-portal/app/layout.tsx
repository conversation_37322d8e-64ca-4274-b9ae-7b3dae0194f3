'use client';

import { Container, Flex } from '@chakra-ui/react';
import { <PERSON><PERSON>, Header, TimeoutModal } from '@next/admin/components';
import { usePathname } from 'next/navigation';
import { ReactNode } from 'react';

import Providers from './providers';

export default function RootLayout({ children }: { children: ReactNode }) {
  const pathname = usePathname();

  // Avoid Container styles if the URL includes "/change-request-intake/"
  const isChangeRequestPage = pathname?.includes('/change-request-intake/');

  // Hide footer for all change request intake pages (not just edit mode)
  const shouldHideFooter = isChangeRequestPage;

  return (
    <html lang="en">
      <body>
        <Providers>
          <Flex
            minHeight={shouldHideFooter ? 'auto' : '100vh'}
            direction="column"
            justifyContent="flex-start"
          >
            <TimeoutModal />
            <Header />
            <Flex
              flex={1}
              bg="#f9f9f9"
              minHeight={shouldHideFooter ? 'calc(100vh - 64px)' : 'auto'}
            >
              {isChangeRequestPage ? (
                children
              ) : (
                <Container maxW="1600px" py={12} px={[4, 4, 4, 6]}>
                  {children}
                </Container>
              )}
            </Flex>
            {!shouldHideFooter && <Footer />}
          </Flex>
        </Providers>
      </body>
    </html>
  );
}
