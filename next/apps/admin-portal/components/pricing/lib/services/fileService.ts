export const getFiles = async () => {
  const response = await fetch('/api/pricing/upload');
  if (!response.ok) {
    throw new Error('Failed to fetch files');
  }
  return response.json();
};

export const deleteFile = async (fileId: string) => {
  const response = await fetch(`/api/pricing/upload/${fileId}`, {
    method: 'DELETE',
  });
  return response;
};

export const getProcessingResults = async (fileId: string) => {
  const response = await fetch(`/api/pricing/upload/${fileId}`);
  if (!response.ok) {
    throw new Error('Failed to fetch processing results');
  }
  return response.json();
};

export const processFile = async (fileId: string) => {
  const response = await fetch(`/api/pricing/upload/${fileId}`, {
    method: 'POST',
  });
  if (!response.ok) {
    const error = await response.json();
    throw error;
  }
  return response;
};
