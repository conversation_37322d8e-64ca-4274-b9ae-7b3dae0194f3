const channels = [
  { key: 'retail', name: 'Retail', label: 'RT' },
  { key: 'retail90', name: 'Retail 90', label: 'R90' },
  { key: 'maintenance', name: 'Maintenance', label: 'MN' },
  { key: 'mail', name: 'Mail', label: 'ML' },
  { key: 'specialtyMail', name: 'Specialty Mail', label: 'SM' },
  { key: 'specialtyRetail', name: 'Specialty Retail', label: 'SR' },
  {
    key: 'limitedDistributionMail',
    name: 'Limited Distribution Mail',
    label: 'LDM',
  },
  {
    key: 'limitedDistributionRetail',
    name: 'Limited Distribution Retail',
    label: 'LDR',
  },
];

const contractParameters = [
  'contract_duration',
  'time_in_contract',
  'client_size',
  'employer_status_rxb',
  'employer_status_pbm',
  'employer_state',
];

const networkParameters = [
  'retail_network',
  'maintenance_network',
  'specialty_network',
  'specialty_days',
];

const formularyParameters = ['formulary', 'um', 'copay_tiers'];

const pricingParameters = [
  'custom_pricing',
  'custom_pricing_note',
  'discount_rebate_type',
  'pricing_tier',
  'decre_rate',
  'ldd_rebates_allocation',
  'hiv_rebates_allocation',
  'rebate_reconciliation_method',
];

const otherParameters = [
  'confirmed_with_pbm',
  'hospital_pricing',
  'egwp',
  'stop_loss',
  'inHousePharmacy',
];

const channelSections = [
  {
    key: 'retail',
    name: 'Retail',
    hasBrand: true,
    hasGeneric: true,
    hasRebate: true,
  },
  {
    key: 'retail90',
    name: 'Retail 90',
    hasBrand: true,
    hasGeneric: true,
    hasRebate: true,
  },
  {
    key: 'maintenance',
    name: 'Maintenance',
    hasBrand: true,
    hasGeneric: true,
    hasRebate: true,
  },
  {
    key: 'mail',
    name: 'Mail',
    hasBrand: true,
    hasGeneric: true,
    hasRebate: true,
  },
  {
    key: 'specialtyMail',
    name: 'Specialty Mail',
    hasBrand: true,
    hasGeneric: true,
    hasRebate: true,
  },
  {
    key: 'specialtyRetail',
    name: 'Specialty Retail',
    hasBrand: true,
    hasGeneric: true,
    hasRebate: true,
  },
  {
    key: 'limitedDistributionMail',
    name: 'Limited Distribution Mail',
    hasBrand: true,
    hasGeneric: true,
    hasRebate: true,
  },
  {
    key: 'limitedDistributionRetail',
    name: 'Limited Distribution Retail',
    hasBrand: true,
    hasGeneric: true,
    hasRebate: true,
  },
  {
    key: 'blendedSpecialty',
    name: 'Blended Specialty',
    hasBrand: true,
    hasGeneric: true,
    hasRebate: true,
  },
];

const filterLabels: Record<string, string> = {
  pbm: 'PBM',
  formulary: 'Formulary',
  clientSize: 'Client Size',
  status: 'Status',
  biosimilar: 'Biosimilar',
  weightLoss: 'Weight Loss Covergae',
  commonlyUsed: 'Commonly Used',
  searchTerm: 'Search',
};

export {
  channels,
  channelSections,
  contractParameters,
  filterLabels,
  formularyParameters,
  networkParameters,
  otherParameters,
  pricingParameters,
};
