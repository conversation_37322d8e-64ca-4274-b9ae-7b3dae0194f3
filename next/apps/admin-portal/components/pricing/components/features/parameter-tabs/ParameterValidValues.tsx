// src/components/features/parameter-tabs/ParameterValidValues.tsx

import {
  Badge,
  Box,
  Button,
  Card,
  CardBody,
  Flex,
  HStack,
  IconButton,
  Skeleton,
  Spacer,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
  VStack,
} from '@chakra-ui/react';
import { useMemo, useState } from 'react';
import { FaEdit, FaPlus, FaTrash } from 'react-icons/fa';

import { useValidValues } from '../../../lib/hooks/useValidValues';
import { ParameterValidValue } from '../../../lib/types/parameter';
import { formatDate } from '../../../lib/utils/date';
import DeleteConfirmationDialog from '../../ui/DeleteConfirmationDialog';
import { useToast } from '../../ui/toast';
import ValidValueModal from './ValidValueModal';

interface ParameterValidValuesProps {
  parameterId: string;
  validValues: ParameterValidValue[];
  onUpdateSuccess: () => void;
  isLoading?: boolean;
}

export default function ParameterValidValues({
  parameterId,
  validValues,
  onUpdateSuccess,
  isLoading = false,
}: ParameterValidValuesProps) {
  // State
  const [selectedValue, setSelectedValue] =
    useState<ParameterValidValue | null>(null);
  const [valueToDelete, setValueToDelete] =
    useState<ParameterValidValue | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Hooks
  const { toast } = useToast();
  const { createValidValue, updateValidValue, deleteValidValue } =
    useValidValues();

  // Memoized computations
  const groupedValues = useMemo(() => {
    return validValues.reduce((acc, value) => {
      const key = value.pbm_id || 'default';
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(value);
      return acc;
    }, {} as Record<string, ParameterValidValue[]>);
  }, [validValues]);

  const availablePbms = useMemo(() => {
    return Array.from(
      new Set(
        validValues
          .filter((value) => value.pbm_id)
          .map((value) => value.pbm_id!)
      )
    ).sort();
  }, [validValues]);

  // Event Handlers
  const handleAdd = () => {
    setSelectedValue(null);
    setShowEditModal(true);
  };

  const handleEdit = (value: ParameterValidValue) => {
    setSelectedValue(value);
    setShowEditModal(true);
  };

  const handleDelete = (value: ParameterValidValue) => {
    setValueToDelete(value);
    setShowDeleteDialog(true);
  };

  const handleSave = async (value: Partial<ParameterValidValue>) => {
    try {
      setIsProcessing(true);

      if (value.id) {
        await updateValidValue.mutateAsync({
          parameterId,
          data: value,
        });
      } else {
        await createValidValue.mutateAsync({
          parameterId,
          data: value,
        });
      }

      toast({
        title: 'Success',
        description: `Valid value ${
          value.id ? 'updated' : 'created'
        } successfully`,
        variant: 'success',
      });

      onUpdateSuccess();
      setShowEditModal(false);
    } catch (error) {
      console.error('Error saving valid value:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to save valid value',
        variant: 'error',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!valueToDelete) return;

    try {
      setIsProcessing(true);
      await deleteValidValue.mutateAsync({
        parameterId,
        data: { id: valueToDelete.id },
      });

      toast({
        title: 'Success',
        description: 'Valid value deleted successfully',
        variant: 'success',
      });

      onUpdateSuccess();
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Error deleting valid value:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to delete valid value',
        variant: 'error',
      });
    } finally {
      setIsProcessing(false);
      setValueToDelete(null);
    }
  };

  if (isLoading) {
    return (
      <VStack spacing={4} align="stretch">
        <Skeleton height="40px" />
        <Skeleton height="200px" />
      </VStack>
    );
  }

  // Empty state
  if (validValues.length === 0) {
    return (
      <VStack spacing={4} align="stretch">
        <Flex align="center">
          <Text fontSize="lg" fontWeight="medium">
            Valid Values
          </Text>
          <Spacer />
          <Button
            leftIcon={<FaPlus size={14} />}
            colorScheme="blue"
            onClick={handleAdd}
            size="sm"
          >
            Add Value
          </Button>
        </Flex>
        <Card>
          <CardBody>
            <Box p={8} textAlign="center">
              <Text color="gray.600">
                {`No valid values defined yet. Click "Add Value" to create one.`}
              </Text>
            </Box>
          </CardBody>
        </Card>

        <ValidValueModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          onSave={handleSave}
          value={selectedValue}
          parameterId={parameterId}
          isLoading={isProcessing}
          availablePbms={availablePbms}
        />
      </VStack>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Flex mb={6} align="center">
        <Text fontSize="lg" fontWeight="medium">
          Valid Values
        </Text>
        <Spacer />
        <Button
          leftIcon={<FaPlus size={14} />}
          colorScheme="blue"
          onClick={handleAdd}
          size="sm"
        >
          Add Value
        </Button>
      </Flex>

      {/* Values Tables */}
      {Object.entries(groupedValues).map(([pbmId, values]) => (
        <Box key={pbmId} mb={8}>
          <HStack mb={4}>
            <Badge
              colorScheme={pbmId === 'default' ? 'gray' : 'blue'}
              px={3}
              py={1}
              borderRadius="full"
            >
              {pbmId === 'default' ? 'Global Values' : `PBM: ${pbmId}`}
            </Badge>
            <Text fontSize="sm" color="gray.600">
              {values.length} value{values.length !== 1 ? 's' : ''}
            </Text>
          </HStack>

          <Box overflowX="auto">
            <Table variant="simple" size="sm">
              <Thead>
                <Tr>
                  <Th>Value</Th>
                  <Th>Label</Th>
                  <Th>Description</Th>
                  <Th>Effective From</Th>
                  <Th>Effective To</Th>
                  <Th width="100px">Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {values.map((value) => (
                  <Tr key={value.id}>
                    <Td fontFamily="mono">{value.value}</Td>
                    <Td>{value.label}</Td>
                    <Td maxW="300px" isTruncated title={value.description}>
                      {value.description || '-'}
                    </Td>
                    <Td>{formatDate(value.effective_from)}</Td>
                    <Td>
                      {value.effective_to
                        ? formatDate(value.effective_to)
                        : '-'}
                    </Td>
                    <Td>
                      <HStack spacing={2}>
                        <Tooltip label="Edit value">
                          <IconButton
                            aria-label="Edit value"
                            icon={<FaEdit size={16} />}
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.currentTarget.blur();
                              handleEdit(value);
                            }}
                          />
                        </Tooltip>
                        <Tooltip label="Delete value">
                          <IconButton
                            aria-label="Delete value"
                            icon={<FaTrash size={16} />}
                            size="sm"
                            variant="ghost"
                            colorScheme="red"
                            onClick={(e) => {
                              e.currentTarget.blur();
                              handleDelete(value);
                            }}
                          />
                        </Tooltip>
                      </HStack>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </Box>
        </Box>
      ))}

      {/* Modals */}
      <ValidValueModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSave={handleSave}
        value={selectedValue}
        parameterId={parameterId}
        isLoading={isProcessing}
        availablePbms={availablePbms}
      />

      <DeleteConfirmationDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Valid Value"
        message={
          valueToDelete
            ? `Are you sure you want to delete the value "${valueToDelete.label}"? This action cannot be undone.`
            : 'Are you sure you want to delete this value? This action cannot be undone.'
        }
        isLoading={isProcessing}
      />
    </Box>
  );
}
