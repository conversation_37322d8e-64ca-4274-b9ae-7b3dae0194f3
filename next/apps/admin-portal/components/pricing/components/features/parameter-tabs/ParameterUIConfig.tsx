import {
  <PERSON>,
  <PERSON>ton,
  Card,
  CardBody,
  Di<PERSON>r,
  <PERSON>ing,
  HStack,
  Text,
  VStack,
} from '@chakra-ui/react';
import React, { useCallback, useEffect, useState } from 'react';

import { ParameterUIConfig } from '../../../lib/types/parameter';
import { componentTypes } from '../../../lib/utils/parameterManagementConstants';
import FormItem from '../../ui/FormItem';
import { useToast } from '../../ui/toast';

interface ParameterUIConfigProps {
  parameterId: string;
  uiConfig: ParameterUIConfig | null;
  onUpdateSuccess: () => void;
}

interface FormData extends ParameterUIConfig {
  touched: Record<string, boolean>;
}

export default function ParameterUIConfigForm({
  parameterId,
  uiConfig,
  onUpdateSuccess,
}: ParameterUIConfigProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  // Form state
  const [formData, setFormData] = useState<FormData>({
    parameter_id: parameterId,
    component_type: 'input',
    label: '',
    placeholder: '',
    help_text: '',
    validation_rules: null,
    dependencies: null,
    is_required: false,
    is_visible: true,
    config_data: null,
    touched: {},
  });

  // Initialize form with existing config
  useEffect(() => {
    if (uiConfig) {
      setFormData({ ...uiConfig, touched: {} });
    }
  }, [uiConfig]);

  // Form validation
  const getErrors = useCallback(() => {
    const errors: Record<string, string> = {};
    if (!formData.label.trim()) {
      errors.label = 'label is required';
    }
    if (!formData.placeholder.trim()) {
      errors.placeholder = 'Placeholder is required';
    }
    if (formData.config_data && typeof formData.config_data === 'string') {
      try {
        // Try to parse the JSON data
        const parsedData: Record<string, any> = JSON.parse(
          formData.config_data
        );

        // Check if parsedData is an object (you can modify this to your own validation)
        if (typeof parsedData !== 'object' || parsedData === null) {
          errors.config_data =
            'Config data must be a valid JSON object or array.';
        }
      } catch (error) {
        // If JSON parsing fails, report invalid JSON format
        errors.config_data = 'Invalid JSON format. Please correct the input.';
      }
    }

    return errors;
  }, [formData]);

  const errors = getErrors();

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
      touched: { ...prev.touched, [name]: true },
    }));
  };

  const handleSwitchChange =
    (name: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({
        ...prev,
        [name]: e.target.checked,
        touched: { ...prev.touched, [name]: true },
      }));
    };

  const handleSave = async () => {
    const validationErrors = getErrors();
    if (Object.keys(validationErrors).length > 0) {
      Object.keys(validationErrors).forEach((field) => {
        toast({
          title: 'Validation Error',
          description: validationErrors[field],
          variant: 'error',
        });
      });
      return;
    }

    try {
      setIsSaving(true);
      const { touched, ...payload } = formData;
      const url = uiConfig
        ? `/api/pricing/ui-config/${parameterId}`
        : `/api/pricing/ui-config`;
      const response = await fetch(url, {
        method: uiConfig ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error('Failed to save UI configuration');
      }

      toast({
        title: 'Success',
        description: 'UI configuration saved successfully',
        variant: 'success',
      });

      onUpdateSuccess();
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving UI config:', error);
      toast({
        title: 'Error',
        description: 'Failed to save UI configuration',
        variant: 'error',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (uiConfig) {
      setFormData({ ...uiConfig, touched: {} });
    } else {
      setFormData({
        parameter_id: parameterId,
        component_type: 'input',
        label: '',
        placeholder: '',
        help_text: '',
        validation_rules: null,
        dependencies: null,
        is_required: false,
        is_visible: true,
        config_data: null,
        touched: {},
      });
    }
    setIsEditing(false);
  };

  return (
    <Box>
      <HStack justify="space-between" mb={6}>
        <Box>
          <Heading size="md">UI Configuration</Heading>
          <Text color="gray.600" fontSize="sm">
            Configure how this parameter appears in the user interface
          </Text>
        </Box>
        <Button
          onClick={() => {
            if (isEditing) handleCancel();
            else setIsEditing(!isEditing);
          }}
          isDisabled={isSaving}
        >
          {isEditing ? 'Cancel' : 'Edit'}
        </Button>
      </HStack>

      <Card>
        <CardBody>
          <VStack spacing={6} align="stretch">
            <FormItem
              isRequired
              label="Component Type"
              name="component_type"
              value={formData.component_type}
              onChange={handleInputChange}
              isReadOnly={!isEditing}
              fieldType="select"
              options={componentTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            />

            <FormItem
              isRequired
              isInvalid={formData.touched.label && !!errors.label}
              label="Label"
              name="label"
              value={formData.label}
              onChange={handleInputChange}
              isReadOnly={!isEditing}
              errorMessage={errors.label}
              fieldType="input"
              placeholder="Enter field label"
            />

            <FormItem
              isRequired
              isInvalid={formData.touched.placeholder && !!errors.placeholder}
              label="Placeholder"
              name="placeholder"
              value={formData.placeholder}
              onChange={handleInputChange}
              isReadOnly={!isEditing}
              errorMessage={errors.placeholder}
              fieldType="input"
              placeholder="Enter placeholder text"
            />

            <FormItem
              label="Help Text"
              name="help_text"
              value={formData.help_text}
              onChange={handleInputChange}
              isReadOnly={!isEditing}
              placeholder="Enter help text or description"
              rows={3}
              fieldType="textarea"
            />

            <Divider />

            <HStack spacing={8}>
              <FormItem
                label="Required Field"
                name="is_required"
                value={formData.is_required}
                onChange={handleSwitchChange('is_required')}
                isReadOnly={!isEditing}
                fieldType="switch"
                formControlProps={{
                  display: 'flex',
                  alignItems: 'center', // Custom props for FormControl
                }}
                labelProps={{ mb: '0' }}
              />

              <FormItem
                label="Visible"
                name="is_visible"
                value={formData.is_visible}
                onChange={handleSwitchChange('is_visible')}
                isReadOnly={!isEditing}
                fieldType="switch"
                formControlProps={{
                  display: 'flex',
                  alignItems: 'center', // Custom props for FormControl
                }}
                labelProps={{ mb: '0' }}
              />
            </HStack>

            <FormItem
              isInvalid={formData.touched.config_data && !!errors.config_data}
              label="Configuration"
              name="config_data"
              value={
                typeof formData.config_data === 'string'
                  ? formData.config_data
                  : formData.config_data
                  ? JSON.stringify(formData.config_data, null, 2)
                  : ''
              }
              onChange={(e) => {
                const value = e.target.value;
                if (value.trim() === '') {
                  setFormData((prev) => ({
                    ...prev,
                    config_data: null, // Clear config_data if empty
                    touched: { ...prev.touched, config_data: true },
                  }));
                } else {
                  try {
                    const parsed = JSON.parse(value);
                    if (typeof parsed === 'object' && parsed != null) {
                      setFormData((prev) => ({
                        ...prev,
                        config_data: parsed,
                        touched: { ...prev.touched, config_data: true },
                      }));
                    } else {
                      throw new Error('Invalid input');
                    }
                  } catch (error) {
                    // Handle invalid JSON
                    setFormData((prev) => ({
                      ...prev,
                      config_data: value, // Keep the raw string if invalid JSON
                      touched: { ...prev.touched, config_data: true },
                    }));
                  }
                }
              }}
              isReadOnly={!isEditing}
              placeholder="Enter component configuration in JSON format"
              rows={5}
              errorMessage={errors.config_data}
              fieldType="textarea"
            />
          </VStack>

          {isEditing && (
            <HStack justify="flex-end" mt={6}>
              <Button onClick={handleCancel} isDisabled={isSaving}>
                Cancel
              </Button>
              <Button
                colorScheme="blue"
                onClick={handleSave}
                isLoading={isSaving}
              >
                Save Changes
              </Button>
            </HStack>
          )}
        </CardBody>
      </Card>
    </Box>
  );
}
