import { useUser } from '@auth0/nextjs-auth0/client';
import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Badge,
  Box,
  Button,
  Divider,
  Heading,
  HStack,
  Text,
  useDisclosure,
  VStack,
} from '@chakra-ui/react';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { useUpdateParameter } from '../../../lib/queries/parameters';
import { Parameter } from '../../../lib/types/parameter';
import { formatDate } from '../../../lib/utils/date';
import { parameterGroups } from '../../../lib/utils/parameterManagementConstants';
import FormItem from '../../ui/FormItem';
import { useToast } from '../../ui/toast';

interface ParameterBasicInfoProps {
  parameter: Parameter;
  onUpdateSuccess?: () => void;
}

interface FormData
  extends Omit<
    Parameter,
    'created_at' | 'updated_at' | 'created_by' | 'updated_by'
  > {
  touched: Record<string, boolean>;
}

export default function ParameterBasicInfo({
  parameter,
  onUpdateSuccess,
}: ParameterBasicInfoProps) {
  // State management
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    parameter_id: parameter.parameter_id,
    name: parameter.name,
    description: parameter.description || '',
    group_name: parameter.group_name,
    is_pbm_specific: parameter.is_pbm_specific,
    sort_order: parameter.sort_order,
    is_active: parameter.is_active,
    touched: {},
  });

  const { user } = useUser();

  // Hooks
  const { toast } = useToast();
  const updateParameterMutation = useUpdateParameter();
  const {
    isOpen: isDiscardOpen,
    onOpen: onDiscardOpen,
    onClose: onDiscardClose,
  } = useDisclosure();
  const cancelRef = useRef<HTMLButtonElement>(null);

  // update the formData when the parameter is changed
  useEffect(() => {
    setFormData({
      parameter_id: parameter.parameter_id,
      name: parameter.name,
      description: parameter.description || '',
      group_name: parameter.group_name,
      is_pbm_specific: parameter.is_pbm_specific,
      sort_order: parameter.sort_order,
      is_active: parameter.is_active,
      touched: {},
    });
  }, [parameter]);

  // Form validation
  const getErrors = useCallback(() => {
    const errors: Record<string, string> = {};
    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }
    if (!formData.group_name.trim()) {
      errors.group_name = 'Group is required';
    }
    if (formData.sort_order < 0) {
      errors.sort_order = 'Sort order must be a positive number';
    }
    return errors;
  }, [formData]);

  const errors = getErrors();

  // Check if form is dirty
  const isDirty = useCallback(() => {
    return (
      formData.name !== parameter.name ||
      formData.description !== parameter.description ||
      formData.group_name !== parameter.group_name ||
      formData.is_pbm_specific !== parameter.is_pbm_specific ||
      formData.sort_order !== parameter.sort_order ||
      formData.is_active !== parameter.is_active
    );
  }, [formData, parameter]);

  // Event handlers
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]:
        type === 'number'
          ? value === '' || /^\d+$/.test(value)
            ? parseInt(value, 10)
            : prev.sort_order
          : value,
      touched: { ...prev.touched, [name]: true },
    }));
  };

  const handleSwitchChange =
    (name: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({
        ...prev,
        [name]: e.target.checked,
        touched: { ...prev.touched, [name]: true },
      }));
    };

  const handleSave = async () => {
    const validationErrors = getErrors();
    if (Object.keys(validationErrors).length > 0) {
      Object.keys(validationErrors).forEach((field) => {
        toast({
          title: 'Validation Error',
          description: validationErrors[field],
          variant: 'error',
        });
      });
      return;
    }

    try {
      await updateParameterMutation.mutateAsync({
        parameterId: parameter.parameter_id,
        data: {
          name: formData.name,
          description: formData.description,
          group_name: formData.group_name,
          is_pbm_specific: formData.is_pbm_specific,
          sort_order: formData.sort_order,
          is_active: formData.is_active,
          updated_by: user?.email,
        },
      });

      toast({
        title: 'Success',
        description: 'Parameter updated successfully',
        variant: 'success',
      });

      onUpdateSuccess?.();
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving parameter:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to update parameter',
        variant: 'error',
      });
    }
  };

  const handleCancel = () => {
    if (isDirty()) {
      onDiscardOpen();
    } else {
      setIsEditing(false);
    }
  };

  const handleDiscard = () => {
    setFormData({
      parameter_id: parameter.parameter_id,
      name: parameter.name,
      description: parameter.description || '',
      group_name: parameter.group_name,
      is_pbm_specific: parameter.is_pbm_specific,
      sort_order: parameter.sort_order,
      is_active: parameter.is_active,
      touched: {},
    });
    setIsEditing(false);
    onDiscardClose();
  };

  return (
    <>
      <Box>
        <HStack justify="space-between" align="top" mb={6}>
          <Box>
            <HStack mb={2}>
              <Heading size="md">{parameter.name}</Heading>
              <Badge colorScheme={parameter.is_active ? 'green' : 'gray'}>
                {parameter.is_active ? 'Active' : 'Inactive'}
              </Badge>
              {parameter.is_pbm_specific && (
                <Badge colorScheme="blue">PBM Specific</Badge>
              )}
            </HStack>
            <Text fontSize="sm" color="gray.600">
              ID: {parameter.parameter_id}
            </Text>
          </Box>
          <Button
            onClick={() => {
              if (isEditing) handleCancel();
              else setIsEditing(!isEditing);
            }}
            isDisabled={updateParameterMutation.isPending}
          >
            {isEditing ? 'Cancel' : 'Edit'}
          </Button>
        </HStack>

        <VStack spacing={6} align="stretch">
          <FormItem
            isRequired
            isInvalid={formData.touched.name && !!errors.name}
            label="Name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            isReadOnly={!isEditing}
            errorMessage={errors.name}
            fieldType="input"
          />

          <FormItem
            label="Description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            isReadOnly={!isEditing}
            fieldType="textarea"
            rows={4}
          />

          <FormItem
            isRequired
            isInvalid={formData.touched.group_name && !!errors.group_name}
            placeholder="Select Group"
            label="Group"
            name="group_name"
            value={formData.group_name}
            onChange={handleInputChange}
            isReadOnly={!isEditing}
            errorMessage={errors.group_name}
            fieldType="select"
            options={parameterGroups.map((type) => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          />

          <HStack spacing={8}>
            <FormItem
              label="PBM Specific"
              name="is_pbm_specific"
              value={formData.is_pbm_specific}
              onChange={handleSwitchChange('is_pbm_specific')}
              isReadOnly={!isEditing}
              fieldType="switch"
              formControlProps={{
                display: 'flex',
                alignItems: 'center', // Custom props for FormControl
              }}
              labelProps={{ mb: '0' }}
            />

            <FormItem
              label="Active"
              name="is_active"
              value={formData.is_active}
              onChange={handleSwitchChange('is_active')}
              isReadOnly={!isEditing}
              fieldType="switch"
              formControlProps={{
                display: 'flex',
                alignItems: 'center', // Custom props for FormControl
              }}
              labelProps={{ mb: '0' }}
            />
          </HStack>

          <FormItem
            isInvalid={formData.touched.sort_order && !!errors.sort_order}
            label="Sort Order"
            name="sort_order"
            fieldFormat="number"
            value={formData.sort_order}
            onChange={handleInputChange}
            isReadOnly={!isEditing}
            errorMessage={errors.sort_order}
            fieldType="input"
            width="100px"
            min={0}
          />

          <Divider />

          <Box>
            <Text fontSize="sm" color="gray.600">
              Created by {parameter.created_by} on{' '}
              {formatDate(parameter.created_at)}
            </Text>
            {parameter.updated_by && (
              <Text fontSize="sm" color="gray.600">
                Last updated by {parameter.updated_by} on{' '}
                {formatDate(parameter.updated_at)}
              </Text>
            )}
          </Box>
        </VStack>

        {isEditing && (
          <HStack spacing={4} justify="flex-end" mt={6}>
            <Button
              onClick={handleCancel}
              isDisabled={updateParameterMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleSave}
              isLoading={updateParameterMutation.isPending}
            >
              Save Changes
            </Button>
          </HStack>
        )}
      </Box>

      {/* Discard Changes Dialog */}
      <AlertDialog
        isOpen={isDiscardOpen}
        onClose={onDiscardClose}
        leastDestructiveRef={cancelRef}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader>Discard Changes?</AlertDialogHeader>
            <AlertDialogBody>
              Are you sure you want to discard your changes? This action cannot
              be undone.
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onDiscardClose}>
                Cancel
              </Button>
              <Button colorScheme="red" ml={3} onClick={handleDiscard}>
                Discard Changes
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </>
  );
}
