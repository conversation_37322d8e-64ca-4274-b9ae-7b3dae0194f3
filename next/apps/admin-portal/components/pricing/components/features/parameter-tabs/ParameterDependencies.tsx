// src/components/features/parameter-tabs/ParameterDependencies.tsx
import {
  Badge,
  Box,
  Button,
  Card,
  CardBody,
  Heading,
  HStack,
  IconButton,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
  useDisclosure,
  VStack,
} from '@chakra-ui/react';
import { useState } from 'react';
import { FaEdit, FaPlus, FaTrash } from 'react-icons/fa';

import { ParameterDependency } from '../../../lib/types/parameter';
import { formatDate } from '../../../lib/utils/date';
import {
  effectColorScheme,
  effectLabels,
} from '../../../lib/utils/parameterManagementConstants';
import DeleteConfirmationDialog from '../../ui/DeleteConfirmationDialog';
import { useToast } from '../../ui/toast';
import DependencyModal from './DependencyModal';

interface ParameterDependenciesProps {
  parameterId: string;
  dependencies: ParameterDependency[];
  onUpdateSuccess: () => void;
}

export default function ParameterDependencies({
  parameterId,
  dependencies,
  onUpdateSuccess,
}: ParameterDependenciesProps) {
  // State
  const [selectedDependency, setSelectedDependency] =
    useState<ParameterDependency | null>(null);
  const [dependencyToDelete, setDependencyToDelete] =
    useState<ParameterDependency | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeOnly, setActiveOnly] = useState(true);

  // UI controls
  const {
    isOpen: isModalOpen,
    onOpen: onModalOpen,
    onClose: onModalClose,
  } = useDisclosure();

  const {
    isOpen: isDeleteOpen,
    onOpen: onDeleteOpen,
    onClose: onDeleteClose,
  } = useDisclosure();

  const { toast } = useToast();

  // Filter active dependencies
  const filteredDependencies = activeOnly
    ? dependencies.filter(
        (dep) => !dep.effective_to || new Date(dep.effective_to) > new Date()
      )
    : dependencies;

  // Group dependencies by controlling parameter
  const groupedDependencies = filteredDependencies.reduce((acc, dep) => {
    if (!acc[dep.controlling_parameter_id]) {
      acc[dep.controlling_parameter_id] = [];
    }
    acc[dep.controlling_parameter_id].push(dep);
    return acc;
  }, {} as Record<string, ParameterDependency[]>);

  // Handlers
  const handleAdd = () => {
    setSelectedDependency(null);
    onModalOpen();
  };

  const handleEdit = (dependency: ParameterDependency) => {
    setSelectedDependency(dependency);
    onModalOpen();
  };

  const handleDelete = (dependency: ParameterDependency) => {
    setDependencyToDelete(dependency);
    onDeleteOpen();
  };

  const handleSave = async (dependency: Partial<ParameterDependency>) => {
    try {
      setIsLoading(true);
      const isNew = !dependency.id;
      const method = isNew ? 'POST' : 'PUT';
      const url = isNew
        ? `/api/pricing/dependencies`
        : `/api/pricing/dependencies/${dependency.id}`;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...dependency,
          parameter_id: parameterId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save dependency');
      }

      toast({
        title: 'Success',
        description: `Dependency ${isNew ? 'created' : 'updated'} successfully`,
        variant: 'success',
      });

      onUpdateSuccess();
      onModalClose();
    } catch (error) {
      console.error('Error saving dependency:', error);
      toast({
        title: 'Error',
        description: 'Failed to save dependency',
        variant: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!dependencyToDelete) return;

    try {
      setIsLoading(true);
      const response = await fetch(
        `/api/pricing/dependencies/${dependencyToDelete.id}`,
        { method: 'DELETE' }
      );

      if (!response.ok) {
        throw new Error('Failed to delete dependency');
      }

      toast({
        title: 'Success',
        description: 'Dependency deleted successfully',
        variant: 'success',
      });

      onUpdateSuccess();
      onDeleteClose();
    } catch (error) {
      console.error('Error deleting dependency:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete dependency',
        variant: 'error',
      });
    } finally {
      setIsLoading(false);
      setDependencyToDelete(null);
    }
  };

  return (
    <Box>
      <HStack justify="space-between" mb={6}>
        <Box>
          <Heading size="md">Dependencies</Heading>
          <Text color="gray.600" fontSize="sm">
            Manage parameter dependencies and relationships
          </Text>
        </Box>
        <HStack spacing={4}>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setActiveOnly(!activeOnly)}
          >
            {activeOnly ? 'Show All' : 'Show Active Only'}
          </Button>
          <Button
            leftIcon={<FaPlus size={14} />}
            size="sm"
            colorScheme="blue"
            onClick={handleAdd}
          >
            Add Dependency
          </Button>
        </HStack>
      </HStack>

      {Object.entries(groupedDependencies).length === 0 ? (
        <Card>
          <CardBody>
            <Box textAlign="center" py={8}>
              <Text color="gray.600">
                {`No dependencies defined yet. Click "Add Dependency" to create one.`}
              </Text>
            </Box>
          </CardBody>
        </Card>
      ) : (
        <VStack spacing={6} align="stretch">
          {Object.entries(groupedDependencies).map(([controllingId, deps]) => (
            <Card key={controllingId}>
              <CardBody>
                <VStack align="stretch" spacing={4}>
                  <HStack>
                    <Text fontWeight="medium">
                      {deps[0].controlling_parameter_name}
                    </Text>
                    <Badge>
                      {deps.length} rule{deps.length !== 1 ? 's' : ''}
                    </Badge>
                  </HStack>

                  <Box overflowX="auto">
                    <Table size="sm">
                      <Thead>
                        <Tr>
                          <Th>Condition</Th>
                          <Th>Effect</Th>
                          <Th>PBM Specific</Th>
                          <Th>Effective Period</Th>
                          <Th width="100px"></Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        {deps.map((dep) => (
                          <Tr key={dep.id}>
                            <Td>
                              <Text fontSize="sm">
                                {dep.condition_type === 'equals'
                                  ? '='
                                  : dep.condition_type === 'not_equals'
                                  ? '≠'
                                  : dep.condition_type === 'in'
                                  ? '∈'
                                  : '∉'}{' '}
                                <code>{dep.condition_value}</code>
                              </Text>
                            </Td>
                            <Td>
                              <Badge
                                colorScheme={effectColorScheme[dep.effect]}
                                variant="subtle"
                              >
                                {effectLabels[dep.effect]}
                              </Badge>
                            </Td>
                            <Td>
                              {dep.pbm_id ? (
                                <Badge variant="outline">
                                  PBM: {dep.pbm_id}
                                </Badge>
                              ) : (
                                <Text fontSize="sm" color="gray.600">
                                  GLOBAL
                                </Text>
                              )}
                            </Td>
                            <Td fontSize="sm">
                              <VStack align="start" spacing={0}>
                                <Text>
                                  From: {formatDate(dep.effective_from)}
                                </Text>
                                {dep.effective_to && (
                                  <Text>
                                    To: {formatDate(dep.effective_to)}
                                  </Text>
                                )}
                              </VStack>
                            </Td>
                            <Td>
                              <HStack justify="flex-end" spacing={1}>
                                <Tooltip label="Edit dependency">
                                  <IconButton
                                    aria-label="Edit dependency"
                                    icon={<FaEdit size={16} />}
                                    size="sm"
                                    variant="ghost"
                                    onClick={(e) => {
                                      e.currentTarget.blur();
                                      handleEdit(dep);
                                    }}
                                  />
                                </Tooltip>
                                <Tooltip label="Delete dependency">
                                  <IconButton
                                    aria-label="Delete dependency"
                                    icon={<FaTrash size={16} />}
                                    size="sm"
                                    variant="ghost"
                                    colorScheme="red"
                                    onClick={(e) => {
                                      e.currentTarget.blur();
                                      handleDelete(dep);
                                    }}
                                  />
                                </Tooltip>
                              </HStack>
                            </Td>
                          </Tr>
                        ))}
                      </Tbody>
                    </Table>
                  </Box>
                </VStack>
              </CardBody>
            </Card>
          ))}
        </VStack>
      )}

      <DependencyModal
        isOpen={isModalOpen}
        onClose={onModalClose}
        onSave={handleSave}
        dependency={selectedDependency}
        isLoading={isLoading}
      />

      <DeleteConfirmationDialog
        isOpen={isDeleteOpen}
        onClose={onDeleteClose}
        onConfirm={handleDeleteConfirm}
        title="Delete Dependency"
        message={
          dependencyToDelete
            ? `Are you sure you want to delete this dependency rule? This action cannot be undone.`
            : 'Are you sure you want to delete this dependency? This action cannot be undone.'
        }
        isLoading={isLoading}
      />
    </Box>
  );
}
