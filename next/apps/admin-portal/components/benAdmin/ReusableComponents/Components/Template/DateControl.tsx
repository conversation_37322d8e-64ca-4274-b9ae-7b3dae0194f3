import { SmallCloseIcon } from '@chakra-ui/icons';
import { CalendarIcon } from '@chakra-ui/icons';
import {
  chakra,
  Flex,
  FormControl,
  FormControlProps,
  FormErrorMessage,
  FormLabel,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  StyleProps,
} from '@chakra-ui/react';
import { DEFAULT_DATE_FORMAT_FOR_DATE_PICKER } from '@next/shared/constants';
import { SingleDatepicker } from 'chakra-dayzed-datepicker';
import { DatepickerProps } from 'chakra-dayzed-datepicker/dist/utils/commonTypes';
import React, { FC, useEffect, useState } from 'react';

import { CircularQuestionIcon } from '../Form/components/sharables/CircularQuestionIcon';
import { FieldError, FieldWarning } from '../InlineFieldIcons';

type FormControlDateProps = {
  label?: string;
  placeholder?: string;
  isRequired?: boolean;
  isDisabled?: boolean;
  name?: string;
  labelProps?: StyleProps;
  containerProps?: FormControlProps;
  dateProps?: DatepickerProps;
  showClearButton?: boolean;
  clearAction?: () => void;
  onChange?: (e: Date | undefined) => void;
  onBlur?: () => void;
  value?: Date | undefined;
  error?: boolean;
  errorType?: string;
  defaultValue?: Date;
  inModal?: boolean;
  infoText?: string;
};

export const DateControl: FC<FormControlDateProps> = ({
  label,
  placeholder = 'Select date', // Default placeholder
  isRequired,
  isDisabled,
  name,
  labelProps,
  containerProps,
  dateProps = {},
  showClearButton = false,
  clearAction = () => null,
  onChange,
  onBlur,
  value,
  error,
  errorType,
  defaultValue,
  inModal,
  infoText,
}) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    value || defaultValue
  );

  const internalChange = (date: Date | undefined) => {
    setSelectedDate(date);
    if (onChange) {
      onChange(date);
    }

    // Trigger onBlur after date selection to ensure validation runs
    if (onBlur) {
      setTimeout(onBlur, 100);
    }
  };

  useEffect(() => {
    if (inModal) {
      const style = document.createElement('style');
      style.type = 'text/css';
      style.innerHTML = `.css-1ews2c8 { z-index: 100 !important; } .css-wl0d9u { z-index: 10 !important; } .css-hdd9l7 { z-index: 10 !important; }`;
      document.head.appendChild(style);

      return () => {
        document.head.removeChild(style);
      };
    }
  }, [inModal]);

  // Add global style to remove cursor from date inputs
  useEffect(() => {
    const style = document.createElement('style');
    style.type = 'text/css';
    style.innerHTML = `
      .date-picker-no-cursor input {
        cursor: pointer !important;
        caret-color: transparent !important;
      }
      .date-picker-no-cursor input:focus {
        box-shadow: 0 0 0 1px #3182ce !important;
        border-color: #3182ce !important;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Update local state when external value changes
  useEffect(() => {
    setSelectedDate(value);
  }, [value]);

  const handleClear = () => {
    setSelectedDate(undefined);
    if (clearAction) clearAction();

    // Trigger onBlur after clearing to ensure validation runs
    if (onBlur) {
      setTimeout(onBlur, 100);
    }
  };

  // Create wrapper for SingleDatepicker with onBlur support and placeholder
  const datePickerProps = {
    ...dateProps,
    configs: {
      ...(dateProps.propsConfigs || {}),
      dateFormat: DEFAULT_DATE_FORMAT_FOR_DATE_PICKER,
    },
    // Add onBlur and placeholder to inputProps
    propsConfigs: {
      ...(dateProps.propsConfigs || {}),
      inputProps: {
        ...(dateProps.propsConfigs?.inputProps || {}),
        placeholder: placeholder, // Pass the placeholder to the input
        readOnly: true, // Prevent keyboard input and blinking cursor
        cursor: 'pointer', // Show pointer cursor instead of text cursor
        style: { caretColor: 'transparent' },
        _hover: { cursor: 'pointer' }, // Ensure pointer cursor on hover
        borderColor: error ? 'red.500' : 'gray.300',
        pl: error ? '32px' : '',

        onBlur: (event: React.FocusEvent<HTMLInputElement, Element>) => {
          // Call the original onBlur if it exists
          if (dateProps.propsConfigs?.inputProps?.onBlur) {
            dateProps.propsConfigs.inputProps.onBlur(event);
          }
          // Call our onBlur
          if (onBlur) onBlur();
        },
      },
    },
  };

  return (
    <FormControl {...containerProps}>
      {label && (
        <Flex alignItems="flex-start" mb={1}>
          <FormLabel
            color="#676767"
            mb={0}
            optionalIndicator={
              isRequired ? <chakra.span color="red">{' *'}</chakra.span> : null
            }
            noOfLines={1}
            overflow="hidden"
            textOverflow="ellipsis"
            whiteSpace="nowrap"
            sx={{
              fontSize: 'clamp(10px, 2vw, 14px)',
              minWidth: 0,
            }}
            {...labelProps}
          >
            {label}
          </FormLabel>
          {infoText && <CircularQuestionIcon tooltiptext={infoText} ml={-2} />}
        </Flex>
      )}
      <Flex direction="column" className="date-picker-no-cursor">
        <InputGroup>
          {error && (
            <InputLeftElement
              h="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              pointerEvents="none"
            >
              {(errorType === 'zodValidation' ||
                errorType === 'required' ||
                errorType === 'field-error') &&
                FieldError()}
              {errorType === 'field-warning' && FieldWarning()}
            </InputLeftElement>
          )}
          <SingleDatepicker
            disabled={isDisabled}
            usePortal
            onDateChange={internalChange}
            date={selectedDate}
            {...datePickerProps}
          />
          {/* Always show calendar icon unless there's a clear button */}
          {!showClearButton && (
            <InputRightElement
              h="-webkit-fill-available"
              justifyContent="flex-end"
              pr={2}
              pointerEvents="none"
            >
              <CalendarIcon color="#ADADAD" boxSize="12px" />
            </InputRightElement>
          )}
          {/* Only show clear button when needed */}
          {showClearButton && (
            <InputRightElement
              h="-webkit-fill-available"
              justifyContent="flex-end"
              pr={2}
            >
              <SmallCloseIcon
                _hover={{ borderColor: 'gray.400', color: 'gray.400' }}
                cursor="pointer"
                borderRadius="full"
                border="1px solid"
                borderColor="gray.200"
                onClick={handleClear}
                color="gray.200"
              />
            </InputRightElement>
          )}
        </InputGroup>
        {isRequired && !selectedDate && (
          <FormErrorMessage m={0}>Date is required</FormErrorMessage>
        )}
      </Flex>
    </FormControl>
  );
};
