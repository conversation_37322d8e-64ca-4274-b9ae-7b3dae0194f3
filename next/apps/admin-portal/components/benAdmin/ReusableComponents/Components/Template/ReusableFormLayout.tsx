/**
 * ReusableFormLayout Component
 *
 * This component renders a section of a form with a title and a set of fields.
 * It supports both an editable mode (using react-hook-form controllers) and a
 * read-only mode. In editable mode, when the user clicks "Cancel", only the values
 * for that specific section are reset to their initial snapshot.
 *
 * Developer Notes:
 * - The component assumes that each "section" of the form is independent.
 *   When entering edit mode, we capture a snapshot (sectionInitialValues) of the
 *   current field values for that section.
 * - The Cancel button resets only the fields in this section, leaving other sections untouched.
 * - We explicitly check for empty values (null, undefined, or empty string) to avoid
 *   treating valid values like 0 as falsy.
 * - Make sure that the field configuration (TemplateFieldConfig) provides appropriate
 *   default values via the "value" property for read-only mode.
 */

import { EditIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Flex,
  FormLabel,
  Heading,
  Icon,
  SimpleGrid,
  Text,
} from '@chakra-ui/react';
import React, { useMemo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { PiWarningOctagonFill } from 'react-icons/pi';

import { TemplateFieldConfig } from '../../Models/types';
import { bopTemplateStyles } from '../../Styles/styles';
import FormFieldRenderer from '../Form/components/sharables/FormFieldRenderer';

interface LayoutProps {
  title?: string;
  fields: Partial<TemplateFieldConfig>[];
  columns: number;
  editable?: boolean;
  onSubmit?: (data: any) => void;
  formMethods: UseFormReturn<any>;
  autoSaveHandler?: (data: any) => void;
  tab?: string;
  onUpdateActiveItem?: (section: string, tab?: string) => void;
  section?: string;
  tabErrors?: string[];
}

/**
 * ReusableFormLayout Component
 *
 * This component renders a section of a form with a title and a set of fields.
 * It supports both an editable mode and a read-only mode.
 */
const ReusableFormLayout: React.FC<LayoutProps> = ({
  title,
  fields,
  columns,
  editable = false,
  onSubmit,
  formMethods,
  autoSaveHandler,
  tab,
  onUpdateActiveItem,
  section,
  tabErrors,
}) => {
  // Destructure react-hook-form methods from formMethods
  const { handleSubmit, getValues, setValue, watch } = formMethods;

  // State to track whether this section is in edit mode
  const [isEditing, setIsEditing] = useState(false);

  // This state holds the snapshot of the section's field values when editing begins.
  const [sectionInitialValues, setSectionInitialValues] = useState<
    Record<string, any>
  >({});

  if (!editable && isEditing) {
    setIsEditing(false);
  }

  // Check if this tab has errors
  const hasTabErrors = useMemo(() => {
    if (!tabErrors || !Array.isArray(tabErrors) || !tab) return false;
    return tabErrors.includes(tab);
  }, [tabErrors, tab]);

  // Toggle edit mode and capture current field values
  const toggleEditMode = () => {
    if (editable) {
      if (onUpdateActiveItem && tab && section) {
        onUpdateActiveItem(section, tab);
        return;
      }
      const currentSectionValues = updatedFields.reduce((acc, field) => {
        // Only process fields that have a name
        if (field.name) {
          // Capture current value for each field using its name
          acc[field.name] = getValues(field.name);
        }
        return acc;
      }, {} as Record<string, any>);
      setSectionInitialValues(currentSectionValues);
      setIsEditing(true);
    }
  };

  // Cancel handler: Reverts fields to the captured snapshot
  const handleCancel = () => {
    setIsEditing(false);
    // For each field in this section, reset its value to the snapshot
    Object.entries(sectionInitialValues).forEach(([fieldName, value]) => {
      setValue(fieldName, value, { shouldDirty: false, shouldValidate: false });
    });
  };

  // Helper function to get nested value from object using dot notation path
  const getNestedValue = (obj: any, path: string) => {
    return path.split('.').reduce((current, key) => {
      return current && typeof current === 'object' ? current[key] : undefined;
    }, obj);
  };

  // Watch form values to keep field configurations in sync
  const formValues = watch();

  // Create updated field configurations with current form values
  const updatedFields = React.useMemo(() => {
    return fields.map((field) => {
      if (field.name) {
        const currentValue = getNestedValue(formValues, field.name);
        return {
          ...field,
          value: currentValue !== undefined ? currentValue : field.value,
        };
      }
      return field;
    });
  }, [fields, formValues]);

  // Save handler: Updates values and exits edit mode
  const handleSave = (data: any) => {
    // Update the form values AND the snapshot for this section
    const newSectionValues = updatedFields.reduce((acc, field) => {
      if (field.name) {
        // Use the nested path to get the value from the form data
        const newValue = getNestedValue(data, field.name);

        // Update the actual form value using setValue
        setValue(field.name, newValue);

        // Update our snapshot for future cancel operations
        acc[field.name] = newValue;
      }
      return acc;
    }, {} as Record<string, any>);

    autoSaveHandler?.(data);

    setSectionInitialValues(newSectionValues);
    setIsEditing(false);
  };

  // Render a field wrapped in FormControl with styling
  const renderStyledField = (
    field: Partial<TemplateFieldConfig>,
    index: number
  ) => {
    // Skip rendering fields without a name or label
    if (!field.name || !field.label) {
      return null;
    }

    return (
      <Box
        key={index}
        display="flex"
        flexDirection={{ base: 'column', md: 'row' }}
        alignItems={{ base: 'stretch', md: 'center' }}
        border="0 solid gray.200"
        borderBottomWidth="1px"
        gap={0}
        minHeight="fit-content"
      >
        <Box
          flex={{ base: 'none', md: '0 0 35%' }}
          bg="gray.50"
          height="100%"
          color="gray.700"
          padding={{ base: 2, md: 3 }}
          mb={0}
          display="flex"
          alignItems="center"
          fontSize="sm"
          wordBreak="keep-all"
          whiteSpace="nowrap"
          overflow="hidden"
          textOverflow="ellipsis"
          minHeight={{ base: 'auto', md: 'fit-content' }}
        >
          <FormLabel
            mb={0}
            fontSize="inherit"
            noOfLines={1}
            overflow="hidden"
            textOverflow="ellipsis"
            whiteSpace="nowrap"
            sx={{
              fontSize: 'clamp(10px, 2vw, 14px)',
              minWidth: 0,
            }}
          >
            {field.label}
          </FormLabel>
        </Box>

        <Box
          flex={{ base: '1', md: '0 0 65%' }}
          padding={{ base: 2, md: 3 }}
          fontSize="sm"
          minWidth={0}
        >
          <FormFieldRenderer
            field={field as TemplateFieldConfig}
            formMethods={formMethods}
            readOnly={!isEditing || !editable}
            renderError={(error) =>
              error ? (
                <Text color="red.500" fontSize="sm">
                  {error.message}
                </Text>
              ) : null
            }
          />
        </Box>
      </Box>
    );
  };

  const renderTitleWithErrorIcon = useMemo(() => {
    if (!title) return null;

    return (
      <Flex align="center" gap={2}>
        {hasTabErrors && (
          <Icon
            as={PiWarningOctagonFill}
            mb={-10}
            color="red.500"
            fontSize="md"
          />
        )}
        <Heading {...bopTemplateStyles.title}>{title}</Heading>
      </Flex>
    );
  }, [title, hasTabErrors]);

  return (
    <Box {...bopTemplateStyles.container} width="100%">
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={4}
      >
        {renderTitleWithErrorIcon}
        {editable && !isEditing && (
          <Button
            {...bopTemplateStyles.editButton}
            onClick={toggleEditMode}
            leftIcon={<EditIcon />}
          >
            Edit Page
          </Button>
        )}
      </Box>

      <form onSubmit={handleSubmit(handleSave)} style={{ width: '100%' }}>
        <SimpleGrid
          columns={{ base: 1, md: Math.min(columns, 2), lg: columns }}
          spacing={0}
          width="100%"
        >
          {updatedFields?.map((field, index) =>
            renderStyledField(field, index)
          )}
        </SimpleGrid>

        {editable && isEditing && (
          <Box {...bopTemplateStyles.buttonContainer}>
            <Button
              {...bopTemplateStyles.saveButton}
              type="submit" // Changed to type="submit" to properly trigger form submission
              //   isDisabled={!isDirty}
            >
              Save
            </Button>
            <Button
              type="button"
              {...bopTemplateStyles.cancelButton}
              onClick={handleCancel}
            >
              Cancel
            </Button>
          </Box>
        )}
      </form>
    </Box>
  );
};

export default ReusableFormLayout;
