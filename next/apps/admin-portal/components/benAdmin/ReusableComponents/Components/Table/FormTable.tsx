// InlineEditTable.tsx
import {
  Box,
  Button,
  IconButton,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  <PERSON>dal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Portal,
  Table,
  TableContainer,
  Tbody,
  Td,
  Tfoot,
  Th,
  Thead,
  Tooltip,
  Tr,
  useDisclosure,
} from '@chakra-ui/react';
import { parseDateWithoutTimezone } from '@next/shared/hooks';
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';

import DropdownSelect from '../Form/components/DropdownSelect';
import ReusableInput from '../Form/components/Input';
import { DateControl } from '../Template/DateControl';

// Styles
const UNIFORM_COLUMN_WIDTH = '300px';
const ACTIONS_COLUMN_WIDTH = '60px';

// Function to generate table styles based on compact mode
const getTableStyles = (compact: boolean) => ({
  container: {
    overflow: 'visible' as const,
    border: '1px solid',
    borderColor: 'gray.200',
    borderRadius: 'md',
  },

  scrollableContainer: {
    overflowX: 'auto' as const,
    overflowY: 'visible' as const,
    display: 'flex' as const,
    flexDirection: 'column' as const,
  },

  headerContainer: {
    overflow: 'hidden' as const,
  },

  bodyContainer: {
    flex: 1,
    overflowY: 'auto' as const,
    overflowX: 'hidden' as const,
  },

  footerContainer: {
    overflow: 'hidden' as const,
  },

  table: {
    borderCollapse: 'separate' as const,
    borderSpacing: 0,
    tableLayout: 'fixed' as const,
  },

  headerCell: {
    width: UNIFORM_COLUMN_WIDTH,
    minWidth: UNIFORM_COLUMN_WIDTH,
    maxWidth: UNIFORM_COLUMN_WIDTH,
    textAlign: 'left' as const,
    paddingLeft: '8px',
    paddingRight: '8px',
    paddingTop: compact ? '8px' : '16px',  // More generous padding for headers
    paddingBottom: compact ? '8px' : '16px',  // More generous padding for headers
    backgroundColor: '#f4f8ff',
    color: '#57739f',
    fontWeight: 'bold',
    borderBottom: '1px solid',
    borderColor: 'gray.200',
    overflow: 'hidden' as const,
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap' as const,
    position: 'sticky' as const,
    top: 0,
    zIndex: 10,
    boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  },

  actionsHeaderCell: {
    width: ACTIONS_COLUMN_WIDTH,
    minWidth: ACTIONS_COLUMN_WIDTH,
    maxWidth: ACTIONS_COLUMN_WIDTH,
    textAlign: 'center' as const,
    paddingLeft: '8px',
    paddingRight: '8px',
    paddingTop: compact ? '8px' : '16px',  // More generous padding for headers
    paddingBottom: compact ? '8px' : '16px',  // More generous padding for headers
    backgroundColor: '#f4f8ff',
    color: '#57739f',
    fontWeight: 'bold',
    borderBottom: '1px solid',
    borderColor: 'gray.200',
    overflow: 'hidden' as const,
    position: 'sticky' as const,
    top: 0,
    zIndex: 10,
    boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  },

  bodyCell: {
    width: UNIFORM_COLUMN_WIDTH,
    minWidth: UNIFORM_COLUMN_WIDTH,
    maxWidth: UNIFORM_COLUMN_WIDTH,
    padding: 0,
    overflow: 'visible' as const,
    borderBottom: '1px solid',
    borderColor: 'gray.200',
    color: '#828282',
    position: 'relative' as const,
  },

  actionsBodyCell: {
    width: ACTIONS_COLUMN_WIDTH,
    minWidth: ACTIONS_COLUMN_WIDTH,
    maxWidth: ACTIONS_COLUMN_WIDTH,
    paddingLeft: '8px',
    paddingRight: '8px',
    paddingTop: compact ? '4px' : '16px',
    paddingBottom: compact ? '4px' : '16px',
    textAlign: 'center' as const,
    overflow: 'visible' as const,
    borderBottom: '1px solid',
    borderColor: 'gray.200',
    color: '#828282',
  },
});

const editStyles = {
  container: {
    width: '100%',
    position: 'relative' as const,
    zIndex: 10,
    onClick: (e: React.MouseEvent) => e.stopPropagation(),
  },

  selectContainer: {
    width: '100%',
    position: 'relative' as const,
    zIndex: 1000,
  },

  inputGroup: {
    size: 'md' as const,
  },

  selectInput: {
    readOnly: true,
    cursor: 'pointer' as const,
  },

  searchableInput: {
    cursor: 'text' as const,
  },
};

const getDisplayStyles = (compact: boolean) => ({
  cell: {
    cursor: 'pointer' as const,
    paddingLeft: '8px',
    paddingRight: '8px',
    paddingTop: compact ? '4px' : '16px',
    paddingBottom: compact ? '4px' : '16px',
    borderRadius: 'md',
    _hover: { bg: 'gray.50' },
    transition: 'background-color 0.2s',
    width: '100%',
    userSelect: 'none' as const,
    color: '#828282',
    overflow: 'hidden' as const,
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap' as const,
  },

  nonEditableCell: {
    cursor: 'default' as const,
    paddingLeft: '8px',
    paddingRight: '8px',
    paddingTop: compact ? '4px' : '16px',
    paddingBottom: compact ? '4px' : '16px',
    borderRadius: 'md',
    width: '100%',
    userSelect: 'none' as const,
    color: '#828282',
    overflow: 'hidden' as const,
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap' as const,
  },
});

const datePickerStyles = {
  container: {
    width: '100%',
  },

  props: {
    inModal: true,
    dateProps: {
      propsConfigs: {
        inputProps: {
          size: 'md' as const,
          autoFocus: true,
        },
        calendarPanelProps: {
          wrapperProps: {
            zIndex: 9999,
          },
        },
      },
    },
  },
};

// Types
export interface InlineEditColumn<T = any> {
  key: string;
  header: string;
  width?: string;
  editable?: boolean;
  editType?: 'text' | 'number' | 'date' | 'select';
  editOptions?: Record<string, string>;
  searchable?: boolean;
  placeholder?: string;
  format?: (value: any, row: T) => React.ReactNode;
  validate?: (value: any) => boolean;
  tooltip?: string; // Hover text for column header
}

interface RowAction<T = any> {
  label: string;
  onClick: (row: T, rowIndex: number) => void;
  isDisabled?: (row: T, rowIndex: number) => boolean;
  colorScheme?: string;
}

interface ValidationState {
  hasError: boolean;
  hasWarning: boolean;
  error?: {
    validation_message: string;
    rule_name: string;
  };
  warning?: {
    validation_message: string;
    rule_name: string;
  };
}

interface InlineEditTableProps<T = any> {
  data: T[];
  columns: InlineEditColumn<T>[];
  onCellUpdate: (rowIndex: number, key: string, value: any) => void;
  onAddNew?: () => void;
  onDeleteRow?: (rowIndex: number) => void;
  onDuplicateRow?: (rowIndex: number) => void;
  onEditInModal?: (rowIndex: number) => void;
  addNewButtonText?: string;
  showAddNew?: boolean;
  showRowActions?: boolean;
  customRowActions?: RowAction<Record<string, any>>[];
  rowKey?: (row: T, index: number) => string | number;
  tableStyles?: any;
  rowProps?: (
    row: T,
    index: number
  ) => React.HTMLAttributes<HTMLTableRowElement>;
  // Validation support
  getRowValidationState?: (rowIndex: number) => {
    hasErrors: boolean;
    hasWarnings: boolean;
  };
  getFieldValidationState?: (
    rowIndex: number,
    fieldKey: string
  ) => ValidationState;
  maxBodyHeight?: string;
  compact?: boolean; // Simple prop to make table rows less tall
}

// Three dots icon component
const ThreeDotsIcon = () => (
  <Box color="#828282">
    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
      <circle cx="8" cy="3" r="1.5" />
      <circle cx="8" cy="8" r="1.5" />
      <circle cx="8" cy="13" r="1.5" />
    </svg>
  </Box>
);

// Sort icons
const SortAscIcon = () => (
  <Box color="currentColor">
    <svg
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3 9 L6 3 L9 9"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </Box>
);

const SortDescIcon = () => (
  <Box color="currentColor">
    <svg
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3 3 L6 9 L9 3"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </Box>
);

// Row Actions Menu Component
const RowActionsMenu = memo(
  ({
    row,
    rowIndex,
    onDelete,
    onDuplicate,
    onEditInModal,
    customActions = [],
  }: {
    row: Record<string, any>;
    rowIndex: number;
    onDelete?: (rowIndex: number) => void;
    onDuplicate?: (rowIndex: number) => void;
    onEditInModal?: (rowIndex: number) => void;
    customActions?: RowAction<Record<string, any>>[];
  }) => {
    const defaultActions: RowAction<Record<string, any>>[] = [
      {
        label: 'Edit in Modal',
        onClick: () => onEditInModal?.(rowIndex),
        colorScheme: 'blue',
      },
      {
        label: 'Duplicate',
        onClick: () => onDuplicate?.(rowIndex),
        colorScheme: 'green',
      },
      {
        label: 'Delete',
        onClick: () => onDelete?.(rowIndex),
        colorScheme: 'red',
      },
    ];

    const allActions = [...customActions, ...defaultActions];

    return (
      <Menu>
        <MenuButton
          as={IconButton}
          icon={<ThreeDotsIcon />}
          variant="ghost"
          size="sm"
          aria-label="Row actions"
          _hover={{ bg: 'gray.100' }}
          onClick={(e) => e.stopPropagation()}
        />
        <Portal>
          <MenuList zIndex={9999}>
            {allActions.map((action, index) => (
              <MenuItem
                key={index}
                onClick={(e) => {
                  e.stopPropagation();
                  action.onClick(row, rowIndex);
                }}
                isDisabled={action.isDisabled?.(row, rowIndex)}
                color={
                  action.colorScheme ? `${action.colorScheme}.600` : undefined
                }
                _hover={{
                  bg: action.colorScheme
                    ? `${action.colorScheme}.50`
                    : 'gray.50',
                }}
              >
                {action.label}
              </MenuItem>
            ))}
          </MenuList>
        </Portal>
      </Menu>
    );
  }
);

RowActionsMenu.displayName = 'RowActionsMenu';

// Custom Scrollbar Component
const CustomScrollbar = memo(
  ({
    containerRef,
    onScroll,
  }: {
    containerRef: React.RefObject<HTMLDivElement>;
    onScroll?: () => void;
  }) => {
    const [scrollLeft, setScrollLeft] = useState(0);
    const [scrollWidth, setScrollWidth] = useState(0);
    const [clientWidth, setClientWidth] = useState(0);
    const [isDragging, setIsDragging] = useState(false);
    const scrollbarRef = useRef<HTMLDivElement>(null);

    // Update scroll dimensions
    const updateScrollDimensions = useCallback(() => {
      if (containerRef.current) {
        const container = containerRef.current;
        setScrollLeft(container.scrollLeft);
        setScrollWidth(container.scrollWidth);
        setClientWidth(container.clientWidth);
      }
    }, [containerRef]);

    // Listen to container scroll
    useEffect(() => {
      const container = containerRef.current;
      if (!container) return;

      const handleScroll = () => {
        updateScrollDimensions();
        onScroll?.();
      };

      container.addEventListener('scroll', handleScroll);
      // Initial update
      updateScrollDimensions();

      // Update on resize
      const resizeObserver = new ResizeObserver(updateScrollDimensions);
      resizeObserver.observe(container);

      return () => {
        container.removeEventListener('scroll', handleScroll);
        resizeObserver.disconnect();
      };
    }, [containerRef, onScroll, updateScrollDimensions]);

    // Calculate scrollbar dimensions
    const maxScroll = scrollWidth - clientWidth;
    const isScrollable = maxScroll > 0;
    const scrollPercentage = maxScroll > 0 ? scrollLeft / maxScroll : 0;
    const trackWidth = Math.max(300, clientWidth - 100); // Track width based on table width, min 300px
    const thumbWidth = Math.max(60, (clientWidth / scrollWidth) * trackWidth); // Min 60px thumb width
    const thumbLeft = scrollPercentage * (trackWidth - thumbWidth);

    // Handle arrow clicks
    const handleLeftArrow = () => {
      if (containerRef.current) {
        containerRef.current.scrollLeft = Math.max(0, scrollLeft - 100);
      }
    };

    const handleRightArrow = () => {
      if (containerRef.current) {
        containerRef.current.scrollLeft = Math.min(maxScroll, scrollLeft + 100);
      }
    };

    // Handle thumb dragging
    const handleMouseDown = (e: React.MouseEvent) => {
      e.preventDefault();
      setIsDragging(true);

      const scrollbar = scrollbarRef.current;
      if (!scrollbar) return;

      const startX = e.clientX;
      const startScrollLeft = scrollLeft;

      const handleMouseMove = (e: MouseEvent) => {
        const deltaX = e.clientX - startX;
        const maxThumbLeft = trackWidth - thumbWidth;
        const deltaPercent = deltaX / maxThumbLeft;
        const newScrollLeft = Math.max(
          0,
          Math.min(maxScroll, startScrollLeft + deltaPercent * maxScroll)
        );

        if (containerRef.current) {
          containerRef.current.scrollLeft = newScrollLeft;
        }
      };

      const handleMouseUp = () => {
        setIsDragging(false);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };

    if (!isScrollable) return null;

    return (
      <Box
        display="flex"
        alignItems="center"
        justifyContent="center"
        mt={3}
        px={4}
        gap={2}
        userSelect="none"
      >
        {/* Left Arrow */}
        <IconButton
          icon={
            <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
              <path
                d="M7.5 9L4.5 6L7.5 3"
                stroke="currentColor"
                strokeWidth="1.5"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          }
          aria-label="Scroll left"
          size="sm"
          variant="ghost"
          color="#d0d0d0"
          _hover={{ color: '#a0a0a0' }}
          onClick={handleLeftArrow}
          isDisabled={scrollLeft <= 0}
        />

        {/* Scrollbar Track */}
        <Box
          ref={scrollbarRef}
          position="relative"
          width={`${trackWidth}px`}
          height="12px"
          bg="#f5f5f5"
          borderRadius="6px"
          cursor="pointer"
          onClick={(e) => {
            const rect = e.currentTarget.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const clickPercent = clickX / trackWidth;
            const newScrollLeft = clickPercent * maxScroll;

            if (containerRef.current) {
              containerRef.current.scrollLeft = newScrollLeft;
            }
          }}
        >
          {/* Scrollbar Thumb */}
          <Box
            position="absolute"
            left={`${thumbLeft}px`}
            top="0"
            width={`${thumbWidth}px`}
            height="12px"
            bg={isDragging ? '#a0a0a0' : '#d0d0d0'}
            borderRadius="6px"
            cursor="grab"
            _active={{ cursor: 'grabbing' }}
            onMouseDown={handleMouseDown}
            transition="background-color 0.2s"
          />
        </Box>

        {/* Right Arrow */}
        <IconButton
          icon={
            <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
              <path
                d="M4.5 3L7.5 6L4.5 9"
                stroke="currentColor"
                strokeWidth="1.5"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          }
          aria-label="Scroll right"
          size="sm"
          variant="ghost"
          color="#d0d0d0"
          _hover={{ color: '#a0a0a0' }}
          onClick={handleRightArrow}
          isDisabled={scrollLeft >= maxScroll}
        />
      </Box>
    );
  }
);

CustomScrollbar.displayName = 'CustomScrollbar';
// Memoized Cell Component for performance
const EditableCell = memo(
  ({
    value,
    row,
    column,
    rowIndex,
    onUpdate,
    isEditing,
    onStartEdit,
    onEndEdit,
    getFieldValidationState,
    compact = false,
  }: {
    value: any;
    row: any;
    column: InlineEditColumn;
    rowIndex: number;
    onUpdate: (value: any) => void;
    isEditing: boolean;
    onStartEdit: () => void;
    onEndEdit: () => void;
    getFieldValidationState?: (
      rowIndex: number,
      fieldKey: string
    ) => ValidationState;
    compact?: boolean;
  }) => {
    const formMethods = useForm({
      defaultValues: { [column.key]: value },
      mode: 'onChange',
    });
    const inputRef = useRef<HTMLInputElement>(null);
    const dropdownSelectRef = useRef<HTMLDivElement>(null);

    // Update form value when prop changes or when entering edit mode
    useEffect(() => {
      formMethods.setValue(column.key, value);
    }, [value, isEditing, formMethods, column.key]);

    // Auto-focus when entering edit mode
    useEffect(() => {
      if (isEditing) {
        if (column.editType === 'select') {
          // For dropdowns, find the input inside and trigger click to open
          setTimeout(() => {
            const dropdownInput =
              dropdownSelectRef.current?.querySelector('input');
            if (dropdownInput) {
              dropdownInput.focus();
              dropdownInput.click();
            }
          }, 10);
        } else if (inputRef.current) {
          // For other inputs, focus and select
          setTimeout(() => {
            inputRef.current?.focus();
            inputRef.current?.select?.();
          }, 10);
        }
      }
    }, [isEditing, column.editType]);

    const handleUpdate = useCallback(
      (newValue: any) => {
        if (column.validate && !column.validate(newValue)) {
          formMethods.setValue(column.key, value); // Reset to original
        } else {
          onUpdate(newValue);
        }
        onEndEdit();
      },
      [value, onUpdate, onEndEdit, column, formMethods]
    );

    // Reset form when value prop changes (for new rows/cells)
    useEffect(() => {
      formMethods.reset({ [column.key]: value });
    }, [formMethods, column.key, value]);

    // Render edit mode
    if (isEditing && column.editable) {
      const commonProps = {
        name: column.key,
        placeholder: column.placeholder || '',
        value: formMethods.watch(column.key) || '',
        formMethods,
      };

      switch (column.editType) {
        case 'text':
        case 'number':
          return (
            <Box {...editStyles.container}>
              <ReusableInput
                {...commonProps}
                ref={inputRef}
                type={column.editType}
                onChange={(newValue) => {
                  // Only update form state, don't commit yet
                  formMethods.setValue(column.key, newValue);
                }}
                onBlur={() => {
                  // Commit the value on blur
                  const currentValue = formMethods.getValues(column.key);
                  handleUpdate(currentValue);
                }}
                customProps={{
                  autoFocus: true,
                  onKeyDown: (e: React.KeyboardEvent) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      const currentValue = formMethods.getValues(column.key);
                      handleUpdate(currentValue);
                    } else if (e.key === 'Escape') {
                      e.preventDefault();
                      formMethods.setValue(column.key, value); // Reset to original
                      onEndEdit();
                    }
                  },
                }}
              />
            </Box>
          );

        case 'date':
          return (
            <Box {...editStyles.container}>
              <DateControl
                name={column.key}
                placeholder={column.placeholder || 'Select date'}
                value={
                  value ? parseDateWithoutTimezone(String(value)) : undefined
                }
                onChange={(date) => {
                  const dateStr = date
                    ? date.toISOString().split('T')[0]
                    : null;
                  handleUpdate(dateStr);
                }}
                {...datePickerStyles.props}
              />
            </Box>
          );

        case 'select': {
          return (
            <Box
              {...editStyles.container}
              ref={dropdownSelectRef}
              sx={{
                zIndex: 1001,
                position: 'relative',
              }}
            >
              <DropdownSelect
                name={column.key}
                optionsMap={column.editOptions || {}}
                value={formMethods.watch(column.key) || ''}
                placeholder={column.placeholder || 'Select an option...'}
                allowSearch={column.searchable || false}
                onChange={(name, selectedKey) => {
                  const newValue = selectedKey === null ? '' : selectedKey;
                  formMethods.setValue(column.key, newValue);
                  handleUpdate(newValue);
                }}
                onBlur={() => {
                  // Optional: handle blur if needed
                }}
                formMethods={formMethods}
                isDisabled={false}
              />
            </Box>
          );
        }

        default:
          return null;
      }
    }

    // Get validation state for this cell
    const validationState = getFieldValidationState?.(rowIndex, column.key) || {
      hasError: false,
      hasWarning: false,
    };

    // Render display mode
    const displayValue = column.format
      ? column.format(value, row)
      : value || '--';

    // Get dynamic display styles based on compact mode
    const displayStyles = getDisplayStyles(compact);

    // Create custom styles based on validation state with background colors
    const baseCellStyles = column.editable
      ? displayStyles.cell
      : displayStyles.nonEditableCell;

    // Create custom styles with hover effect for validation backgrounds
    const containerStyles =
      validationState.hasError || validationState.hasWarning
        ? {
            ...baseCellStyles,
            _hover: {
              '& > .validation-bg': {
                backgroundColor: validationState.hasError
                  ? '#FEF5F5'
                  : '#FFFAF0', // Lighter red/orange for hover
              },
            },
          }
        : baseCellStyles;

    return (
      <Box
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          if (column.editable) {
            onStartEdit();
          }
        }}
        {...containerStyles}
        position="relative"
        display="flex"
        alignItems="center"
        width="100%"
        height="100%"
      >
        {/* Validation background overlay */}
        {(validationState.hasError || validationState.hasWarning) && (
          <Box
            className="validation-bg"
            position="absolute"
            top="0"
            left="4px" // Few pixels shorter on the left side
            right="4px" // Few pixels shorter on the right side
            bottom="0"
            backgroundColor={validationState.hasError ? '#FED7D7' : '#FEEBC8'} // Light red for errors, light orange for warnings
            borderRadius="0" // Sharp edges, no rounding
            zIndex={0}
          />
        )}

        {/* Content */}
        <Box
          position="relative"
          zIndex={1}
          width="100%"
          display="flex"
          alignItems="center"
          justifyContent="flex-start"
          title={
            validationState.hasError
              ? validationState.error?.validation_message
              : validationState.hasWarning
              ? validationState.warning?.validation_message
              : React.isValidElement(displayValue)
              ? undefined
              : String(displayValue)
          }
        >
          {React.isValidElement(displayValue)
            ? displayValue
            : String(displayValue)}
        </Box>
      </Box>
    );
  }
);

EditableCell.displayName = 'EditableCell';

// Main table component
export const InlineEditTable = <T extends Record<string, any>>({
  data,
  columns,
  onCellUpdate,
  onAddNew,
  onDeleteRow,
  onDuplicateRow,
  onEditInModal,
  addNewButtonText = 'Add New Row',
  showAddNew = true,
  showRowActions = true,
  customRowActions = [] as RowAction<Record<string, any>>[],
  rowKey = (_, index) => index,
  tableStyles: externalTableStyles = {},
  rowProps,
  getRowValidationState,
  getFieldValidationState,
  maxBodyHeight = '500px',
  compact = false,
}: InlineEditTableProps<T>) => {
  // Generate table styles based on compact mode
  const tableStyles = useMemo(() => getTableStyles(compact), [compact]);

  const [editingCell, setEditingCell] = useState<{
    row: number;
    col: string;
  } | null>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const bodyRef = useRef<HTMLDivElement>(null);
  const footerRef = useRef<HTMLDivElement>(null);

  // Add sorting states
  const [sortBy, setSortBy] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc' | null>(
    null
  );

  // Add state for delete confirmation modal
  const [rowToDelete, setRowToDelete] = useState<number | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const handleStartEdit = useCallback((rowIndex: number, columnKey: string) => {
    setEditingCell({ row: rowIndex, col: columnKey });
  }, []);

  const handleEndEdit = useCallback(() => {
    setEditingCell(null);
  }, []);

  const handleCellUpdate = useCallback(
    (rowIndex: number, columnKey: string, value: any) => {
      onCellUpdate(rowIndex, columnKey, value);
    },
    [onCellUpdate]
  );

  const handleAddNew = useCallback(() => {
    if (onAddNew) {
      onAddNew();
      // Optionally start editing the first editable cell of the new row
      const firstEditableColumn = columns.find((col) => col.editable);
      if (firstEditableColumn) {
        setTimeout(() => {
          setEditingCell({ row: data.length, col: firstEditableColumn.key });
        }, 100);
      }
    }
  }, [onAddNew, columns, data.length]);

  // Replace onDeleteRow handler to show modal
  const handleDeleteRow = useCallback(
    (rowIndex: number) => {
      setRowToDelete(rowIndex);
      onOpen();
    },
    [onOpen]
  );

  // Confirm delete
  const confirmDelete = useCallback(() => {
    if (rowToDelete !== null && onDeleteRow) {
      onDeleteRow(rowToDelete);
    }
    setRowToDelete(null);
    onClose();
  }, [rowToDelete, onDeleteRow, onClose]);

  // Cancel delete
  const cancelDelete = useCallback(() => {
    setRowToDelete(null);
    onClose();
  }, [onClose]);

  // Add sort click handler
  const handleSortClick = useCallback(
    (key: string) => {
      if (sortBy === key) {
        if (sortDirection === 'asc') {
          setSortDirection('desc');
        } else if (sortDirection === 'desc') {
          setSortDirection(null);
          setSortBy(null);
        } else {
          setSortDirection('asc');
        }
      } else {
        setSortBy(key);
        setSortDirection('asc');
      }
    },
    [sortBy, sortDirection]
  );

  // Add sorted indices
  const sortedIndices = Array.from({ length: data.length }, (_, i) => i);
  if (sortBy && sortDirection) {
    const column = columns.find((c) => c.key === sortBy);
    sortedIndices.sort((iA, iB) => {
      const valA = data[iA][sortBy] ?? '';
      const valB = data[iB][sortBy] ?? '';
      let cmp = 0;
      if (column?.editType === 'number') {
        cmp = (Number(valA) || 0) - (Number(valB) || 0);
      } else if (column?.editType === 'date') {
        const timeA = valA ? new Date(valA).getTime() : 0;
        const timeB = valB ? new Date(valB).getTime() : 0;
        cmp = timeA - timeB;
      } else if (typeof valA === 'string' && typeof valB === 'string') {
        cmp = valA.localeCompare(valB);
      } else {
        cmp = valA < valB ? -1 : valA > valB ? 1 : 0;
      }
      return sortDirection === 'asc' ? cmp : -cmp;
    });
  }

  useEffect(() => {
    const refs = [headerRef.current, bodyRef.current, footerRef.current].filter(
      Boolean
    ) as (HTMLDivElement | null)[];
    const syncWidths = () => {
      if (refs.length === 0) return;
      const maxWidth = Math.max(...refs.map((ref) => ref?.scrollWidth || 0));
      refs.forEach((ref) => {
        if (ref) ref.style.minWidth = `${maxWidth}px`;
      });
    };
    syncWidths();
    const resizeObserver = new ResizeObserver(syncWidths);
    refs.forEach(
      (ref: HTMLDivElement | null) => ref && resizeObserver.observe(ref)
    );
    return () => resizeObserver.disconnect();
  }, [data, columns, showAddNew, showRowActions]);

  return (
    <Box>
      {/* Confirmation Modal */}
      <Modal isOpen={isOpen} onClose={cancelDelete} isCentered>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Confirm Delete</ModalHeader>
          <ModalCloseButton />
          <ModalBody>Are you sure you want to delete this row?</ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={cancelDelete}>
              Cancel
            </Button>
            <Button colorScheme="red" onClick={confirmDelete}>
              Delete
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
      <TableContainer {...tableStyles.container}>
        <Box
          ref={scrollContainerRef}
          {...tableStyles.scrollableContainer}
          sx={{
            '&::-webkit-scrollbar': {
              display: 'none',
              width: 0,
              height: 0,
            },
          }}
        >
          <Box {...tableStyles.headerContainer} ref={headerRef}>
            <Table
              variant="simple"
              sx={{
                ...tableStyles.table,
                ...externalTableStyles,
              }}
            >
              <Thead>
                <Tr>
                  {showRowActions && (
                    <Th {...tableStyles.actionsHeaderCell}>
                      {/* Empty header for actions column */}
                    </Th>
                  )}
                  {columns.map((column) => (
                    <Th
                      key={column.key}
                      {...tableStyles.headerCell}
                      onClick={() => handleSortClick(column.key)}
                      cursor="pointer"
                      style={{
                        ...tableStyles.headerCell,
                        ...(column.width && {
                          width: column.width,
                          minWidth: column.width,
                          maxWidth: column.width,
                        }),
                      }}
                    >
                      <Box
                        display="flex"
                        alignItems="center"
                        gap={2}
                        userSelect="none"
                      >
                        {column.tooltip ? (
                          <Tooltip label={column.tooltip} hasArrow>
                            <Box>{column.header}</Box>
                          </Tooltip>
                        ) : (
                          column.header
                        )}
                        {sortBy === column.key && sortDirection && (
                          <Box color="#57739f">
                            {sortDirection === 'asc' ? (
                              <SortAscIcon />
                            ) : (
                              <SortDescIcon />
                            )}
                          </Box>
                        )}
                      </Box>
                    </Th>
                  ))}
                </Tr>
              </Thead>
            </Table>
          </Box>
          <Box
            {...tableStyles.bodyContainer}
            maxHeight={maxBodyHeight}
            ref={bodyRef}
          >
            <Table
              variant="simple"
              sx={{
                ...tableStyles.table,
                ...externalTableStyles,
              }}
            >
              <Tbody>
                {sortedIndices.map((originalRowIndex, displayIndex) => {
                  const row = data[originalRowIndex];
                  const isLastRow = displayIndex === sortedIndices.length - 1;
                  const rowValidation =
                    getRowValidationState?.(originalRowIndex);

                  const baseRowProps = rowProps
                    ? rowProps(row, originalRowIndex)
                    : {};
                  const validationRowProps = {
                    ...baseRowProps,
                    style: {
                      ...baseRowProps.style,
                      position: 'relative' as const,
                    },
                  };

                  return (
                    <Tr
                      key={rowKey(row, originalRowIndex)}
                      {...validationRowProps}
                    >
                      {showRowActions && (
                        <Td
                          {...tableStyles.actionsBodyCell}
                          borderBottom={
                            isLastRow && !showAddNew
                              ? 'none'
                              : tableStyles.actionsBodyCell.borderBottom
                          }
                          position="relative"
                        >
                          {(rowValidation?.hasErrors ||
                            rowValidation?.hasWarnings) && (
                            <Box
                              position="absolute"
                              left="0"
                              top="0"
                              bottom="0"
                              width="6px"
                              backgroundColor={
                                rowValidation.hasErrors ? '#E53E3E' : '#DD6B20'
                              }
                              zIndex={1}
                              pointerEvents="none"
                            />
                          )}
                          <RowActionsMenu
                            row={row}
                            rowIndex={originalRowIndex}
                            onDelete={handleDeleteRow}
                            onDuplicate={onDuplicateRow}
                            onEditInModal={onEditInModal}
                            customActions={customRowActions}
                          />
                        </Td>
                      )}
                      {columns.map((column, columnIndex) => (
                        <Td
                          key={column.key}
                          {...tableStyles.bodyCell}
                          borderBottom={
                            isLastRow && !showAddNew
                              ? 'none'
                              : tableStyles.bodyCell.borderBottom
                          }
                          position={
                            !showRowActions && columnIndex === 0
                              ? 'relative'
                              : undefined
                          }
                          style={{
                            ...tableStyles.bodyCell,
                            ...(column.width && {
                              width: column.width,
                              minWidth: column.width,
                              maxWidth: column.width,
                            }),
                          }}
                        >
                          {!showRowActions &&
                            columnIndex === 0 &&
                            (rowValidation?.hasErrors ||
                              rowValidation?.hasWarnings) && (
                              <Box
                                position="absolute"
                                left="0"
                                top="0"
                                bottom="0"
                                width="6px"
                                backgroundColor={
                                  rowValidation.hasErrors
                                    ? '#E53E3E'
                                    : '#DD6B20'
                                }
                                zIndex={1}
                                pointerEvents="none"
                              />
                            )}
                          <EditableCell
                            value={row[column.key]}
                            row={row}
                            column={column}
                            rowIndex={originalRowIndex}
                            onUpdate={(value) =>
                              handleCellUpdate(
                                originalRowIndex,
                                column.key,
                                value
                              )
                            }
                            isEditing={
                              editingCell?.row === originalRowIndex &&
                              editingCell?.col === column.key
                            }
                            onStartEdit={() =>
                              handleStartEdit(originalRowIndex, column.key)
                            }
                            onEndEdit={handleEndEdit}
                            getFieldValidationState={getFieldValidationState}
                            compact={compact}
                          />
                        </Td>
                      ))}
                    </Tr>
                  );
                })}
              </Tbody>
            </Table>
          </Box>
          {showAddNew && onAddNew && (
            <Box {...tableStyles.footerContainer} ref={footerRef}>
              <Table
                variant="simple"
                sx={{
                  ...tableStyles.table,
                  ...externalTableStyles,
                }}
              >
                <Tfoot>
                  <Tr>
                    <Td
                      colSpan={columns.length + (showRowActions ? 1 : 0)}
                      px={3}
                      py={5}
                      borderBottom="none"
                      position={'sticky' as const}
                      bottom={0}
                      bg="white"
                      zIndex={10}
                      borderTop="1px solid"
                      borderTopColor="gray.200"
                    >
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={handleAddNew}
                        color="#168aa5"
                        _hover={{
                          bg: '#168aa5',
                          color: 'white',
                        }}
                        _active={{
                          bg: '#0f6b7d',
                        }}
                      >
                        + Add New
                      </Button>
                    </Td>
                  </Tr>
                </Tfoot>
              </Table>
            </Box>
          )}
        </Box>
      </TableContainer>
      <CustomScrollbar containerRef={scrollContainerRef} />
    </Box>
  );
};
