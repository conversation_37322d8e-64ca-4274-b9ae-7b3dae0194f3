import {
  Box,
  FormControl,
  FormLabel,
  Input,
  InputGroup,
  InputLeftElement,
  Text,
  Textarea,
  VStack,
} from '@chakra-ui/react';
import React, { forwardRef } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { FieldError, FieldWarning } from '../../InlineFieldIcons';
import { AncillaryToggleAccordion } from './AncillaryToggleAccordion';
import { CircularQuestionIcon } from './sharables/CircularQuestionIcon';
import ToggleWithAccordion from './ToggleWithAccordion';

interface ReusableInputProps {
  label?: string;
  placeholder: string;
  isRequired?: boolean;
  infoText?: string;
  value?: string | number;
  error?: boolean;
  errorType?: string;
  onChange?: (value: string | number) => void;
  onBlur?: () => void;
  ref?: React.Ref<HTMLInputElement>;
  // Add type property for input type (important for number validation)
  type?: React.HTMLInputTypeAttribute | 'textarea';
  isDisabled?: boolean;
  showAncillaryToggle?: boolean;
  showToggle?: boolean;
  toggleLabel?: string;
  toggleName?: string;
  toggleValue?: boolean;
  onToggleChange?: (isChecked: boolean, isInitializing?: boolean) => void;
  formMethods: UseFormReturn<any>;
  formPath?: string;
  sync?: boolean;
  name: string;
  rows?: number;
  customProps?: Record<string, any>;
  idMap?: {
    key: string;
    value: number;
  } | null;
  syncFunction?: (
    sync: boolean,
    toggleValue: boolean,
    name: string,
    value: string | number,
    formMethods: UseFormReturn<any>
  ) => void;
}

const ReusableInput = forwardRef<HTMLInputElement, ReusableInputProps>(
  (
    {
      label,
      placeholder,
      isRequired = false,
      infoText,
      value = '',
      error,
      errorType,
      onChange,
      onBlur,
      type = 'text',
      isDisabled = false,
      showAncillaryToggle = false,
      showToggle = false,
      toggleLabel = 'Differs from Plan Design',
      toggleName,
      toggleValue = false,
      onToggleChange,
      formMethods,
      formPath,
      sync = false,
      name,
      rows = 5,
      customProps,
      idMap,
      syncFunction,
    },
    ref
  ) => {
    // Format value appropriately
    const displayValue = value === null ? '' : value;

    // Auto-resize textarea on initial render if needed
    React.useEffect(() => {
      // Skip auto-resize for non-textarea types
      if (type !== 'textarea') return;

      // Use a ref to target the specific textarea instead of querying all textareas
      const textareaRef = ref as React.RefObject<HTMLTextAreaElement>;
      const textareaElement = textareaRef?.current;

      if (
        textareaElement &&
        textareaElement.value === displayValue.toString() &&
        !!displayValue
      ) {
        textareaElement.style.height = 'auto';
        textareaElement.style.height = `${Math.max(
          120,
          textareaElement.scrollHeight
        )}px`;
      }
    }, [type, displayValue, ref]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;

      // Update the original input value
      onChange?.(newValue);

      // If sync is enabled and toggle is not active, sync across all plan designs
      if (syncFunction) {
        syncFunction(sync, toggleValue, name, newValue, formMethods);
      }
    };

    // Helper function to get max length from validation schema or direct value
    const getMaxLength = () => {
      if (type === 'textarea') {
        // First check for direct maxLength prop
        if (
          customProps?.maxLength &&
          typeof customProps.maxLength === 'number'
        ) {
          return customProps.maxLength;
        }

        // Then check for Zod validation schema
        if (customProps?.validations) {
          let maxLength;

          // Check if it's a Zod schema
          if (customProps.validations._def) {
            // Navigate through Zod's internal structure to find max check
            const checks = customProps.validations._def.checks || [];
            const maxCheck = checks.find((check: any) => check.kind === 'max');
            maxLength = maxCheck?.value;
          }

          if (maxLength) {
            return maxLength;
          }
        }
      }
      return undefined;
    };

    // Handle textarea change with proper character limiting
    const handleTextareaChange = (
      e: React.ChangeEvent<HTMLTextAreaElement>
    ) => {
      const newValue = e.target.value;
      const maxLength = getMaxLength();

      // Check character limit
      if (maxLength && newValue.length > maxLength) {
        // Don't update if it exceeds max length - this creates the hard stop
        return;
      }

      // Update the value
      onChange?.(newValue);

      // If sync is enabled and toggle is not active, sync across all plan designs
      if (syncFunction) {
        syncFunction(sync, toggleValue, name, newValue, formMethods);
      }

      // Auto-resize textarea based on content
      e.target.style.height = 'auto';
      e.target.style.height = `${Math.max(120, e.target.scrollHeight)}px`;
    };

    // Handle paste with proper character limiting
    const handleTextareaPaste = (
      e: React.ClipboardEvent<HTMLTextAreaElement>
    ) => {
      const maxLength = getMaxLength();
      if (!maxLength) return;

      const currentValue = e.currentTarget.value;
      const selectionStart = e.currentTarget.selectionStart || 0;
      const selectionEnd = e.currentTarget.selectionEnd || 0;
      const pastedText = e.clipboardData.getData('text');

      // Calculate what the new value would be
      const beforeSelection = currentValue.substring(0, selectionStart);
      const afterSelection = currentValue.substring(selectionEnd);
      const potentialNewValue = beforeSelection + pastedText + afterSelection;

      // If the new value would exceed the limit, handle it
      if (potentialNewValue.length > maxLength) {
        e.preventDefault();

        // Calculate how much space is available
        const selectedTextLength = selectionEnd - selectionStart;
        const availableSpace =
          maxLength - currentValue.length + selectedTextLength;

        if (availableSpace > 0) {
          // Truncate the pasted text to fit
          const truncatedPaste = pastedText.substring(0, availableSpace);
          const finalValue = beforeSelection + truncatedPaste + afterSelection;

          // Update the value through onChange
          onChange?.(finalValue);

          // If sync is enabled, sync across all plan designs
          if (syncFunction) {
            syncFunction(sync, toggleValue, name, finalValue, formMethods);
          }

          // Store reference to textarea element before setTimeout
          const textareaElement = e.currentTarget;

          // Set cursor position after the paste
          setTimeout(() => {
            if (textareaElement) {
              const newCursorPos = selectionStart + truncatedPaste.length;
              textareaElement.setSelectionRange(newCursorPos, newCursorPos);

              // Auto-resize
              textareaElement.style.height = 'auto';
              textareaElement.style.height = `${Math.max(
                120,
                textareaElement.scrollHeight
              )}px`;
            }
          }, 0);
        }
      }
    };

    return (
      <FormControl isRequired={isRequired}>
        <VStack spacing={2} align="stretch" width="100%">
          <Box>
            <Box display="flex" alignItems="flex-start" mb={1}>
              {label && (
                <FormLabel
                  color="#676767"
                  mb={0}
                  noOfLines={1}
                  overflow="hidden"
                  textOverflow="ellipsis"
                  whiteSpace="nowrap"
                  sx={{
                    fontSize: 'clamp(10px, 2vw, 14px)',
                    minWidth: 0,
                  }}
                >
                  {label}
                </FormLabel>
              )}
              {infoText && (
                <CircularQuestionIcon tooltiptext={infoText} ml={-2} />
              )}
              {/* Character counter for textarea - moved to label line */}
              {type === 'textarea' && getMaxLength() && (
                <Text
                  fontSize="xs"
                  color={
                    (displayValue as string)?.length >= getMaxLength()!
                      ? 'red.500'
                      : 'gray.500'
                  }
                  ml="auto"
                  mt={1}
                >
                  {(displayValue as string)?.length || 0} / {getMaxLength()}
                </Text>
              )}
            </Box>
            <InputGroup position="relative">
              {error && (
                <InputLeftElement
                  h="100%"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  pointerEvents="none"
                >
                  {(errorType === 'zodValidation' ||
                    errorType === 'required' ||
                    errorType === 'field-error') &&
                    FieldError()}
                  {errorType === 'field-warning' && FieldWarning()}
                </InputLeftElement>
              )}
              {type === 'textarea' ? (
                <Textarea
                  pl={error ? '32px' : ''}
                  placeholder={placeholder}
                  value={toggleValue ? '' : displayValue}
                  onChange={handleTextareaChange}
                  onPaste={handleTextareaPaste}
                  onBlur={onBlur}
                  isDisabled={isDisabled || toggleValue}
                  isInvalid={error}
                  errorBorderColor="red.500"
                  borderColor={error ? 'red.500' : 'gray.300'}
                  rows={rows || 5}
                  minHeight="120px"
                  resize="none"
                  overflow="hidden"
                  _focus={{
                    borderColor: 'blue.500',
                    boxShadow: '0 0 0 1px #3182ce',
                  }}
                  {...(customProps || {})}
                />
              ) : (
                <Input
                  pl={error ? '32px' : ''}
                  ref={ref}
                  placeholder={placeholder}
                  value={toggleValue ? '' : displayValue}
                  type={type}
                  onChange={handleInputChange}
                  onBlur={onBlur}
                  isDisabled={isDisabled || toggleValue}
                  isInvalid={error}
                  errorBorderColor="red.500"
                  borderColor={error ? 'red.500' : 'gray.300'}
                  _focus={{
                    borderColor: 'blue.500',
                    boxShadow: '0 0 0 1px #3182ce',
                  }}
                />
              )}
            </InputGroup>
          </Box>

          {/* Render toggle with accordion if showToggle is true */}

          {showAncillaryToggle && (
            <AncillaryToggleAccordion
              label={toggleLabel}
              isChecked={toggleValue}
              onChange={(isChecked, isInitializing) =>
                onToggleChange?.(isChecked, isInitializing)
              }
              name={toggleName}
              formMethods={formMethods}
              formPath={formPath}
            />
          )}

          {showToggle && (
            <ToggleWithAccordion
              label={toggleLabel}
              isChecked={toggleValue}
              onChange={(isChecked, isInitializing) =>
                onToggleChange?.(isChecked, isInitializing)
              }
              name={toggleName}
              formMethods={formMethods}
              formPath={formPath}
              idMap={idMap}
            />
          )}
        </VStack>
      </FormControl>
    );
  }
);

ReusableInput.displayName = 'ReusableInput';

export const ReusableTextarea = ReusableInput;
export default ReusableInput;
