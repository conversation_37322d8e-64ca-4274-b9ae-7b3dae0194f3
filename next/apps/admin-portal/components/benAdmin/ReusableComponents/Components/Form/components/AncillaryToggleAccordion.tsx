import { ChevronDownIcon } from '@chakra-ui/icons';
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Button,
  Checkbox,
  Flex,
  HStack,
  Input,
  Select,
  Text,
  VStack,
} from '@chakra-ui/react';
import { parseDateWithoutTimezone } from '@next/shared/hooks';
import React, { useEffect, useState } from 'react';
import { UseFormReturn, useWatch } from 'react-hook-form';

import { formatDate } from '../../Table/utils/tableUtils';
import { DateControl } from '../../Template/DateControl';
import * as styles from './types/styles';

export const AncillaryToggleAccordion = ({
  label,
  isChecked,
  onChange,
  name,
  isDisabled = false,
  optionsMap = {},
  formMethods,
  formPath,
}: {
  label: string;
  isChecked: boolean;
  onChange: (isChecked: boolean, isInitializing?: boolean) => void;
  name?: string;
  isDisabled?: boolean;
  optionsMap?: { [key: string | number]: string };
  formMethods: UseFormReturn<any>;
  formPath?: string;
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isAccordionExpanded, setIsAccordionExpanded] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  const getAncillaryPath = (planDesignIndex: number) => {
    if (!name) return '';
    return name
      ?.split('_differs')[0]
      .replace(
        /(\d+)(?=\.plan_design_ancillaries)/,
        planDesignIndex.toString()
      );
  };

  // Check if values differ across plan designs for ancillary fields
  const checkIfValuesDiffer = () => {
    if (!name || !formPath) {
      return false;
    }

    const planDesigns = formMethods.getValues(formPath);
    if (!Array.isArray(planDesigns) || planDesigns.length <= 1) {
      return false;
    }

    let firstValue: any = null;
    let valuesFound = false;
    const values: any[] = [];

    for (let i = 0; i < planDesigns.length; i++) {
      const valuePath = getAncillaryPath(i);
      const currentValue = formMethods.getValues(valuePath);
      values.push({ planDesignIndex: i, value: currentValue });

      if (!valuesFound && currentValue !== undefined) {
        firstValue = currentValue;
        valuesFound = true;
      } else if (currentValue !== firstValue && currentValue !== undefined) {
        return true;
      }
    }

    return false;
  };

  // Initialize checkbox state based on value comparison
  useEffect(() => {
    if (!hasInitialized && formPath && name) {
      const valuesDiffer = checkIfValuesDiffer();

      if (valuesDiffer && !isChecked) {
        onChange(true, true); // Pass isInitializing=true
      } else if (!valuesDiffer && isChecked) {
        onChange(false, true); // Pass isInitializing=true
      }
      setHasInitialized(true);
    }
  }, [hasInitialized, name, formPath]);

  const toggleAccordion = () => {
    if (isChecked) {
      setIsAccordionExpanded(!isAccordionExpanded);
    }
  };

  const totalItems = Object.keys(optionsMap).length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handleItemsPerPageChange = (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const newItemsPerPage = parseInt(event.target.value);
    setItemsPerPage(newItemsPerPage === 0 ? totalItems : newItemsPerPage);
    setCurrentPage(1);
  };

  const renderNavButton = (direction: 'prev' | 'next') => {
    const isPrev = direction === 'prev';
    return (
      <Button
        onClick={() =>
          handlePageChange(isPrev ? currentPage - 1 : currentPage + 1)
        }
        isDisabled={isPrev ? currentPage === 1 : currentPage === totalPages}
        aria-label={isPrev ? 'Previous page' : 'Next page'}
        variant="unstyled"
        display="flex"
        alignItems="center"
        justifyContent="center"
        sx={styles.navButtonStyles}
      >
        <svg width="15" height="15" viewBox="0 0 24 24" fill="none">
          <path
            d={isPrev ? 'M19 12H5' : 'M5 12H19'}
            stroke="#01874e"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d={isPrev ? 'M12 19L5 12L12 5' : 'M12 5L19 12L12 19'}
            stroke="#01874e"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </Button>
    );
  };

  const watchedObjects = useWatch({
    control: formMethods.control,
    name: formPath || '',
    defaultValue: [],
  });

  const handleCheckboxChange = (
    objectIndex: number,
    accordionKey: any,
    isChecked: boolean
  ) => {
    const currentValuePath = getAncillaryPath(objectIndex);
    const newValue = isChecked ? accordionKey : null;

    formMethods.setValue(currentValuePath, newValue, {
      shouldValidate: true,
      shouldDirty: true,
      shouldTouch: true,
    });
    formMethods.trigger(currentValuePath);
  };

  const isValueSelected = (objectIndex: number, accordionKey: string) => {
    if (!name) return false;

    const currentValuePath = getAncillaryPath(objectIndex);
    const value = formMethods.watch(currentValuePath);
    return String(value) === String(accordionKey);
  };

  const handleMainCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newCheckedValue = e.target.checked;

    if (!isDisabled) {
      onChange(newCheckedValue, false); // Pass isInitializing=false for user actions

      if (newCheckedValue) {
        setIsAccordionExpanded(true);
      }
    }
  };

  return (
    <Box width="100%">
      <Box sx={styles.containerStyles}>
        {/* Header */}
        <Flex
          sx={styles.headerStyles}
          bg={isChecked ? '#f8fdff' : 'white'}
          cursor={isChecked ? 'pointer' : 'default'}
          onClick={isChecked ? toggleAccordion : undefined}
        >
          <HStack spacing={4}>
            <Checkbox
              isChecked={isChecked}
              onChange={handleMainCheckboxChange}
              isDisabled={isDisabled}
              colorScheme="custom"
              onClick={(e) => {
                e.stopPropagation();
              }}
              sx={styles.checkboxStyles}
            >
              <Text sx={styles.headerTextStyles}>{label}</Text>
            </Checkbox>
          </HStack>
          {isChecked && (
            <ChevronDownIcon
              sx={styles.chevronIconStyles(isAccordionExpanded)}
            />
          )}
        </Flex>

        {/* Content - only show when checked and expanded */}
        {isChecked && isAccordionExpanded && (
          <>
            {/* Scrollable accordion container */}
            <Box sx={styles.accordionListContainerStyles}>
              <Accordion allowToggle sx={styles.accordionContainerStyles}>
                {optionsMap && Object.keys(optionsMap).length > 0
                  ? // Render dropdown options
                    Object.entries(optionsMap)
                      .slice(startIndex, endIndex)
                      .map(([accordionKey, value], accordionIndex) => (
                        <AccordionItem
                          key={accordionKey}
                          sx={styles.accordionItemStyles}
                          borderBottom={
                            accordionIndex !==
                            Math.min(
                              itemsPerPage,
                              Object.keys(optionsMap).slice(
                                startIndex,
                                endIndex
                              ).length
                            ) -
                              1
                              ? '1px solid'
                              : 'none'
                          }
                          borderColor="gray.200"
                        >
                          <AccordionButton sx={styles.accordionButtonStyles}>
                            <Box sx={styles.accordionButtonTextStyles}>
                              {value}
                            </Box>
                            <AccordionIcon />
                          </AccordionButton>
                          <AccordionPanel sx={styles.accordionPanelStyles}>
                            {formPath && (
                              <Box sx={styles.checkboxContainerStyles}>
                                <VStack sx={styles.checkboxListStyles}>
                                  {Array.isArray(watchedObjects) &&
                                    watchedObjects.map((obj: any, index) => {
                                      const displayValue =
                                        typeof obj === 'object'
                                          ? obj.name ||
                                            obj.label ||
                                            obj.id ||
                                            String(index + 1)
                                          : String(obj);
                                      const isSelected = isValueSelected(
                                        index,
                                        accordionKey
                                      );

                                      return (
                                        <Checkbox
                                          key={`${accordionKey}-${index}`}
                                          isChecked={isSelected}
                                          onChange={(e) =>
                                            handleCheckboxChange(
                                              index,
                                              accordionKey,
                                              e.target.checked
                                            )
                                          }
                                          colorScheme="custom"
                                          sx={{
                                            ...styles.checkboxStyles,
                                            ...styles.checkboxItemStyles(
                                              isSelected
                                            ),
                                          }}
                                        >
                                          {displayValue}
                                        </Checkbox>
                                      );
                                    })}
                                </VStack>
                              </Box>
                            )}
                          </AccordionPanel>
                        </AccordionItem>
                      ))
                  : // Render plan designs as accordions with input fields
                    Array.isArray(watchedObjects) &&
                    watchedObjects.map((obj: any, index) => {
                      const displayValue =
                        typeof obj === 'object'
                          ? obj.name || obj.label || obj.id || String(index + 1)
                          : String(obj);
                      return (
                        <AccordionItem
                          key={`plan-design-${index}`}
                          sx={styles.accordionItemStyles}
                          borderBottom={
                            index !== watchedObjects.length - 1
                              ? '1px solid'
                              : 'none'
                          }
                          borderColor="gray.200"
                        >
                          <AccordionButton sx={styles.accordionButtonStyles}>
                            <Box sx={styles.accordionButtonTextStyles}>
                              {displayValue}
                            </Box>
                            <AccordionIcon />
                          </AccordionButton>
                          <AccordionPanel sx={styles.accordionPanelStyles}>
                            {formPath && (
                              <Box sx={styles.checkboxContainerStyles}>
                                {name?.includes('effective_date') ||
                                name?.includes('expiration_date') ? (
                                  <DateControl
                                    showClearButton
                                    value={
                                      formMethods.watch(getAncillaryPath(index))
                                        ? parseDateWithoutTimezone(
                                            String(
                                              formMethods.watch(
                                                getAncillaryPath(index)
                                              )
                                            )
                                          )
                                        : undefined
                                    }
                                    clearAction={() => {
                                      formMethods.setValue(
                                        getAncillaryPath(index),
                                        null,
                                        {
                                          shouldValidate: true,
                                          shouldDirty: true,
                                          shouldTouch: true,
                                        }
                                      );
                                    }}
                                    onChange={(date) => {
                                      const formattedDate = formatDate(
                                        date,
                                        true
                                      );
                                      formMethods.setValue(
                                        getAncillaryPath(index),
                                        formattedDate,
                                        {
                                          shouldValidate: true,
                                          shouldDirty: true,
                                          shouldTouch: true,
                                        }
                                      );
                                    }}
                                  />
                                ) : (
                                  <Input
                                    placeholder="Enter value"
                                    value={
                                      formMethods.watch(
                                        getAncillaryPath(index)
                                      ) || ''
                                    }
                                    onChange={(e) => {
                                      formMethods.setValue(
                                        getAncillaryPath(index),
                                        e.target.value,
                                        {
                                          shouldValidate: true,
                                          shouldDirty: true,
                                          shouldTouch: true,
                                        }
                                      );
                                    }}
                                  />
                                )}
                              </Box>
                            )}
                          </AccordionPanel>
                        </AccordionItem>
                      );
                    })}
              </Accordion>
            </Box>

            {/* Pagination footer - only show for dropdown */}
            {optionsMap && Object.keys(optionsMap).length > 0 && (
              <Box sx={styles.paginationContainerStyles}>
                <Box sx={styles.paginationBoxStyles}>
                  <Flex sx={styles.paginationFlexStyles}>
                    {renderNavButton('prev')}
                    <VStack spacing={1}>
                      <Text color="gray.600" fontSize="sm" fontWeight="medium">
                        Page {currentPage}/{totalPages}
                      </Text>
                      <Box position="relative">
                        <Select
                          value={itemsPerPage}
                          onChange={handleItemsPerPageChange}
                          icon={<ChevronDownIcon />}
                          variant="filled"
                          size="sm"
                          width="180px"
                          bg="#f0f0f0"
                          _hover={{ bg: '#e8e8e8' }}
                        >
                          <option value={0}>Show All</option>
                          <option value={50}>Show 50 Per Page</option>
                          <option value={20}>Show 20 Per Page</option>
                          <option value={10}>Show 10 Per Page</option>
                        </Select>
                      </Box>
                    </VStack>
                    {renderNavButton('next')}
                  </Flex>
                </Box>
              </Box>
            )}
          </>
        )}
      </Box>
    </Box>
  );
};
