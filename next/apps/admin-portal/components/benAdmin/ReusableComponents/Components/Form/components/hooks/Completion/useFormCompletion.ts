/**
 * @fileoverview Form Completion Tracking System
 *
 * This module calculates and tracks completion percentages for complex form structures.
 * It handles various section types and maintains an overall completion status.
 *
 * Key Features:
 * - Tracks completion across nested form structures with subcategories
 * - Handles collection-based sections (arrays of items)
 * - Supports inline field groups
 * - Updates form data with completion percentages
 * - Maintains "last updated" information
 * - Provides overall lastUpdated status for the entire form section
 *
 * Performance Considerations:
 * - Memoized calculations to prevent unnecessary recalculations
 * - Optimized change detection to minimize form updates
 * - Safe nested property access with error handling
 */

import { useUser } from '@auth0/nextjs-auth0/client';
import {
  FieldConfig,
  InlineGroupFieldConfig,
  SubCategoryType,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { FormSections } from './types';

// ==================== TYPE GUARDS & UTILITIES ====================

/**
 * Type guard to check if a field is an inline group
 */
const isInlineGroup = (field: FieldConfig): field is InlineGroupFieldConfig => {
  return field.type === 'inlineGroup';
};

/**
 * Safely retrieves nested data from an object using a dot notation path
 * @param obj - Source object to traverse
 * @param path - Dot-notation path (e.g., "user.profile.name")
 * @returns The value at the specified path or undefined if not found
 */
const getNestedValue = (obj: any, path: string): any => {
  if (!obj || !path) return undefined;

  try {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  } catch (error) {
    console.error('Error accessing nested value:', error);
    return undefined;
  }
};

/**
 * Extracts field names from subcategories for completion tracking
 * @param subcategories - Array of subcategories to process
 * @returns Array of field names
 */
const extractFieldNamesFromSubcategories = (
  subcategories: SubCategoryType<any>[]
): string[] => {
  const fieldNames: string[] = [];

  subcategories.forEach((subcategory) => {
    subcategory.fields.forEach((field) => {
      if (isInlineGroup(field)) {
        // Process fields within inline groups
        field.fields.forEach((innerField) => {
          if ('name' in innerField) {
            fieldNames.push(innerField.name);
          }
        });
      } else if ('name' in field) {
        // Regular field
        fieldNames.push(field.name);
      }
    });
  });

  return fieldNames;
};

/**
 * Calculates the completion percentage for an item based on filled fields
 * @param item - Object to calculate completion for
 * @param excludeFields - Array of field names to exclude from calculation (default: ['lastUpdated'])
 * @returns Percentage of fields that have values (0-100)
 */
const calculateItemCompletionPercentage = (
  item: any,
  excludeFields: string[] = ['lastUpdated']
): number => {
  if (!item || typeof item !== 'object') return 0;

  // Get all fields except excluded ones
  const validFields = Object.keys(item).filter(
    (key) => !excludeFields.includes(key)
  );

  if (validFields.length === 0) return 0;

  // Count fields with values
  const filledFields = validFields.filter((key) => {
    const value = item[key];
    return value !== undefined && value !== null && value !== '';
  });

  // Calculate percentage
  return Math.round((filledFields.length / validFields.length) * 100);
};

// ==================== EXPORTED UTILITY FUNCTIONS ====================

/**
 * Updates completion percentage in form data for a major section
 * Only updates if the percentage has changed to avoid unnecessary renders
 *
 * @param formMethods - React Hook Form methods
 * @param percentage - Completion percentage (0-100)
 * @param formSections - Form sections configuration
 */
export const updateFormCompletion = (
  formMethods: UseFormReturn<any> | undefined,
  percentage: number,
  formSections?: FormSections
): void => {
  if (!formMethods || !formSections?.id) return;

  const sectionId = formSections.id;

  // Ensure percentages object exists
  const currentValues = formMethods.getValues();
  if (!currentValues.percentages) {
    formMethods.setValue('percentages', {}, { shouldDirty: false });
  }

  // Get current percentage and only update if changed
  const currentPercentage = formMethods.getValues(`percentages.${sectionId}`);
  if (currentPercentage !== percentage) {
    formMethods.setValue(`percentages.${sectionId}`, percentage, {
      shouldDirty: false,
    });
  }
};

/**
 * Gets completion percentage from form data
 *
 * @param formMethods - React Hook Form methods
 * @param sectionId - Major section identifier
 * @returns Completion percentage (0-100)
 */
export const getFormCompletionStatus = (
  formMethods: UseFormReturn<any> | undefined,
  sectionId: string
): number => {
  if (!formMethods || !sectionId) return 0;

  try {
    const percentage = formMethods.getValues(`percentages.${sectionId}`);
    return typeof percentage === 'number' ? percentage : 0;
  } catch (error) {
    console.error(`Error getting completion for ${sectionId}:`, error);
    return 0;
  }
};

// ==================== MAIN HOOK ====================

/**
 * Hook to calculate and track section completion within form data
 *
 * This hook analyzes form data to determine completion percentages for each section
 * and calculates an overall completion percentage for the major section.
 * It also tracks and provides the most recent update information.
 *
 * @param formMethods - React Hook Form methods
 * @param formSections - Configuration for form sections
 * @param userName - Default user name for tracking who updated the section
 * @returns Object with sectionStatus array, overallCompletion percentage, and lastUpdated info
 */
export const useFormCompletionTracking = (
  formMethods?: UseFormReturn<any>,
  formSections?: FormSections
) => {
  // State to track the last updated information for the entire form section
  const { user } = useUser();

  const userName = String(user?.given_name) || 'Unknown User';

  // Reference to track previous completion percentage
  const prevCompletionRef = useRef<number>(0);

  // Extract field names to watch for changes
  const fieldsToWatch = useMemo(() => {
    if (!formSections?.sections?.length) return [];

    const fields: string[] = [];

    formSections.sections.forEach((section) => {
      if (section.path) {
        fields.push(section.path);
      }
      if (section.relatedSections) {
        const sectionFields = extractFieldNamesFromSubcategories(
          section.relatedSections
        );
        fields.push(...sectionFields);
      }
    });

    // Also watch the percentages field
    fields.push('percentages');

    return fields;
  }, [formSections]);

  // Watch only specific fields instead of the entire form
  const watchedValues = formMethods?.watch(fieldsToWatch);

  /**
   * Creates a timestamp object for tracking when items are modified
   */
  const createTimestamp = useCallback(
    () => ({
      by: userName,
      at: new Date().toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      }),
    }),
    [userName]
  );

  const [lastUpdated, setLastUpdated] = useState<
    | {
        by: string;
        at: string;
      }
    | undefined
  >(undefined);

  /**
   * Checks if a field has a valid value (not undefined, null, or empty string)
   */
  const checkFieldHasValue = useCallback(
    (name: string): boolean => {
      if (!formMethods) return false;

      try {
        const fieldValue = formMethods.getValues(name);
        return (
          fieldValue !== undefined && fieldValue !== null && fieldValue !== ''
        );
      } catch (error) {
        return false;
      }
    },
    [formMethods]
  );

  /**
   * Calculates completion percentage for a section with fields
   * @param fieldNames - Array of field names to check
   * @returns Object with completionPercentage and isComplete status
   */
  const calculateFieldsCompletion = useCallback(
    (
      fieldNames: string[]
    ): {
      completionPercentage: number;
      isComplete: boolean;
    } => {
      if (!fieldNames.length)
        return { completionPercentage: 0, isComplete: false };

      let filledFields = 0;
      fieldNames.forEach((fieldName) => {
        if (checkFieldHasValue(fieldName)) {
          filledFields++;
        }
      });

      const totalFields = fieldNames.length;
      const completionPercentage =
        totalFields > 0 ? Math.round((filledFields / totalFields) * 100) : 0;

      return {
        completionPercentage,
        isComplete: totalFields > 0 && filledFields === totalFields,
      };
    },
    [checkFieldHasValue]
  );

  // Calculate completion status for all sections
  const sectionStatusWithPercentages = useMemo(() => {
    if (!formMethods || !formSections?.sections?.length) return [];

    const formValues = formMethods.getValues();

    return formSections.sections.map((section) => {
      let isComplete = false;
      let completionPercentage = 0;
      let sectionLastUpdated = createTimestamp();
      // Get section data if a path is provided
      const sectionData = section.path
        ? getNestedValue(formValues, section.path)
        : undefined;

      // Check if sectionData is an array
      if (Array.isArray(sectionData) && sectionData.length > 0) {
        // Get the last object in the array and use its lastUpdated property
        const lastItem = sectionData[sectionData.length - 1];

        if (lastItem?.lastUpdated) {
          sectionLastUpdated = lastItem.lastUpdated;
        }
      }
      // If not an array, check if it has lastUpdated directly
      else if (sectionData?.lastUpdated) {
        sectionLastUpdated = sectionData.lastUpdated;
      }

      // CASE 1: Collection sections (arrays)
      if (section.isCollection) {
        if (Array.isArray(sectionData) && sectionData.length > 0) {
          // Calculate field-based completion for each item in the collection
          const itemCompletions = sectionData.map((item) =>
            calculateItemCompletionPercentage(item, ['lastUpdated'])
          );

          // Calculate average completion across all items
          const totalCompletion = itemCompletions.reduce(
            (sum, percentage) => sum + percentage,
            0
          );

          completionPercentage = Math.round(
            totalCompletion / sectionData.length
          );

          // Section is complete if all items are 100% complete
          isComplete = itemCompletions.every(
            (percentage) => percentage === 100
          );
        } else {
          // No items in collection - 0% complete
          isComplete = false;
          completionPercentage = 0;
        }
      }
      // CASE 2: Non-collection forms with related subcategories
      else if (!section.isCollection && section.relatedSections) {
        const fieldNames = extractFieldNamesFromSubcategories(
          section.relatedSections
        );
        const result = calculateFieldsCompletion(fieldNames);
        completionPercentage = result.completionPercentage;
        isComplete = result.isComplete;
      }
      // CASE 3: Sections with direct path but no subcategories
      else if (section.path && sectionData && typeof sectionData === 'object') {
        // Filter out special properties like lastUpdated
        const relevantKeys = Object.keys(sectionData).filter(
          (key) => key !== 'lastUpdated'
        );

        if (relevantKeys.length) {
          const filledFields = relevantKeys.filter((key) => {
            const value = sectionData[key];
            return value !== undefined && value !== null && value !== '';
          }).length;

          completionPercentage = Math.round(
            (filledFields / relevantKeys.length) * 100
          );
          isComplete = filledFields === relevantKeys.length;
        }
      }

      return {
        ...section,
        isComplete,
        completionPercentage,
        lastUpdated: sectionLastUpdated,
      };
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    formMethods,
    formSections,
    createTimestamp,
    calculateFieldsCompletion,
    watchedValues,
  ]);

  // Calculate overall completion percentage
  const overallCompletion = useMemo(() => {
    if (!sectionStatusWithPercentages.length) return 0;

    const sum = sectionStatusWithPercentages.reduce(
      (total, section) => total + section.completionPercentage,
      0
    );

    return Math.round(sum / sectionStatusWithPercentages.length);
  }, [sectionStatusWithPercentages]);

  // Update form data with the calculated completion percentage
  // And track the last updated information
  useEffect(() => {
    if (!formMethods || !formSections) return;

    // Check if completion percentage has changed
    if (prevCompletionRef.current !== overallCompletion) {
      // Create a new timestamp
      const timestamp = createTimestamp();

      // If there's a change and it's not the initial calculation
      if (prevCompletionRef.current > 0 || overallCompletion > 0) {
        // Find the most recently updated section
        const mostRecentSection = [...sectionStatusWithPercentages].sort(
          (a, b) => {
            // Sort by timestamp if available
            if (a.lastUpdated.at !== 'N/A' && b.lastUpdated.at !== 'N/A') {
              return (
                new Date(b.lastUpdated.at).getTime() -
                new Date(a.lastUpdated.at).getTime()
              );
            }
            // Otherwise sort by completion percentage change
            return b.completionPercentage - a.completionPercentage;
          }
        )[0];

        // If we found a recently updated section, use its lastUpdated info
        if (
          mostRecentSection?.lastUpdated &&
          mostRecentSection.lastUpdated.at !== 'N/A'
        ) {
          setLastUpdated(mostRecentSection.lastUpdated);
        } else {
          // Otherwise use the current timestamp
          setLastUpdated(timestamp);
        }
      }

      // Update the ref with the current completion percentage
      prevCompletionRef.current = overallCompletion;
    }

    updateFormCompletion(formMethods, overallCompletion, formSections);
  }, [
    formMethods,
    overallCompletion,
    formSections,
    sectionStatusWithPercentages,
    createTimestamp,
  ]);

  return {
    sectionStatus: sectionStatusWithPercentages,
    overallCompletion,
    lastUpdated,
  };
};
