import { ArrowBackIcon, ArrowForwardIcon } from '@chakra-ui/icons';
import {
  <PERSON>,
  Button,
  Divider,
  Flex,
  Spinner,
  Text,
  VStack,
} from '@chakra-ui/react';
import React, {
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';

import GenericMultiSelectDropdown from '../../../organization/components/ChangeRequestIntake/MenuItems/PlanDesignTool/ProductsAndServices/GenericMultiSelectDropdown';
import { SubCategoryType } from '../../Models/types';
import SubCategory from './SubCategory';

/**
 * GenericForm Component
 *
 * Renders a complete form using the provided subcategories and react-hook-form methods.
 * Supports custom onSubmit handler for API calls and other submission logic.
 * Can operate independently with its own form state when used in a modal.
 */
type GenericFormProps = {
  formName?: string;
  subtitle?: string;
  formDescription: ReactNode;
  subCategories: SubCategoryType[];
  showSubmit?: boolean;
  showButtons?: boolean; // Control visibility of all buttons
  showSaveExit?: boolean; // Control visibility of Save and Exit button
  onSubmit?: (data: any) => void | Promise<void>;
  onContinue?: (data?: any) => void; // Updated to accept optional data parameter
  onBack?: () => void;
  onSaveExit?: () => void; // Handler for Save and Exit button
  formMethods?: UseFormReturn<any>; // Optional
  initialData?: any; // For initializing form with data (e.g., editing in modal)
  onSave?: (data: any) => void; // For saving data in modal
  onCancel?: () => void; // For cancelling in modal
  isInModal?: boolean;
  isProcessing?: boolean;
  continueButtonText?: string;
  multiSelectData?: {
    data: any;
    label?: string;
    tag?: string;
    disabledItems?: Set<number>;
  };
  table?: ReactNode; // Optional table component to render instead of form fields
};

// Utility to get the object's id property (generic) - now consistent across components
const getObjectId = (obj: any): string => {
  if (!obj || typeof obj !== 'object') return '';
  const idKey = Object.keys(obj).find((k) => k.toLowerCase().includes('id'));
  const id = idKey ? obj[idKey] : '';
  return id !== undefined && id !== null ? String(id) : '';
};

const GenericForm: React.FC<GenericFormProps> = ({
  formName,
  subtitle,
  formDescription,
  subCategories,
  showSubmit = false,
  showButtons = true,
  onSubmit,
  onContinue,
  onBack,
  onSaveExit,
  formMethods: parentFormMethods,
  initialData,
  onSave,
  onCancel,
  isInModal,
  multiSelectData,
  isProcessing = false,
  continueButtonText = 'Continue',
  table,
}) => {
  // Use local form methods if parent formMethods isn't provided
  const localFormMethods = useForm({
    defaultValues: initialData || {},
  });
  const formMethods = parentFormMethods || localFormMethods;

  const { handleSubmit, reset } = formMethods;
  const [isDirty, setIsDirty] = useState(false);

  // Dynamically determine the key for the array of selectable items
  const itemsKey = useMemo(() => {
    if (!multiSelectData?.data) return '';
    return (
      Object.keys(multiSelectData.data).find((k) =>
        Array.isArray(multiSelectData.data[k])
      ) || ''
    );
  }, [multiSelectData?.data]);

  const [selectedItems, setSelectedItems] = useState<any>(() => ({
    ...multiSelectData?.data,
    [itemsKey]: [],
  }));

  // Reset form with initialData when it changes (e.g., for editing in modal)
  useEffect(() => {
    if (initialData) {
      reset(initialData);
    }
  }, [initialData, reset]);

  /**
   * Field change handler that marks the form as dirty
   */
  const handleFieldChange = useCallback(() => {
    setIsDirty(true);
  }, []);

  /**
   * Form submission handler
   */
  const handleFormSubmit = useCallback(
    async (data: any) => {
      // If used in a modal with onSave, call it to save data
      if (onSave) {
        onSave(data);
      }
      // Otherwise, use the provided onSubmit for parent form submission
      else if (onSubmit) {
        try {
          await onSubmit(data);
        } catch (error) {
          console.error('Error in form submission:', error);
          return; // Don't reset if submission fails
        }
      }

      reset(data); // Reset form after successful submission
    },
    [reset, onSubmit, onSave]
  );

  /**
   * Continue button click handler - handles both multiSelect and regular navigation cases
   */
  const handleContinueClick = useCallback(() => {
    if (onContinue) {
      if (multiSelectData) {
        // If multiSelectData exists, pass the selectedItems to onContinue
        onContinue(selectedItems);
      } else {
        // Original behavior when multiSelectData is undefined
        onContinue();
      }
    }
  }, [onContinue, multiSelectData, selectedItems]);

  /**
   * Cancel button click handler - for modal use case
   */
  const handleCancelClick = useCallback(() => {
    if (onCancel) onCancel();
  }, [onCancel]);

  /**
   * Save and Exit button click handler
   */
  const handleSaveExitClick = useCallback(() => {
    if (onSaveExit) onSaveExit();
  }, [onSaveExit]);

  /**
   * Optimized handler for removing selected items
   */
  const handleRemoveItem = useCallback(
    (item: any) => {
      const itemId = getObjectId(item);

      if (!itemId) {
        return;
      }

      setSelectedItems((prev: any) => {
        const currentItems = prev[itemsKey] || [];
        const filteredItems = currentItems.filter(
          (i: any) => getObjectId(i) !== itemId
        );

        return {
          ...prev,
          [itemsKey]: filteredItems,
        };
      });
    },
    [itemsKey]
  );

  return (
    <Box
      as="form"
      display="flex"
      flexDirection="column"
      width="100%"
      mx={isInModal ? '0' : '16px'}
      p={isInModal ? 4 : 8}
      background={'white'}
      borderWidth={isInModal ? 0 : '1px'}
      borderRadius={isInModal ? 0 : 'lg'}
      shadow={isInModal ? 'none' : 'md'}
      onSubmit={handleSubmit(handleFormSubmit)}
      minHeight="60vh"
    >
      <Box flex="1">
        {/* Always render form name, description, and divider */}
        {(formName || formDescription) && (
          <>
            {formName && (
              <Text fontSize="2xl" mb={3}>
                {formName}
              </Text>
            )}
            {formDescription && <Box mb={5}>{formDescription}</Box>}
          </>
        )}
        <Divider />

        {subtitle && (
          <Text color="#676767" fontSize="4xl" ml={2} my={5}>
            {subtitle}
          </Text>
        )}

        {/* Render table if provided, otherwise render multi-select or subcategories */}
        {table ? (
          <Box mb={6}>{table}</Box>
        ) : multiSelectData ? (
          <Box 
            mb={6} 
            onClick={(e) => e.stopPropagation()}
            onMouseDown={(e) => e.stopPropagation()}
            onMouseUp={(e) => e.stopPropagation()}
            onPointerDown={(e) => e.stopPropagation()}
            onPointerUp={(e) => e.stopPropagation()}
          >
            <GenericMultiSelectDropdown
              data={multiSelectData.data}
              label={multiSelectData.label || 'Select product add-ons'}
              selectedItems={selectedItems}
              setSelectedItems={setSelectedItems}
              tag={multiSelectData.tag}
              itemsKey={itemsKey}
              disabledItems={multiSelectData.disabledItems}
            />
            {/* Render selected items below dropdown */}
            <Box mt={4} display="flex" flexWrap="wrap" gap={2}>
              {selectedItems[itemsKey]?.map((item: any, index: number) => {
                const itemId = getObjectId(item);
                return (
                  <Box
                    key={`${itemId || `item-${index}`}-${item.name}`}
                    bg="gray.50"
                    border="1px solid"
                    borderColor="gray.200"
                    borderRadius="md"
                    px={4}
                    py={2}
                    display="flex"
                    alignItems="center"
                    fontWeight="medium"
                    fontSize="sm"
                    boxShadow="sm"
                    position="relative"
                  >
                    <Text mr={2}>{item.name}</Text>
                    <Button
                      type="button"
                      size="sm"
                      minW="1.5rem"
                      h="1.5rem"
                      fontSize="lg"
                      fontWeight="bold"
                      variant="ghost"
                      colorScheme="red"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleRemoveItem(item);
                      }}
                      aria-label={`Remove ${item.name}`}
                      _hover={{ bg: 'red.100' }}
                    >
                      ×
                    </Button>
                  </Box>
                );
              })}
            </Box>

          </Box>
        ) : (
          subCategories.length > 0 && (
            <VStack spacing={5} align="stretch">
              {subCategories.map((subCategory, index) => (
                <React.Fragment key={index}>
                  <SubCategory
                    subCategory={subCategory}
                    formMethods={formMethods}
                    onChange={handleFieldChange}
                    isInModal={isInModal}
                  />
                  {index < subCategories.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </VStack>
          )
        )}
      </Box>
      <Divider />
      {/* Navigation Buttons - only shown if showButtons is true */}
      {showButtons && (
        <Flex justify="space-between" mt="auto" pt={5}>
          <Box>
            <Button
              onClick={onBack || handleCancelClick}
              isDisabled={(!onBack && !onCancel) || isProcessing}
              variant="outline"
              borderColor="#01874e"
              color="#01874e"
              leftIcon={<ArrowBackIcon />}
            >
              Back
            </Button>
          </Box>

          <Flex>
            {/* Only show Save and Exit if enabled */}
            {onSaveExit && (
              <Button
                variant="outline"
                mr={4}
                onClick={handleSaveExitClick}
                size="md"
                borderRadius="md"
                isDisabled={isProcessing}
              >
                Save Changes and Exit
              </Button>
            )}

            {showSubmit || !onContinue ? (
              <Button
                backgroundColor="#01874e"
                color="white"
                _hover={{ backgroundColor: '#016c3d' }}
                type="submit"
                rightIcon={
                  isProcessing ? <Spinner size="sm" /> : <ArrowForwardIcon />
                }
                isDisabled={!isDirty || isProcessing}
              >
                {isProcessing ? 'Processing...' : 'Submit'}
              </Button>
            ) : (
              <Button
                backgroundColor="#01874e"
                color="white"
                _hover={{ backgroundColor: '#016c3d' }}
                onClick={handleContinueClick}
                rightIcon={
                  isProcessing ? <Spinner size="sm" /> : <ArrowForwardIcon />
                }
                isDisabled={
                  isProcessing ||
                  (multiSelectData &&
                    !!itemsKey &&
                    selectedItems[itemsKey]?.length === 0)
                }
              >
                {isProcessing ? 'Processing...' : continueButtonText}
              </Button>
            )}
          </Flex>
        </Flex>
      )}
    </Box>
  );
};

export default GenericForm;
