import { CheckCircleIcon, ChevronLeftIcon, EditIcon } from '@chakra-ui/icons';
import { Box, Button, Divider, Flex, Heading, Text } from '@chakra-ui/react';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { FormSections } from './components/hooks/Completion/types';
import { useFormCompletionTracking } from './components/hooks/Completion/useFormCompletion';

/**
 * Props for the FormReviewSummary component
 */
interface FormReviewSummaryProps {
  title: string; // Main title for the review summary
  description?: string; // Description text
  subtitle?: string; // Subtitle for the form sections
  formMethods: UseFormReturn<any>; // React Hook Form methods
  onBack?: (sectionId?: string) => void; // Navigation handler
  onSaveExit?: () => void; // Save and exit handler
  formSections: FormSections;
}

/**
 * FormReviewSummary Component
 *
 * Displays a summary of form sections with completion tracking.
 * Can track individual section completion as well as overall completion.
 */
const FormReviewSummary: React.FC<FormReviewSummaryProps> = ({
  title = 'Review and Save Changes',
  description = "Here's a brief description placeholder of what this page will have the user work or make changes to.",
  subtitle = 'Products & Services',
  formMethods,
  onBack,
  onSaveExit,
  formSections,
}) => {
  // Use the form-based completion tracking hook instead of session-based
  const { sectionStatus, overallCompletion } = useFormCompletionTracking(
    formMethods,
    formSections
  );

  /**
   * Handle navigation to edit a specific section
   */
  const handleEditClick = (sectionId: string) => {
    if (onBack) {
      onBack(sectionId);
    }
  };

  /**
   * Handle back navigation
   */
  const handleBackClick = () => {
    if (onBack) {
      onBack();
    }
  };

  return (
    <Box bg="white" p={6} mr={12} ml={4} borderRadius="md">
      {/* Header with title, description, and top save button */}
      <Flex justify="space-between" align="center" mb={6}>
        <Box maxW="70%">
          <Heading as="h1" size="lg" fontWeight="medium" color="#696969" mb={2}>
            {title}
          </Heading>
          <Text color="#696969">{description}</Text>
        </Box>
        <Button
          variant="outline"
          borderColor="gray.300"
          onClick={onSaveExit}
          h="38px"
          px={4}
          borderRadius="md"
          minW="180px"
        >
          Save Changes and Exit
        </Button>
      </Flex>
      <Divider mb={6} />

      {/* Bordered container for sections */}
      <Box borderWidth="1px" borderColor="gray.200" borderRadius="md" mb={6}>
        {/* Overall Progress Section */}
        <Box
          bg="gray.50"
          px={6}
          py={3}
          borderTopRadius="md"
          borderBottomWidth="1px"
          borderColor="gray.200"
        >
          <Flex align="center">
            <Text fontWeight="medium" color="#696969" fontSize="lg" mr={2}>
              {subtitle}
            </Text>
            <Flex
              bg="#E6F6EC"
              color="#007F36"
              px={2}
              py={1}
              borderRadius="md"
              fontSize="sm"
              fontWeight="medium"
              align="center"
            >
              <Box as="span" mr={1}>
                ✓
              </Box>
              <Text>{overallCompletion}% Complete</Text>
            </Flex>
          </Flex>
        </Box>

        {/* Individual Sections */}
        <Box px={4}>
          {sectionStatus.map((section) => (
            <Box
              key={section.id}
              py={4}
              borderBottomWidth={
                sectionStatus.indexOf(section) < sectionStatus.length - 1
                  ? '1px'
                  : '0'
              }
              borderColor="gray.200"
            >
              <Flex align="center">
                <CheckCircleIcon
                  color={section.isComplete ? 'green.500' : 'gray.300'}
                  mr={4}
                />
                <Box flex="1">
                  <Text fontWeight="medium" color="blue.500">
                    {section.title}
                  </Text>
                  <Text color="gray.600" fontSize="sm">
                    Last Updated by {section.lastUpdated.by}, at{' '}
                    {section.lastUpdated.at}
                  </Text>
                </Box>
                <Flex align="center">
                  <Text color="gray.600" fontSize="sm" mr={3}>
                    {section.completionPercentage}% Complete
                  </Text>
                  <Button
                    rightIcon={<EditIcon />}
                    variant="outline"
                    colorScheme="green"
                    size="sm"
                    onClick={() => handleEditClick(section.id)}
                  >
                    Edit
                  </Button>
                </Flex>
              </Flex>
            </Box>
          ))}
        </Box>
      </Box>

      <Divider my={6} />

      {/* Navigation Buttons */}
      <Flex justify="space-between">
        <Button
          leftIcon={<ChevronLeftIcon />}
          variant="outline"
          color="gray.700"
          onClick={handleBackClick}
        >
          Back
        </Button>
        <Button colorScheme="green" onClick={onSaveExit}>
          Save Changes and Exit
        </Button>
      </Flex>
    </Box>
  );
};

// Make sure we have both a named export and a default export
export { FormReviewSummary };
export default FormReviewSummary;
