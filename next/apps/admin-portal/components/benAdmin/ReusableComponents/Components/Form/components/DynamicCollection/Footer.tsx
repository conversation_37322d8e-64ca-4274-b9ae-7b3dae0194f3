/**
 * Footer component for DynamicCollectionSection
 */
import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';
import { Box, Button, Divider, Flex } from '@chakra-ui/react';
import React from 'react';

import { FooterProps } from '../types/types';

/**
 * Renders the footer with navigation buttons
 */
const Footer: React.FC<FooterProps> = ({
  hasItems,
  isOptional,
  backButtonText,
  saveExitButtonText,
  continueButtonText,
  skipStepButtonText,
  onBackClick,
  onSaveExitClick,
  onContinueClick,
  onSkipClick,
  hideContinueButton = false,
}) => {
  return (
    <>
      <Divider mb={6} />
      <Flex justify="space-between" align="center" mt={10} mb={10}>
        <Box>
          <Button
            variant="outline"
            colorScheme="green"
            onClick={onBackClick}
            leftIcon={<ChevronLeftIcon />}
            size="md"
            borderRadius="md"
          >
            {backButtonText}
          </Button>
        </Box>
        <Flex>
          {onSaveExitClick && (
            <Button
              variant="outline"
              mr={4}
              onClick={onSaveExitClick}
              size="md"
              borderRadius="md"
            >
              {saveExitButtonText}
            </Button>
          )}
          {!hideContinueButton &&
            (hasItems ? (
              <Button
                colorScheme="green"
                onClick={isOptional ? onSkipClick : onContinueClick}
                rightIcon={<ChevronRightIcon />}
                size="md"
                borderRadius="md"
              >
                {continueButtonText}
              </Button>
            ) : (
              <Button
                colorScheme="green"
                onClick={isOptional ? onSkipClick : onContinueClick}
                rightIcon={<ChevronRightIcon />}
                size="md"
                borderRadius="md"
              >
                {isOptional ? skipStepButtonText : continueButtonText}
              </Button>
            ))}
        </Flex>
      </Flex>
    </>
  );
};

export default Footer;
