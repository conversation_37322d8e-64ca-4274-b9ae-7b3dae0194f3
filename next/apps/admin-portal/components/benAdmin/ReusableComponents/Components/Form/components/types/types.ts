/**
 * Type definitions for DynamicCollectionSection
 */
import React, { ComponentType, ReactNode } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { SubCategoryType } from '../../../../Models/types';

/**
 * Timestamp object for tracking when items are modified
 */
export interface Timestamp {
  by: string;
  at: string;
}

/**
 * Props for item components that render individual collection items
 */
export interface ItemComponentProps {
  formMethods: UseFormReturn<any>;
  basePath: string;
  index: number;
  onRemove: () => void;
  onEdit: () => void;
  editButtonText: string;
  optionsButtonText: string;
  additionalOptions?: {
    optionName: string;
    onOptionClick: (item?: any) => any;
    isOptionDisabled?: boolean;
  }[];
  deleteMenuItemText: string;
  item: any;
  items: any[];
  customNameField?: string;
}

/**
 * Props for accordion summary component
 */
export interface AccordionSummaryComponentProps {
  formMethods: UseFormReturn<any>;
  basePath: string;
  index: number;
  isOpen?: boolean;
  toggleAccordion?: () => void;
  onRemove?: () => void;
  onEdit?: () => void;
  hideButtons?: boolean;
  editButtonText?: string;
  optionsButtonText?: string;
  deleteMenuItemText?: string;
  itemLabelKey?: string;
  items?: any[];
  item?: any;
  getStatusIcon?: (status: string) => ReactNode | null;
  getPlanDesignValidationStatus?: (planIndex: number) => string;
}

/**
 * Props for accordion details component
 */
export interface AccordionDetailsComponentProps {
  formMethods: UseFormReturn<any>;
  basePath: string;
  index: number;
  tableComponent?: React.ReactNode;
}

export interface GeneralFormProps {
  initialData?: Record<string, any>;
  itemIndex: number | null;
  isNewItem: boolean;
  onSave: (data: any) => void;
  onCancel: () => void;
}

export type CustomVar = [string, any];

/**
 * Main props for the DynamicCollectionSection component
 */
export interface DynamicCollectionSectionProps {
  title: string;
  description?: string;
  formMethods: any;
  basePath: string;
  emptyMessage?: string;
  skipMessage?: string;
  isOptional?: boolean;
  modalTitle?: string;
  modalSize?: string;
  onContinue?: () => void;
  onBack?: () => void;
  onSaveExit?: () => void;
  onSkip?: () => void;
  generalForm?: React.ReactNode;
  itemComponent?: React.ComponentType<ItemComponentProps>;
  useAccordion?: boolean;
  accordionSummaryComponent?: React.ComponentType<any>;
  accordionDetailsComponent?: React.ComponentType<any>;
  accordionContentComponent?: React.ComponentType<{
    item: any;
    index: number;
    basePath: string;
    formMethods: UseFormReturn<any>;
  }>;
  tableComponent?: React.ReactNode | ((item: any) => React.ReactNode);
  hideAccordionIcon?: boolean;
  itemLabelKey?: string;
  fieldsToExcludeFromCompletion?: string[];
  addNewButtonText?: string;
  createNewButtonText?: string;
  showOptionsButton?: boolean | ((item: any) => boolean);
  backButtonText?: string;
  saveExitButtonText?: string;
  continueButtonText?: string;
  skipStepButtonText?: string;
  editButtonText?: string;
  optionsButtonText?: string;
  additionalOptions?: {
    optionName: string;
    onOptionClick: (item?: any) => any;
    isOptionDisabled?: boolean;
  }[];
  deleteMenuItemText?: string;
  deleteAlertTitle?: string;
  deleteAlertMessage?: string;
  deleteAlertCancelText?: string;
  deleteAlertConfirmText?: string;
  itemAddedToastTitle?: string;
  itemUpdatedToastTitle?: string;
  itemRemovedToastTitle?: string;
  onAddItem?: () => void;
  onEditItem?: (index: number) => void;
  onRemoveItem?: (index: number) => void;
  customHandleSaveItem?: (data: any) => boolean;
  customHandleCloseModal?: () => void;
  createEmptyObject?: boolean;
  isAddButtonLoading?: boolean;
  hideContinueButton?: boolean;
  newObjectId?: string;
  copyFieldsFrom?: any;
  addCustomVars?: any[];
  onOpenModal?: () => void;
  filterItems?: (item: any, index: number, allItems: any[]) => boolean;
  itemIdentifier?: [string, any];
  showEmptyContainer?: boolean;
  emptyStateOptions?: any;
  onGetStarted?: (item: any) => void;
  distinct?: {
    isDistinct: boolean;
    indicator: string;
    filter?: (item: any) => boolean;
  };
  addChannelButtonText?: string;
  onAddChannel?: (item: any) => void;
  customNameField?: string;
  hideAddNewButton?: boolean;
  getStatusIcon?: (status: string) => ReactNode | null;
  getPlanDesignValidationStatus?: (planIndex: number) => string;
}

/**
 * Item state for tracking operations in the modal
 */
export interface ItemState {
  isEditing: boolean;
  index: number | null;
}

/**
 * State for tracking which accordions are open
 */
export interface OpenAccordionsState {
  [key: number]: boolean;
}

/**
 * Props for the Header component
 */
export interface HeaderProps {
  title: string;
  description: string;
  isOptional: boolean;
  hasItems: boolean;
  addNewButtonText: string;
  onAddItem: () => void;
  useAccordion: boolean;
  isAddButtonLoading?: boolean;
  hideAddNewButton?: boolean;
}

/**
 * Props for the EmptyState component
 */
export interface EmptyStateProps {
  emptyMessage: string;
  skipMessage: string;
  createNewButtonText: string;
  onAddItem: () => void;
  isAddButtonLoading?: boolean;
  emptyStateOptions?: EmptyStateOptions;
}

/**
 * Props for the Footer component
 */
export interface FooterProps {
  hasItems: boolean;
  isOptional: boolean;
  backButtonText: string;
  saveExitButtonText: string;
  continueButtonText: string;
  skipStepButtonText: string;
  onBackClick: () => void;
  onSaveExitClick?: () => void;
  onContinueClick: () => void;
  onSkipClick: () => void;
  hideContinueButton?: boolean;
}

/**
 * Props for the ItemModal component
 */
export interface ItemModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  modalSize?: string;
  itemState: {
    isEditing: boolean;
    index: number | null;
  };
  items: any[];
  generalForm?: ReactNode;
  onSave: (data: any) => void;
  multiPageModal?: boolean;
  pages?: any[];
  getPageTitle?: (currentPage: number, pageData: any) => string;
}

/**
 * Props for the DeleteDialog component
 */
export interface DeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  cancelText: string;
  confirmText: string;
}

/**
 * Props for the AccordionView component
 */
export interface AccordionViewProps {
  items: any[];
  basePath: string;
  formMethods: UseFormReturn<any>;
  openAccordions: Record<number, boolean>;
  toggleAccordion: (index: number) => void;
  onEdit: (index: number) => void;
  onRemove: (index: number) => void;
  onGetStarted?: (item: any, index: number | number[]) => void;
  hideAccordionIcon?: boolean;
  editButtonText?: string;
  optionsButtonText?: string;
  additionalOptions?: {
    optionName: string;
    onOptionClick: (item?: any) => any;
    isOptionDisabled?: boolean;
  }[];
  deleteMenuItemText?: string;
  accordionSummaryComponent?: ComponentType<AccordionSummaryComponentProps>;
  accordionDetailsComponent?: ComponentType<AccordionDetailsComponentProps>;
  accordionContentComponent?: React.ComponentType<{
    item: any;
    index: number;
    basePath: string;
    formMethods: UseFormReturn<any>;
  }>;
  tableComponent?: ReactNode | ((item: any) => ReactNode);
  itemLabelKey?: string;
  distinct?: {
    isDistinct: boolean;
    indicator: string;
    filter?: (item: any) => boolean;
  };
  showOptionsButton?: boolean | ((item: any) => boolean);
  addChannelButtonText?: string;
  onAddChannel?: (item: any) => void;
  getStatusIcon?: (status: string) => ReactNode | null;
  getPlanDesignValidationStatus?: (planIndex: number) => string;
}

/**
 * Props for the ListView component
 */
export interface ListViewProps {
  items: any[];
  basePath: string;
  formMethods: UseFormReturn<any>;
  onEdit: (index: number) => void;
  onRemove: (index: number) => void;
  editButtonText: string;
  optionsButtonText: string;
  additionalOptions?: {
    optionName: string;
    onOptionClick: (item?: any) => void;
    isOptionDisabled?: boolean;
  }[];
  deleteMenuItemText: string;
  itemComponent?: React.ComponentType<ItemComponentProps>;
  customNameField?: string;
}

/**
 * Return type for useItemOperations hook
 */
export interface ItemOperations {
  itemState: ItemState;
  handleAddItem: () => void;
  handleEditItem: (index: number) => void;
  handleCloseModal: () => void;
  handleOpenDeleteDialog: (index: number) => void;
  handleConfirmDelete: () => void;
  handleSaveItem: (data: any) => void;
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  isDeleteAlertOpen: boolean;
  openDeleteAlert: () => void;
  closeDeleteAlert: () => void;
}

export interface CopyFieldsFrom {
  index?: number;
  fields?: string[];
}

/**
 * Props for useItemOperations hook
 */
export interface ItemOperationsProps {
  formMethods: UseFormReturn<any>;
  basePath: string;
  items: any[];
  useAccordion: boolean;
  resetAccordions: () => void;
  onAddItem?: () => void;
  onEditItem?: (index: number) => void;
  onRemoveItem?: (index: number) => void;
  customHandleSaveItem?: (data: any) => void;
  customHandleCloseModal?: () => void;
  itemAddedToastTitle: string;
  itemUpdatedToastTitle: string;
  itemRemovedToastTitle: string;
  createEmptyObject?: boolean;
  newObjectId?: string;
  copyFieldsFrom?: CopyFieldsFrom;
  addCustomVars?: CustomVar[];
  onOpenModal?: () => void;
  filterItems?: (item: any, index: number, items: any[]) => boolean;
  filteredItems?: any[];
}

/**
 * Return type for useCompletionTracking hook
 */
export interface CompletionTrackingResult {
  itemCompletions: { [key: number]: number };
  overallCompletionPercentage: number;
  isComplete: boolean;
}

export interface SectionConfig {
  id: string;
  title: string;
  path?: string;
  isCollection?: boolean;
  relatedSections?: SubCategoryType<any>[];
}

export interface FormSections {
  sections: SectionConfig[];
  id: string;
}

export interface SectionStatus extends SectionConfig {
  isComplete: boolean;
  completionPercentage: number;
  lastUpdated: {
    by: string;
    at: string;
  };
}

// Add this new interface
export interface MirrorOptions {
  enabled: boolean;
  sourceBasePath: string;
  label?: string;
  transformItem: (item: any) => any;
  sourceFilter?: (item: any) => boolean;
  customDuplicateCheck?: (newItem: any, existingItems: any[]) => boolean;
}

// Add this new interface
interface EmptyStateOptions {
  showMirrorCheckbox: boolean;
  mirrorLabel: string;
  onUncheck?: () => void;
  onCheck?: (checked: boolean) => void;
}
