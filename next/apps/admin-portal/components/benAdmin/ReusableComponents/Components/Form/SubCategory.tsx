import { Box, Text, VStack } from '@chakra-ui/react';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { SubCategoryType } from '../../Models/types';
import FormFieldRenderer from './components/sharables/FormFieldRenderer';

type SubCategoryProps = {
  subCategory: SubCategoryType;
  formMethods: UseFormReturn<any>;
  onChange?: () => void;
  isInModal?: boolean;
};

/**
 * SubCategory Component
 *
 * Renders a single section (subcategory) of the form.
 * It iterates over the field configurations and uses <PERSON><PERSON>ield<PERSON>enderer
 * to render each field based on its type.
 */
const SubCategory: React.FC<SubCategoryProps> = ({
  subCategory,
  formMethods,
  onChange,
  isInModal = false,
}) => {
  const { title, description, fields } = subCategory;

  // Handler for field change events
  const handleFieldChange = () => {
    if (onChange) {
      onChange();
    }
  };

  const titlePresent = title !== '';
  const descriptionPresent = description !== '';

  return (
    <Box
      maxWidth="100%"
      pl={isInModal ? 0 : 4}
      pr={isInModal ? 0 : 4}
      mb={6}
      width="100%"
    >
      {titlePresent && (
        <Text py={6} color="#676767" fontSize="lg" fontWeight="bold" mb={2}>
          {title}
        </Text>
      )}
      {descriptionPresent && (
        <Text fontSize="sm" mb={4} color="gray.600">
          {description}
        </Text>
      )}

      <VStack spacing={4} align="stretch">
        {fields.map((field, index) => (
          <Box
            key={
              field.type === 'inlineGroup'
                ? `inline-group-${
                    title ? title.replace(/\s+/g, '-').toLowerCase() + '-' : ''
                  }${index}-${
                    Array.isArray(field.fields)
                      ? field.fields
                          .filter(
                            (f) => f && typeof f === 'object' && 'name' in f
                          )
                          .map((f) => (f as any).name || '')
                          .filter(Boolean)
                          .join('-') ||
                        `unnamed-group-${index}-${Date.now()
                          .toString(36)
                          .substring(4)}`
                      : `unnamed-group-${index}-${Date.now()
                          .toString(36)
                          .substring(4)}`
                  }`
                : 'name' in field && field.name
                ? `field-${field.name}-${index}`
                : `field-${index}-${Date.now().toString(36).substring(4)}`
            }
            w="100%"
            minWidth="0"
            overflow="hidden"
          >
            <FormFieldRenderer
              field={field}
              formMethods={formMethods}
              onChange={handleFieldChange}
            />
          </Box>
        ))}
      </VStack>
    </Box>
  );
};

export default SubCategory;
