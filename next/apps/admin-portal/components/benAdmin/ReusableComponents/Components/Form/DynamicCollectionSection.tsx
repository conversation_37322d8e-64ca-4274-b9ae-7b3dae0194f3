/**
 * DynamicCollectionSection Component
 *
 * This component provides a standardized interface for managing collections of items
 * with create, read, update, and delete functionality. It includes modals for adding
 * and editing items, confirmation dialogs for deletion, and navigation controls.
 *
 * The component is designed to be used with react-hook-form and relies on a
 * formMethods object to manage form state at the specified basePath.
 *
 * All button texts and functions are customizable with default fallbacks.
 *
 * New objects can be given a unique identifier by specifying a newObjectId prop.
 * This will generate a UUID and add it to the root of the created object.
 */
import { Box } from '@chakra-ui/react';
import React, { useCallback, useMemo } from 'react';
import { useWatch } from 'react-hook-form';

import {
  AccordionView,
  DeleteDialog,
  EmptyState,
  Footer,
  Header,
  ItemModal,
  ListView,
} from './components/DynamicCollection';
import {
  useAccordionState,
  useItemOperations,
} from './components/hooks/DynamicCollections';
import { DynamicCollectionSectionProps } from './components/types/types';

/**
 * URL utilities for  managing URL parameters
 */
export const urlUtils = {
  getParams: (): {
    itemIndexParam: string | null;
    titleParam: string | null;
  } => {
    if (typeof window === 'undefined')
      return { itemIndexParam: null, titleParam: null };

    const urlParams = new URLSearchParams(window.location.search);
    return {
      itemIndexParam: urlParams.get('itemIndex'),
      titleParam: urlParams.get('title'),
    };
  },

  updateParams: (params: Record<string, string | null>): void => {
    if (typeof window === 'undefined') return;

    const currentUrl = new URL(window.location.href);
    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        currentUrl.searchParams.delete(key);
      } else {
        currentUrl.searchParams.set(key, value);
      }
    });
    window.history.replaceState({}, '', currentUrl.toString());
  },
};

/**
 * Main DynamicCollectionSection component
 */
const DynamicCollectionSection: React.FC<DynamicCollectionSectionProps> = ({
  title,
  description = "Here's a brief description placeholder of what this page will have the user work or make changes to. It should be able to provide context and information to the user to confidently answer information.",
  formMethods,
  basePath,
  emptyMessage = "You haven't added anything.",
  skipMessage = 'If this does not have any, skip to the next step.',
  isOptional = false,
  modalTitle,
  modalSize = 'xl',
  onContinue,
  onBack,
  onSaveExit,
  onSkip,
  generalForm,
  itemComponent,

  // Accordion options
  useAccordion = false,
  accordionSummaryComponent,
  accordionDetailsComponent,
  tableComponent,
  hideAccordionIcon = true,
  itemLabelKey,

  // Completion tracking options
  fieldsToExcludeFromCompletion = ['lastUpdated'],

  // Button text customization with defaults
  addNewButtonText = 'Add New',
  createNewButtonText = 'Create New',
  backButtonText = 'Back',
  saveExitButtonText = 'Save Changes and Exit',
  continueButtonText = 'Continue',
  skipStepButtonText = 'Skip Step',
  editButtonText = 'Edit',
  optionsButtonText = 'Options',
  additionalOptions,
  deleteMenuItemText = 'Delete',

  // Alert dialog customization with defaults
  deleteAlertTitle = 'Delete Item',
  deleteAlertMessage = 'Are you sure you want to delete this item? This action cannot be undone.',
  deleteAlertCancelText = 'Cancel',
  deleteAlertConfirmText = 'Delete',

  // Toast messages customization with defaults
  itemAddedToastTitle = 'Item added',
  itemUpdatedToastTitle = 'Item updated',
  itemRemovedToastTitle = 'Item removed',

  // Custom handlers
  onAddItem,
  onEditItem,
  onRemoveItem,
  customHandleSaveItem,
  customHandleCloseModal,

  // New option for creating empty objects
  createEmptyObject = false,
  isAddButtonLoading = false,

  // New option to hide the continue button
  hideContinueButton = false,
  hideAddNewButton = false,

  // New option to specify an ID field for new objects
  newObjectId,

  // New option to copy fields from an existing object
  copyFieldsFrom,

  // New option to add custom variables
  addCustomVars,

  // New option for triggering modal open callback
  onOpenModal,

  // New option for filtering items based on criteria
  filterItems,

  // New option for identifying which type of item this is
  itemIdentifier,

  // New option for showing empty container
  showEmptyContainer = false,

  // New option for empty state options
  emptyStateOptions,

  // New option for getting started
  onGetStarted,

  // New option for distinct items
  distinct,

  // New option for showing options button
  showOptionsButton,

  // New option for adding a channel button
  addChannelButtonText,
  onAddChannel,
  customNameField,
  getStatusIcon,
  getPlanDesignValidationStatus,
}) => {
  // Get all items from form state
  const allItems = useWatch({
    control: formMethods.control,
    name: basePath,
    defaultValue: [],
  });

  // Apply filtering if provided
  const items = useMemo(() => {
    // Make sure allItems is an array
    if (!allItems || !Array.isArray(allItems)) {
      return [];
    }

    // Apply filter function if provided
    if (filterItems && typeof filterItems === 'function') {
      return allItems.filter((item: any, index: number) =>
        filterItems(item, index, allItems)
      );
    }

    return allItems;
  }, [allItems, filterItems]);

  const shouldShowEmptyState = useMemo(() => {
    if (typeof window === 'undefined')
      return !items.length || showEmptyContainer;

    const urlParams = new URLSearchParams(window.location.search);
    const emptyStateParam = urlParams.get('emptyState');
    const result =
      !items.length ||
      showEmptyContainer ||
      (emptyStateParam === 'true' && !!emptyStateOptions);
    return result;
  }, [emptyStateOptions, items.length, showEmptyContainer]);

  // Use custom hooks for state and logic
  const { openAccordions, toggleAccordion, resetAccordions } =
    useAccordionState();

  // Check if we should use the modal or delegate to parent component
  const shouldUseModal = !!generalForm;

  const {
    itemState,
    handleAddItem: baseHandleAddItem,
    handleEditItem: baseHandleEditItem,
    handleCloseModal: baseHandleCloseModal,
    handleOpenDeleteDialog,
    handleConfirmDelete,
    handleSaveItem,
    isOpen,
    isDeleteAlertOpen,
    closeDeleteAlert,
  } = useItemOperations({
    formMethods,
    basePath,
    items: allItems,
    filteredItems: items,
    useAccordion,
    resetAccordions,
    onAddItem: !shouldUseModal ? onAddItem : undefined,
    onEditItem: !shouldUseModal ? onEditItem : undefined,
    onRemoveItem,
    customHandleSaveItem,
    customHandleCloseModal: customHandleCloseModal,
    itemAddedToastTitle,
    itemUpdatedToastTitle,
    itemRemovedToastTitle,
    createEmptyObject,
    newObjectId,
    copyFieldsFrom,
    addCustomVars: [
      ...(addCustomVars || []),
      ...(itemIdentifier ? [itemIdentifier] : []),
    ],
    onOpenModal,
    filterItems,
  });

  // Create a wrapper for handleCloseModal that includes form reset
  const handleCloseModal = useCallback(() => {
    // Reset form state
    formMethods.reset(formMethods.getValues(), {
      keepValues: false,
      keepDirty: false,
      keepTouched: false,
      keepErrors: false,
      keepIsSubmitted: false,
      keepSubmitCount: false,
    });

    // Clear any lingering form state
    formMethods.clearErrors();

    // Force a re-render
    formMethods.trigger();

    // Call the base close handler
    baseHandleCloseModal();
  }, [formMethods, baseHandleCloseModal]);

  // Custom handlers that wrap the base handlers
  const handleAddItem = () => {
    if (shouldUseModal) {
      // Use the built-in modal flow
      baseHandleAddItem();
    } else if (onAddItem) {
      // Let the hook handle creating the empty object with UUID
      if (createEmptyObject) {
        baseHandleAddItem();
      } else {
        // Then call the parent's onAddItem handler
        onAddItem();
      }
    }
  };

  const handleEditItem = (index: number) => {
    // Always update the URL parameter regardless of modal usage
    urlUtils.updateParams({ itemIndex: index.toString() });

    if (shouldUseModal) {
      // Use the built-in modal flow
      baseHandleEditItem(index);
    } else if (onEditItem) {
      // Then call the parent's onEditItem handler
      onEditItem(index);
    }
  };

  // Navigation handlers
  const handleBackClick = () => onBack && onBack();
  const handleSaveExitClick = onSaveExit ? () => onSaveExit() : undefined;
  const handleContinueClick = () => onContinue && onContinue();
  const handleSkipClick = () =>
    onSkip ? onSkip() : onContinue && onContinue();

  return (
    <Box bg="white" p={4} mr={12} ml={4} borderRadius={'2xl'}>
      {/* Header Section */}
      <Header
        title={title}
        description={description}
        isOptional={isOptional}
        hasItems={items.length > 0}
        addNewButtonText={addNewButtonText}
        onAddItem={handleAddItem}
        useAccordion={useAccordion}
        isAddButtonLoading={isAddButtonLoading}
        hideAddNewButton={
          showEmptyContainer || shouldShowEmptyState || hideAddNewButton
        }
      />

      {/* Collection Content */}
      {shouldShowEmptyState ? (
        <EmptyState
          emptyMessage={emptyMessage}
          skipMessage={skipMessage}
          createNewButtonText={createNewButtonText}
          onAddItem={handleAddItem}
          isAddButtonLoading={isAddButtonLoading}
          emptyStateOptions={emptyStateOptions}
        />
      ) : useAccordion ? (
        <AccordionView
          items={items}
          basePath={basePath}
          formMethods={formMethods}
          openAccordions={openAccordions}
          toggleAccordion={toggleAccordion}
          onEdit={handleEditItem}
          onRemove={handleOpenDeleteDialog}
          hideAccordionIcon={hideAccordionIcon}
          editButtonText={editButtonText}
          optionsButtonText={optionsButtonText}
          additionalOptions={additionalOptions}
          deleteMenuItemText={deleteMenuItemText}
          accordionSummaryComponent={accordionSummaryComponent}
          accordionDetailsComponent={accordionDetailsComponent}
          tableComponent={tableComponent}
          itemLabelKey={itemLabelKey}
          onGetStarted={onGetStarted}
          distinct={distinct}
          showOptionsButton={showOptionsButton}
          addChannelButtonText={addChannelButtonText}
          onAddChannel={onAddChannel}
          getStatusIcon={getStatusIcon}
          getPlanDesignValidationStatus={getPlanDesignValidationStatus}
        />
      ) : (
        <ListView
          items={items}
          basePath={basePath}
          formMethods={formMethods}
          onEdit={handleEditItem}
          onRemove={handleOpenDeleteDialog}
          editButtonText={editButtonText}
          optionsButtonText={optionsButtonText}
          additionalOptions={additionalOptions}
          deleteMenuItemText={deleteMenuItemText}
          itemComponent={itemComponent}
          customNameField={customNameField}
        />
      )}

      {/* Footer Navigation */}
      <Footer
        hasItems={items.length > 0}
        isOptional={isOptional}
        backButtonText={backButtonText}
        saveExitButtonText={saveExitButtonText}
        continueButtonText={continueButtonText}
        skipStepButtonText={skipStepButtonText}
        onBackClick={handleBackClick}
        onSaveExitClick={handleSaveExitClick}
        onContinueClick={handleContinueClick}
        onSkipClick={handleSkipClick}
        hideContinueButton={hideContinueButton}
      />

      {/* Item Add/Edit Modal - Only render if generalForm is provided */}
      {shouldUseModal && generalForm && (
        <ItemModal
          isOpen={isOpen}
          onClose={handleCloseModal}
          title={modalTitle || title}
          modalSize={modalSize}
          itemState={itemState}
          items={items}
          generalForm={generalForm}
          onSave={handleSaveItem}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteDialog
        isOpen={isDeleteAlertOpen}
        onClose={closeDeleteAlert}
        onConfirm={handleConfirmDelete}
        title={deleteAlertTitle}
        message={deleteAlertMessage}
        cancelText={deleteAlertCancelText}
        confirmText={deleteAlertConfirmText}
      />
    </Box>
  );
};

export default DynamicCollectionSection;
