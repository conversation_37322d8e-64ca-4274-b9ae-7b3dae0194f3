import {
  <PERSON>,
  Flex,
  Spinner,
  Tab,
  <PERSON><PERSON><PERSON><PERSON>,
  TabPanel,
  TabPanels,
  Tabs,
  Tag,
  Text,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import {
  LegalEntityAssignments,
  OrganizationDetails,
  PlanDesign,
  useSaveChangeRequestHandler,
} from 'apps/admin-portal/components/benAdmin';
import React, { useContext, useState } from 'react';

import {
  getModeFromNavigationConstant,
  PLAN_DESIGN_LIST_ITEM,
  PLAN_DESIGN_MODE,
} from '../../../organization/components/ChangeRequestIntake/Navigation/navigationConstants';
import { getNavigationConstantFromUIContext } from '../../../organization/components/ChangeRequestIntake/Navigation/uiContextEnum';
import { useValidationErrorToast } from '../../../organization/components/ChangeRequestIntake/Validations/useValidationErrorToast';
import { ValidationContext } from '../../../organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { TabSectionProps } from '../../Models/types';
import { useFormCompletionTracking } from '../Form/components/hooks/Completion/useFormCompletion';
import { SimpleDropdown } from '../Form/components/SimpleDropdown';
import PublishConfirmModal from './PublishModal';

// Fields to check for non-null values in ancillaries
const DATA_FIELDS = [
  'effective_date',
  'expiration_date',
  'eligibility_export_ind',
  'pharmacy_claim_export_ind',
  'notes',
];

// Helper: Filter out plan design ancillaries with empty/null required fields
function cleanPlanDesignAncillaries(planDesigns: PlanDesign[]): PlanDesign[] {
  return planDesigns.map((planDesign: PlanDesign) => {
    if (!Array.isArray(planDesign.plan_design_ancillaries)) return planDesign;

    const filteredAncillaries = planDesign.plan_design_ancillaries.filter(
      (anc: any) => {
        return DATA_FIELDS.some(
          (key) =>
            anc[key] !== null && anc[key] !== undefined && anc[key] !== ''
        );
      }
    );

    return {
      ...planDesign,
      plan_design_ancillaries: filteredAncillaries,
    };
  });
}

// Helper: Filter out legal entity assignments with null phone numbers when key fields are populated
function cleanLegalEntityAssignments(
  assignments: LegalEntityAssignments[]
): LegalEntityAssignments[] {
  const hasValue = (value: any) =>
    value !== null && value !== undefined && value !== '';

  return assignments.filter((assignment: LegalEntityAssignments) => {
    const phones = assignment?.source_legal_entity?.phones;
    const role = assignment?.role;

    // If key fields all have values, phone number must also have a value
    const keyFieldsPopulated =
      hasValue(phones?.is_primary) &&
      hasValue(role?.allow_multiple_ind) &&
      hasValue(role?.name);

    return !keyFieldsPopulated || hasValue(phones?.phone_number);
  });
}

// Helper: Clean form data before publishing
function cleanForPublish(formData: OrganizationDetails): OrganizationDetails {
  if (!formData?.plan) return formData;

  let cleanedPlan = { ...formData.plan };

  // Clean plan design ancillaries
  if (formData.plan.plan_designs) {
    cleanedPlan = {
      ...cleanedPlan,
      plan_designs: cleanPlanDesignAncillaries(formData.plan.plan_designs),
    };
  }

  // Clean legal entity assignments
  if (Array.isArray(formData.plan.legal_entity_assignment)) {
    cleanedPlan = {
      ...cleanedPlan,
      legal_entity_assignment: cleanLegalEntityAssignments(
        formData.plan.legal_entity_assignment
      ),
    };
  }

  return {
    ...formData,
    plan: cleanedPlan,
  };
}

const TabSection: React.FC<TabSectionProps> = ({
  tabs,
  isGuided = false,
  formMethods,
  onUpdateActiveItem,
}) => {
  const { overallCompletion } = useFormCompletionTracking();
  const [isPublishing, setIsPublishing] = useState(false);

  // This component can also exist outside of the ValidationProvider (on the OrganizationView screen)
  const validationContext = useContext(ValidationContext);

  const {
    isOpen: isPublishModalOpen,
    onOpen: onPublishModalOpen,
    onClose: onPublishModalClose,
  } = useDisclosure();
  const toast = useToast();
  const publishHandler = useSaveChangeRequestHandler(
    formMethods || ({} as any),
    true
  );
  const saveHandler = useSaveChangeRequestHandler(
    formMethods || ({} as any),
    false,
    true
  );
  const isDirty = formMethods?.formState.isDirty || false;

  // Use the custom hook
  const showValidationErrorToast = useValidationErrorToast({
    onUpdateActiveItem,
  });

  // Helper function to format navigation constant to readable name
  const formatNavigationName = (navigationConstant: string): string => {
    return navigationConstant
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const renderHeader = () => (
    <Box
      display="flex"
      flexDirection="row"
      justifyContent="space-between"
      alignItems="center"
      mb={4}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Text fontSize="20px" fontWeight="bold" mr={4}>
          Plan Design
        </Text>
        <Box display="flex">
          {overallCompletion === 100 ? (
            <Tag colorScheme="green" size="sm">
              Published
            </Tag>
          ) : (
            <Tag colorScheme="orange" size="sm">
              In Progress
            </Tag>
          )}
        </Box>
      </Box>
      <SimpleDropdown
        placeholder="Actions"
        optionsMap={{
          'Publish Plan Design': 'Publish Plan Design',
        }}
        onChange={(selectedKey: string) => {
          if (selectedKey === 'Publish Plan Design') {
            onPublishModalOpen();
          }
        }}
        value=""
        width="200px"
      />
    </Box>
  );

  return (
    <Tabs variant="enclosed" mt={12}>
      <TabList display="inline-flex" borderBottom="2px solid #008750">
        {tabs.map((tab, index) => (
          <Box
            key={tab.label || index}
            borderTopLeftRadius="2xl"
            borderTopRightRadius="2xl"
            bg="white"
            ml={index > 0 ? 2 : 0}
          >
            <Tab
              _selected={{ color: 'white', bg: '#008750' }}
              textColor="#008750"
              borderTopLeftRadius="2xl"
              borderTopRightRadius="2xl"
            >
              <Text fontSize="md" fontWeight="normal">
                {tab.label}
              </Text>
            </Tab>
          </Box>
        ))}
      </TabList>

      <TabPanels mt={0.5}>
        {tabs.map((tab, index) => (
          <Box
            bg="white"
            key={tab.label || index}
            borderBottomLeftRadius="md"
            borderBottomRightRadius="md"
            borderTopRightRadius="md"
          >
            <TabPanel p={4}>
              {' '}
              {index === 0 && isGuided && renderHeader()}
              {tab?.component}
            </TabPanel>
          </Box>
        ))}
      </TabPanels>

      {/* Loading Overlay */}
      {isPublishing && (
        <Box
          position="fixed"
          top={0}
          left={0}
          right={0}
          bottom={0}
          bg="rgba(0, 0, 0, 0.5)"
          zIndex={9999}
          display="flex"
          alignItems="center"
          justifyContent="center"
        >
          <Flex
            direction="column"
            alignItems="center"
            bg="white"
            p={8}
            borderRadius="lg"
            boxShadow="lg"
          >
            <Spinner size="xl" color="#008750" mb={4} />
            <Text fontSize="lg" fontWeight="medium" color="gray.700">
              Publishing Plan Design...
            </Text>
          </Flex>
        </Box>
      )}

      <PublishConfirmModal
        isOpen={isPublishModalOpen}
        onClose={onPublishModalClose}
        onPublish={async () => {
          if (!formMethods) return;

          setIsPublishing(true);
          const formData = formMethods.getValues();

          try {
            const cleanedFormData = cleanForPublish(formData);
            await publishHandler(cleanedFormData);

            toast({
              title: 'Plan Design published successfully',
              status: 'success',
              duration: 5000,
              isClosable: true,
            });

            onPublishModalClose();
          } catch (error: any) {
            validationContext?.refetch();
            saveHandler(formData);

            // Enhanced error handling with navigation links
            const errorMessage =
              error?.response?.data?.message ||
              'An unexpected error occurred while publishing. Please try again.';

            // Extract failed validation results
            const failedResults = error?.response?.data?.results;
            const navigationLinks: Array<{
              name: string;
              navigationConstant: string;
              mode: string;
            }> = [];
            const planDesignErrors: string[] = [];

            if (failedResults && typeof failedResults === 'object') {
              // Filter out results that don't have 'failed' status
              Object.entries(failedResults).forEach(
                ([key, result]: [string, any]) => {
                  if (result?.status === 'failed' && result?.ui_context_ind) {
                    const navigationConstant =
                      getNavigationConstantFromUIContext(result.ui_context_ind);
                    if (navigationConstant) {
                      const mode =
                        getModeFromNavigationConstant(navigationConstant);

                      // Group plan design errors together
                      if (mode === 'plan-design') {
                        const formattedName =
                          formatNavigationName(navigationConstant);
                        if (!planDesignErrors.includes(formattedName)) {
                          planDesignErrors.push(formattedName);
                        }
                      } else {
                        // For non-plan-design errors, add individual links
                        navigationLinks.push({
                          name: formatNavigationName(navigationConstant),
                          navigationConstant,
                          mode,
                        });
                      }
                    }
                  }
                }
              );

              // Add a single "Plan Design" link if there are plan design errors
              if (planDesignErrors.length > 0) {
                navigationLinks.push({
                  name: 'Plan Design',
                  navigationConstant: PLAN_DESIGN_LIST_ITEM,
                  mode: PLAN_DESIGN_MODE,
                });
              }
            }

            // Create toast with navigation links if available
            if (navigationLinks.length > 0) {
              showValidationErrorToast(errorMessage, navigationLinks);
            }
          } finally {
            setIsPublishing(false);
          }
        }}
        isPublishDisabled={!isDirty}
      />
    </Tabs>
  );
};

export default TabSection;
