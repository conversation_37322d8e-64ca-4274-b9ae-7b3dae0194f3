// AutoTable.styles.ts

import { DatePickerStyles } from '@next/shared/constants';

// styles.ts
export const bopTablestyles = {
  outerBox: {
    border: '1px solid #E0E0E0',
    borderRadius: '12px',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    padding: '16px',
    width: '100%',
    maxWidth: '100%',
    overflow: 'hidden',
  },
  table: {
    tableLayout: 'fixed',
    width: '100%',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    borderCollapse: 'collapse',
  },
  thead: {
    position: 'relative' as const,
    _after: {
      content: '""',
      position: 'absolute',
      bottom: '0',
      left: '0',
      height: '2.5px',
      width: '100%',
      background: 'linear-gradient(to right, #7BCC03, #00A361)',
    },
  },
  th: function (compact = false, customStyle = {}) {
    return {
      color: '#003366',
      fontWeight: 'bold',
      textAlign: 'left',
      fontSize: compact ? 'xs' : 'sm',
      padding: compact ? '6px 4px' : '10px 8px',
      borderBottom: 'none',
      textTransform: 'none',
      userSelect: 'none',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      cursor: 'pointer',
      transition: 'background-color 0.2s ease',
      _hover: {
        backgroundColor: 'rgba(0, 0, 0, 0.05)',
      },
      _active: {
        backgroundColor: 'rgba(0, 0, 0, 0.1)',
        outline: 'none',
        userSelect: 'none',
      },

      ...customStyle,
    };
  },
  tbody: {
    position: 'relative' as const,
  },
  tbodyRow: (
    rowIndex: number,
    isClickable: boolean,
    disableRowClick: boolean,
    compact = false,
    customStyle = {}
  ) => ({
    bgColor: rowIndex % 2 === 0 ? '#f5faff' : 'white',
    cursor: isClickable && !disableRowClick ? 'pointer' : 'default',
    _hover:
      isClickable && !disableRowClick
        ? {
            bgColor: '#e6f7ff',
            textDecoration: 'underline',
          }
        : undefined,
    _active:
      isClickable && !disableRowClick ? { bgColor: '#a6e0ff' } : undefined,
    transition: 'background-color 0.3s',
    ...customStyle,
  }),
  td: (compact = false) => ({
    textAlign: 'left' as const,
    fontSize: compact ? 'xs' : 'sm',
    padding: compact ? '6px 4px' : '10px 8px',
    borderBottom: '1px solid #E0E0E0',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    maxWidth: compact ? '120px' : '180px',
  }),
  sortIcon: {
    ml: 2,
    fontSize: 'sm',
    transition: 'all 0.2s',
  },
  // Pagination specific styles
  pagination: {
    container: (compact = false) => ({
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
      padding: compact ? '8px 4px 0' : '16px 0 0',
      fontSize: compact ? 'xs' : 'sm',
    }),
    button: (compact = false) => ({
      minWidth: compact ? '24px' : '32px',
      height: compact ? '24px' : '32px',
      fontSize: compact ? 'xs' : 'sm',
      borderRadius: 'md',
    }),
    select: (compact = false) => ({
      height: compact ? '24px' : '32px',
      fontSize: compact ? 'xs' : 'sm',
      borderRadius: 'md',
      minWidth: compact ? '80px' : '100px',
    }),
  },
};

export const bopTemplateStyles = {
  fieldItem: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',
    border: '0 solid gray.200',
    borderBottomWidth: '1px',
    gap: 2,
    minHeight: 'fit-content',
  },
  label: {
    flex: 'none',
    bg: 'gray.50',
    height: '100%',
    color: 'gray.700',
    padding: 2,
    mb: 0,
    display: 'flex',
    alignItems: 'center',
    fontSize: 'sm',
    wordBreak: 'keep-all',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    minHeight: 'auto',
  },
  value: {
    flex: '1',
    padding: 2,
    fontSize: 'sm',
    minWidth: 0,
  },
  container: {
    pb: 8,
    width: '100%',
    maxWidth: '100%',
    overflow: 'hidden',
  },
  titleContainer: {
    display: 'flex',
    alignItems: 'center',
    mb: 5,
  },
  select: {
    color: 'gray.800',
  },
  input: {
    color: 'gray.800',
    fontSize: 'inherit',
  },
  title: {
    fontSize: 'lg',
    fontWeight: 'bold',
    mt: 10,
  },
  editButton: {
    size: 'sm',
    variant: 'outline',
    borderRadius: 'md',
    height: '30px',
    fontWeight: 'bold',
    ml: 8,
    mt: 10,
    borderColor: '#349f73',
    color: '#349f73',
    px: 1.5,
    minWidth: 'auto',
  },
  buttonContainer: {
    mt: 4,
    display: 'flex',
    justifyContent: 'flex-end',
    gap: 4,
  },
  saveButton: {
    colorScheme: 'green',
    size: 'sm',
  },
  cancelButton: {
    colorScheme: 'red',
    size: 'sm',
  },
};

export const dateStyles = DatePickerStyles();

export const searchBarStyles = {
  formControl: {
    width: '100%',
    mb: 4,
  },
  label: {
    fontWeight: 'medium',
    fontSize: '15px',
    color: '#333',
  },
  hintIcon: {
    color: '#555',
    fontSize: 'sm',
  },
  inputContainer: {
    align: 'center',
    justify: 'space-between',
    w: '100%',
    gap: 4,
    mb: 4,
  },
  input: {
    bg: '#f8f9fa',
    border: '1px solid #dcdcdc',
    borderRadius: '4px',
    _focus: { borderColor: '#28a745', boxShadow: '0 0 4px #28a745' },
    fontSize: 'sm',
    color: '#333',
    px: 4,
    h: '2.2rem',
    flex: 1,
    minW: '200px',
  },
  button: {
    h: '2.1rem',
    w: '7rem',
    size: 'sm',
    bg: '#008750',
    color: 'white',
    fontSize: 'sm',
    px: 6,
    borderRadius: '8px',
    _hover: { bg: '#00A060' },
    _active: { bg: '#006B40' },
    flexShrink: 0,
  },
  quickFilterContainer: {
    display: 'flex',
    flexWrap: 'wrap' as const,
    gap: 4,
  },
  quickFilterButton: {
    size: 'sm',
    px: 4,
    py: 1.5,
    bg: 'white',
    border: '1px solid #38a169',
    color: '#38a169',
    fontWeight: 'medium',
    fontSize: '14px',
    _hover: { bg: '#f0fff4' },
    _active: { bg: '#f0fff4' },
  },
  activeQuickFilterButton: {
    bg: '#38a169',
    color: 'white',
    border: '1px solid #38a169',
    _hover: { bg: '#2e8b57' },
    _active: { bg: '#2e8b57' },
  },
};

export const overlayStyles = {
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  backgroundColor: 'rgba(245, 245, 245, 0.7)',
  zIndex: 10,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
};
