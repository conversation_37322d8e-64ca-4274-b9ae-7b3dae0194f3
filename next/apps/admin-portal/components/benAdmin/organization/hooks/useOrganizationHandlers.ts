import { isEqual } from 'lodash';
import { useEffect, useRef } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';

import { OrganizationDetails } from '../../Models/interfaces';

interface UseOrganizationHandlersProps {
  /**
   * The default organization details used to pre-populate the form
   * on the Organization View page.
   */
  organizationDetails: OrganizationDetails;
}

export const useOrganizationHandlers = ({
  organizationDetails,
}: UseOrganizationHandlersProps) => {
  // Memoize the default form data
  //   const formData = useMemo(() => organizationDetails, [organizationDetails]);

  // Initialize react-hook-form with stable default values.
  const formMethods: UseFormReturn<any> = useForm({
    defaultValues: organizationDetails,
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  // Store the previous form data to avoid unnecessary resets.
  const prevExternalDataRef = useRef(organizationDetails);

  useEffect(() => {
    // If the new external data is truly different from the last external data, reset.
    if (!isEqual(prevExternalDataRef.current, organizationDetails)) {
      formMethods.reset(organizationDetails);
      prevExternalDataRef.current = organizationDetails;
    }
  }, [organizationDetails, formMethods]);

  return {
    formMethods,
  };
};
