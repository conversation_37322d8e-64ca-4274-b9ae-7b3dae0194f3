import {
  Box,
  Center,
  Flex,
  Spinner,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import {
  ChangeRequest,
  PlanDesign,
  PlanDesignAncillary,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { useCallback, useMemo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { PLAN_DESIGNS_BASE_PATH } from '../../../../Tabs/PlanDesign/PlanDesign/Config/coreConfig';
import {
  generateProductConfigs,
  organizePlanDesignAncillaries,
} from '../../../../Tabs/PlanDesign/ProductsAndServices/Config/productConfig';
import { HelpCenter } from '../../../IntakeComponents/HelpCenter/HelpCenter';
import { ADD_ON_PRODUCTS_ITEM } from '../../../Navigation/navigationConstants';
import { useValidateByPageFromContext } from '../../../Validations/ValidationContext';
import { GenericProductForm } from './genericProductForm';

interface AddOnProductsProps {
  planId?: number;
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

// Helper to create validationResults with product types as errors for add-on products
function getProductTypeValidationResults(
  validationData: any,
  formMethods: any
) {
  const results = validationData?.results?.['24'] ?? {};
  const planDesigns = formMethods.getValues('plan.plan_designs') || [];
  const process = (items: any[]) => {
    const counts: Record<string, number> = {};
    (items ?? []).forEach((item: any) => {
      const match = item.field?.match(
        /^plan_designs\.(\d+)\.plan_design_ancillaries\.(\d+)\./
      );
      if (!match) return;
      const planIdx = Number(match[1]);
      const ancillaryIdx = Number(match[2]);
      const plan = planDesigns[planIdx];
      const ancillary = plan?.plan_design_ancillaries?.[ancillaryIdx];
      const productType = ancillary?.product_type || 'Unknown Product Type';
      counts[productType] = (counts[productType] || 0) + 1;
    });
    // Return as ValidationRule[]
    return Object.entries(counts).map(([productType, count], idx) => ({
      plan_validation_rule_id: idx,
      rule_name: productType,
      validation_message: `${count} error${
        count !== 1 ? 's' : ''
      } for this product type`,
      field: productType,
    }));
  };
  return {
    errors: process(results.errors),
    warnings: process(results.warnings),
  };
}

const AddOnProducts = ({
  planId,
  formMethods,
  onUpdateActiveItem,
}: AddOnProductsProps) => {
  const [newAncillaryIndices, setNewAncillaryIndices] = useState<number[]>([]);
  const [showProductForm, setShowProductForm] = useState(false);
  const changeRequest: ChangeRequest | null = JSON.parse(
    sessionStorage.getItem('selectedChangeRequest') || 'null'
  );

  const requestId = changeRequest?.change_request_id || '';
  const {
    isOpen: isOverlayVisible,
    onOpen: showOverlay,
    onClose: hideOverlay,
  } = useDisclosure();
  const toast = useToast();
  const { useApiQuery, useApiMutation } = useBenAdmin();

  // Watch the ancillaries data (use planDesigns[0])
  const watchedAncillaries = formMethods?.watch(
    `${PLAN_DESIGNS_BASE_PATH}.0.plan_design_ancillaries`
  ) as PlanDesignAncillary[];

  const {
    refetch,
    validationData,
    isLoading: isValidationLoading,
  } = useValidateByPageFromContext(ADD_ON_PRODUCTS_ITEM);

  const { helpCenterData, isFetching } = useHelpCenter(ADD_ON_PRODUCTS_ITEM);

  const { createContinueHandler } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  // Create a Set of disabled product IDs from watched ancillaries
  const disabledProductIds = useMemo(() => {
    const safeAncillaries = Array.isArray(watchedAncillaries)
      ? watchedAncillaries
      : [];
    return new Set(
      safeAncillaries
        .map((ancillary) => ancillary.product_id)
        .filter((id): id is number => id !== null)
    );
  }, [watchedAncillaries]);

  const { addOnProduct, isLoading } = useApiQuery([
    {
      key: 'addOnProduct',
      pathParams: { id: planId },
      options: {
        staleTime: 5 * 60 * 1000,
        cacheTime: 10 * 60 * 1000,
      },
    },
  ]);

  //   // Use mutation for creating plan ancillaries
  //   const { mutateAsync: createPlanAncillaries } = useApiMutation(
  //     'planDesignAncillary',
  //     'POST'
  //   );

  const { mutateAsync: getPlanDesigns } = useApiMutation(
    'changeContent',
    'POST'
  );

  const [isProcessing, setIsProcessing] = useState(false);

  // Use product type summary as validationResults for add-on products
  const productTypeValidationResults = useMemo(
    () => getProductTypeValidationResults(validationData, formMethods),
    [validationData, formMethods]
  );

  // Helper to set the tab param in the URL
  const setTabByProductType = (productType: string) => {
    const kebab = productType
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
    if (typeof onUpdateActiveItem === 'function') {
      onUpdateActiveItem(kebab || ADD_ON_PRODUCTS_ITEM);
    }
  };

  const handleContinue = useCallback(
    async (selectedItems: any) => {
      if (!planId || !formMethods) return;

      try {
        setIsProcessing(true);
        showOverlay();
        const response = await getPlanDesigns({
          pathParams: { id: requestId },
          ...selectedItems,
        });
        const planDesigns = response.plan_designs || [];

        if (planDesigns.length === 0) {
          toast({
            title: 'No Plan Designs Detected',
            description:
              'We were not able to create these add-on products because no plan designs were detected.',
            status: 'error',
            duration: 6000,
            isClosable: true,
            position: 'top',
          });
          return;
        }

        // Get new ancillaries from backendPlanDesigns[0]
        const backendAncillaries = Array.isArray(
          planDesigns[0]?.plan_design_ancillaries
        )
          ? planDesigns[0].plan_design_ancillaries
          : [];

        // console.log('backendAncillaries', backendAncillaries);

        // Get current plan designs from form to identify existing product IDs
        const currentPlanDesigns =
          formMethods.getValues(PLAN_DESIGNS_BASE_PATH) || [];

        // Get all existing product IDs from the current form
        const existingProductIds = new Set();
        currentPlanDesigns.forEach((formPlan: any) => {
          const formAncillaries = Array.isArray(
            formPlan.plan_design_ancillaries
          )
            ? formPlan.plan_design_ancillaries
            : [];
          formAncillaries.forEach((ancillary: any) => {
            if (ancillary.product_id) {
              existingProductIds.add(ancillary.product_id);
            }
          });
        });

        // Find indices of new ancillaries in the backend response
        const firstPlanNewIndices: number[] = [];
        backendAncillaries.forEach((ancillary: any, index: number) => {
          if (!existingProductIds.has(ancillary.product_id)) {
            firstPlanNewIndices.push(index);
          }
        });

        // Replace the entire plan designs with the backend response
        formMethods.setValue(PLAN_DESIGNS_BASE_PATH, planDesigns, {
          shouldDirty: true,
          shouldTouch: true,
          shouldValidate: true,
        });

        setNewAncillaryIndices(firstPlanNewIndices);
        setShowProductForm(true);

        if (planDesigns.length > 0 && firstPlanNewIndices.length > 0) {
          const planDesign = planDesigns[0] as PlanDesign;
          const planDesignAncillaries =
            planDesign.plan_design_ancillaries as PlanDesignAncillary[];
          const productBuckets = organizePlanDesignAncillaries(
            planDesignAncillaries
          );
          const productConfigs = generateProductConfigs(productBuckets);
          const firstProductItemId = productConfigs[0]?.tabId;
          if (firstProductItemId) {
            createContinueHandler(ADD_ON_PRODUCTS_ITEM, firstProductItemId);
          }
        }
      } catch (error) {
        console.error('Error creating plan ancillaries:', error);
      } finally {
        setIsProcessing(false);
        hideOverlay();
      }
    },
    [
      planId,
      formMethods,
      showOverlay,
      getPlanDesigns,
      requestId,
      toast,
      createContinueHandler,
      hideOverlay,
    ]
  );

  const handleSave = useCallback((data: PlanDesignAncillary) => {
    // All ancillaries are completed, you can handle the final save here
    console.log('All ancillaries completed:', data);
    // You might want to navigate away or show a success message
  }, []);

  if (isLoading) {
    return (
      <Box mb={6}>
        <Center py={10}>
          <Spinner size="xl" color="green.500" thickness="4px" />
        </Center>
      </Box>
    );
  }

  if (showProductForm) {
    if (!formMethods) {
      console.error('Form methods not available');
      return null;
    }

    return (
      <GenericProductForm
        formMethods={formMethods}
        productType="add-on"
        isModal={false}
        newAncillaryIndices={newAncillaryIndices}
        onSave={handleSave}
        onCancel={() => setShowProductForm(false)}
        onUpdateActiveItem={onUpdateActiveItem}
      />
    );
  }

  return (
    <>
      {isOverlayVisible && (
        <Box
          position="fixed"
          top={0}
          left={0}
          right={0}
          bottom={0}
          bg="blackAlpha.300"
          zIndex={1000}
          onClick={(e) => e.preventDefault()}
        />
      )}
      <Flex justify="space-between">
        <GenericForm
          formName="Which products are you adding to this plan?"
          formDescription=""
          subCategories={[]}
          multiSelectData={{
            data: addOnProduct,
            label: 'Select product add-ons',
            tag: 'product_class_name',
            disabledItems: disabledProductIds,
          }}
          onContinue={handleContinue}
          formMethods={formMethods}
          showButtons={true}
          showSubmit={false}
          isProcessing={isProcessing || isValidationLoading}
        />
        <HelpCenter
          validationResults={productTypeValidationResults}
          helpContent={helpCenterData}
          isLoading={isFetching}
          setTabByProductType={setTabByProductType}
        />
      </Flex>
    </>
  );
};

export default AddOnProducts;
