import {
  CLINICAL_DESIGN_MODE,
  extractSuffix,
  filterFeatures,
  generateFeatureTabId,
  groupFeaturesByPrefixPattern,
  OrganizationDetails,
} from 'apps/admin-portal/components/benAdmin';
import { FormSections } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/Completion/types';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { createRoot, Root } from 'react-dom/client';

import { useDynamicFeatureFields } from '../DynamicForm/useDynamicFeatureFields';

// Component to handle a single feature's fields
const FeatureSection: React.FC<{
  feature: any;
  groupName?: string;
  currentDetails: Partial<OrganizationDetails>;
  formMethods: any; // Add formMethods prop
  onSectionReady: (section: FormSections['sections'][number]) => void;
}> = ({ feature, groupName, currentDetails, formMethods, onSectionReady }) => {
  const { subCategories } = useDynamicFeatureFields(
    feature,
    currentDetails,
    formMethods
  );

  useEffect(() => {
    const featureId = generateFeatureTabId(feature);
    let title =
      feature.label || feature.name || `Feature ${feature.product_feature_id}`;

    if (groupName) {
      const suffix = extractSuffix(title, groupName);
      if (suffix.trim()) {
        title = suffix;
      }
    }

    onSectionReady({
      id: featureId,
      title,
      relatedSections: subCategories,
      isCollection: false,
    });
  }, [feature, groupName, subCategories, onSectionReady]);

  return null;
};

// Component to manage all features
const FeaturesManager: React.FC<{
  featureGroups: ReturnType<typeof groupFeaturesByPrefixPattern>;
  currentDetails: Partial<OrganizationDetails>;
  formMethods: any; // Add formMethods prop
  onSectionsReady: (sections: FormSections['sections']) => void;
}> = ({ featureGroups, currentDetails, formMethods, onSectionsReady }) => {
  const [sections, setSections] = useState<FormSections['sections']>([]);

  const handleSectionReady = useCallback(
    (section: FormSections['sections'][number]) => {
      setSections((prev) => {
        const newSections = [...prev];
        const index = newSections.findIndex((s) => s.id === section.id);
        if (index >= 0) {
          newSections[index] = section;
        } else {
          newSections.push(section);
        }
        return newSections;
      });
    },
    []
  );

  useEffect(() => {
    onSectionsReady(sections);
  }, [sections, onSectionsReady]);

  return (
    <>
      {Object.entries(featureGroups).map(([groupName, groupFeatures]) =>
        groupFeatures.map((feature) => (
          <FeatureSection
            key={generateFeatureTabId(feature)}
            feature={feature}
            groupName={groupFeatures.length > 1 ? groupName : undefined}
            currentDetails={currentDetails}
            formMethods={formMethods}
            onSectionReady={handleSectionReady}
          />
        ))
      )}
    </>
  );
};

export function useClinicalDesignsSections(
  currentDetails: Partial<OrganizationDetails>
): FormSections {
  const [sections, setSections] = useState<FormSections['sections']>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const rootRef = useRef<Root | null>(null);
  const containerRef = useRef<HTMLElement | null>(null);
  const cleanupTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const isUnmountingRef = useRef(false);

  // Filter features, excluding Pharmacy Network
  const allFeatures = useMemo(() => {
    if (!currentDetails?.features || !Array.isArray(currentDetails.features)) {
      return [];
    }

    return filterFeatures(currentDetails.features, undefined, [
      'Pharmacy Network',
    ]);
  }, [currentDetails?.features]);

  // Group features by common prefixes
  const featureGroups = useMemo(
    () => groupFeaturesByPrefixPattern(allFeatures),
    [allFeatures]
  );

  // Safe cleanup function that defers unmounting
  const safeCleanup = useCallback(() => {
    if (isUnmountingRef.current) return;

    // Clear any existing timeout
    if (cleanupTimeoutRef.current) {
      clearTimeout(cleanupTimeoutRef.current);
      cleanupTimeoutRef.current = null;
    }

    // Defer the actual cleanup to avoid race conditions
    cleanupTimeoutRef.current = setTimeout(() => {
      try {
        if (rootRef.current) {
          rootRef.current.unmount();
          rootRef.current = null;
        }
        if (containerRef.current && containerRef.current.parentNode) {
          containerRef.current.parentNode.removeChild(containerRef.current);
          containerRef.current = null;
        }
      } catch (error) {
        console.warn('Error during cleanup:', error);
      }
    }, 100); // Increased timeout to ensure render cycle is complete
  }, []);

  // Initialize the root only once
  useEffect(() => {
    if (isInitialized) return;

    // Create container if it doesn't exist
    let container = document.getElementById('feature-sections-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'feature-sections-container';
      container.style.display = 'none';
      document.body.appendChild(container);
    }
    containerRef.current = container;

    // Create root and render
    const root = document.createElement('div');
    container.appendChild(root);
    rootRef.current = createRoot(root);

    // Create a mock formMethods object for the FeaturesManager
    const mockFormMethods = {
      getValues: () => ({}),
      setValue: () => {
        // Mock implementation - no-op
      },
      watch: () => ({}),
    };

    rootRef.current.render(
      <FeaturesManager
        featureGroups={featureGroups}
        currentDetails={currentDetails}
        formMethods={mockFormMethods}
        onSectionsReady={setSections}
      />
    );

    setIsInitialized(true);
  }, [featureGroups, currentDetails, isInitialized]);

  // Update the rendered content when dependencies change
  useEffect(() => {
    if (!isInitialized || !rootRef.current) return;

    // Only update if we have a valid root
    try {
      // Create a mock formMethods object for the FeaturesManager
      const mockFormMethods = {
        getValues: () => ({}),
        setValue: () => {
          // Mock implementation - no-op
        },
        watch: () => ({}),
      };

      rootRef.current.render(
        <FeaturesManager
          featureGroups={featureGroups}
          currentDetails={currentDetails}
          formMethods={mockFormMethods}
          onSectionsReady={setSections}
        />
      );
    } catch (error) {
      console.warn('Error updating root:', error);
    }
  }, [featureGroups, currentDetails, isInitialized]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isUnmountingRef.current = true;
      safeCleanup();
    };
  }, [safeCleanup]);

  return {
    sections,
    id: CLINICAL_DESIGN_MODE,
  };
}
