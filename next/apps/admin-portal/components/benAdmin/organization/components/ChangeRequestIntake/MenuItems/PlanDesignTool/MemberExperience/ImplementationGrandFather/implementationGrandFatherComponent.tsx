'use client';

import { Box } from '@chakra-ui/react';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
import { useValidationErrorsForForm } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useValidationErrorsForForm';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  IMPLEMENTATION_GRANDFATHER_ITEM,
  TRANSITION_FILES_AND_DETAILS_ITEM,
  WELCOME_KIT_AND_LETTERS_ITEM,
} from '../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../Navigation/uiContextEnum';
import { useValidateByPageFromContext } from '../../../../Validations/ValidationContext';
import { useImplementationGrandFatherForm } from './implementationGrandFatherForm';

interface ImplementationGrandFatherComponentProps {
  formMethods: UseFormReturn<OrganizationDetails>;
  onUpdateActiveItem?: (id: string) => void;
}

const ImplementationGrandFatherComponent: React.FC<
  ImplementationGrandFatherComponentProps
> = ({ formMethods, onUpdateActiveItem }) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const { refetch, validationData, isLoading } = useValidateByPageFromContext(
    IMPLEMENTATION_GRANDFATHER_ITEM
  );

  const { helpCenterData } = useHelpCenter(IMPLEMENTATION_GRANDFATHER_ITEM);
  const validationResults =
    validationData?.results[
      getUIContextFromNavigationConstant(
        IMPLEMENTATION_GRANDFATHER_ITEM
      ) as number
    ].validation_results;

  const { createContinueHandler, isContinueLoading } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  const { subCategories } = useImplementationGrandFatherForm(
    currentDetails as Partial<OrganizationDetails>,
    helpCenterData
  );
  //   const submitHandler = useSaveChangeRequestHandler(formMethods);

  const handleContinue = () => {
    createContinueHandler(
      IMPLEMENTATION_GRANDFATHER_ITEM,
      WELCOME_KIT_AND_LETTERS_ITEM
    )();
  };

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(TRANSITION_FILES_AND_DETAILS_ITEM);
    }
  };

  //   const handleSaveAndExit = () => {
  //     submitHandler(formMethods.getValues());
  //   };
  const contextIndex = getUIContextFromNavigationConstant(
    IMPLEMENTATION_GRANDFATHER_ITEM
  );

  useValidationErrorsForForm({
    subCategories,
    formMethods,
    validationData,
    contextIndex,
  });

  // Use continue loading state or validation loading state, whichever is true
  const isProcessing = isContinueLoading || isLoading;
  return (
    <Box display="flex" flexDirection="row" justifyContent="space-between">
      <GenericForm
        formMethods={formMethods}
        formName="Implementation Grandfather"
        formDescription=""
        subCategories={subCategories}
        onContinue={handleContinue}
        // onSaveExit={handleSaveAndExit}
        onBack={handleBack}
        isProcessing={isProcessing}
      />
      <HelpCenter
        helpContent={helpCenterData}
        validationResults={validationResults}
      />
    </Box>
  );
};

export default ImplementationGrandFatherComponent;
