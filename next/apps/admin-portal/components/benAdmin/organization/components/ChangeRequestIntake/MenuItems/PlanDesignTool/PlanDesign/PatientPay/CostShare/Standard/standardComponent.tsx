import {
  UNBREAKABLE_ITEM,
  XML_ITEM,
} from 'apps/admin-portal/components/benAdmin';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import CostShareComponent from '../costShareComponent';

interface StandardComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
  backIndicator?: string;
  continueIndicator?: string;
  title?: string;
  description?: string;
  modalTitle?: string;
  basePath?: string;
  sourceLabel?: string;
}

/**
 * StandardComponent - Uses CostShareComponent for standard cost share tiers
 */
const StandardComponent: React.FC<StandardComponentProps> = (props) => {
  return (
    <CostShareComponent
      {...props}
      title={props.title || 'Cost Share Design'}
      description={
        props.description ||
        'Select the standard cost share tiers that apply to this plan.'
      }
      modalTitle={props.modalTitle || 'Select Standard Cost Share Tiers'}
      prePackagedIndFilter={0}
      continueIndicator={UNBREAKABLE_ITEM}
      backIndicator={XML_ITEM}
    />
  );
};

export default StandardComponent;
