// Constants for main configurations
export const MAIN_MODE = 'main';
export const OVERVIEW_ITEM = 'preview';
export const CONTACTS_ITEM = 'contacts';
export const DOCUMENTS_ITEM = 'documents';
// Constants for client profile configurations
export const CLIENT_PROFILE_MODE = 'client-profile';

export const CLIENT_INFORMATION_ITEM = 'client-information';
export const PHARMACY_BENEFITS_MANAGER_ITEM = 'pharmacy-benefits-manager';
export const IN_HOUSE_PHARMACY_ITEM = 'in-house-pharmacy';
export const IMPLEMENTATION_ITEM = 'implementation';
export const ELIGIBILITY_ITEM = 'eligibility';
export const FSA_HRA_HSA_ITEM = 'fsa-hra-hsa';
export const CLAIMS_COVER_ITEM = 'claims-cover';

// Constants for member experience configurations
export const MEMBER_EXPERIENCE_MODE = 'member-experience';

export const ID_CARDS_ITEM = 'id-cards';
export const TRANSITION_FILES_AND_DETAILS_ITEM = 'transition-files-and-details';
export const WELCOME_KIT_AND_LETTERS_ITEM = 'welcome-kit-and-letters';
export const MEMBER_SERVICES_ITEM = 'member-services';
export const IMPLEMENTATION_GRANDFATHER_ITEM = 'implementation-grand-father';
export const MEMBER_SERVICES_REVIEW_ITEM = 'member-services-review';

// Constants for products and services configurations
export const PRODUCTS_AND_SERVICES_MODE = 'products-and-services';

export const CORE_PRODUCTS_ITEM = 'core-products';
export const PRODUCT_SET_UP_ITEM = 'product-set-up';
export const PHARMACY_NETWORK_ITEM = 'pharmacy-network';
export const RXB_PRODUCTS_ITEM = 'rxb-products';
export const PBM_PRODUCTS_ITEM = 'pbm-products';
export const THIRD_PARTY_PRODUCTS_ITEM = 'third-party-products';
export const PRODUCTS_AND_SERVICES_REVIEW_ITEM = 'products-and-services-review';
export const ADD_ON_PRODUCTS_ITEM = 'add-on-products';

// Constants for plan design configurations
export const PLAN_DESIGN_MODE = 'plan-design';
export const GENERAL_ITEM = 'general';
export const XML_ITEM = 'xml';
export const PHARMACY_ACCUMULATORS_ITEM = 'pharmacy-accumulators';
export const ACCUMULATORS_GENERAL_ITEM = 'pharmacy-accumulators-general';
export const DEDUCTIBLE_ITEM = 'deductible';
export const MAXIMUM_OUT_OF_POCKET_ITEM = 'maximum-out-of-pocket';
export const OTHER_CAP_ITEM = 'other-cap';
export const PATIENT_PAY_ITEM = 'patient-pay';
export const STANDARD_ITEM = 'standard';
export const UNBREAKABLE_ITEM = 'unbreakable';
export const COMPOUND_ITEM = 'compound';
export const DISPENSE_ITEM = 'dispense';
export const PLAN_DESIGN_LIST_ITEM = 'plan-design-list';
export const PLAN_DESIGN_REVIEW_ITEM = 'plan-design-review';

// Constants for clinical design configurations
export const CLINICAL_DESIGN_MODE = 'clinical-design';
export const FORMULARY_SETUP_ITEM = 'formulary-set-up';
export const FORMULARY_INCLUSION_ITEM = 'formulary-inclusion';
export const FORMULARY_EXCLUSION_ITEM = 'formulary-exclusion';
export const DRUG_CLASS_ITEM = 'drug-class';
export const CONDITION_SPECIFIC_ITEM = 'condition-specific';
export const REGULATORY_REQUIREMENT_ITEM = 'regulatory-requirement';
export const DIABETICS_COVERAGE_ITEM = 'diabetics-coverage';
export const UTILIZATION_MANAGEMENT_ITEM = 'utilization-management';
export const INDEPENDENT_REVIEW_ITEM = 'independent-review';
export const AFFORDABLE_CARE_ITEM = 'affordable-care';
export const PREVENTATIVE_LIST_AND_VACCINE_ITEM =
  'preventative-list-and-vaccine';
export const BUNDLE_ITEM = 'bundle';
export const PRIOR_AUTHORIZATION_ITEM = 'prior-authorization';
export const QUANTITY_LIMIT_ITEM = 'quantity-limit';
export const STEP_THERAPY_ITEM = 'step-therapy';

/**
 * Helper function to get the mode from a navigation constant
 */
export const getModeFromNavigationConstant = (
  navigationConstant: string
): string => {
  // Client Profile items
  if (
    [
      CLIENT_INFORMATION_ITEM,
      PHARMACY_BENEFITS_MANAGER_ITEM,
      IN_HOUSE_PHARMACY_ITEM,
      IMPLEMENTATION_ITEM,
      ELIGIBILITY_ITEM,
      CLAIMS_COVER_ITEM,
      FSA_HRA_HSA_ITEM,
    ].includes(navigationConstant)
  ) {
    return CLIENT_PROFILE_MODE;
  }

  // Member Experience items
  if (
    [
      ID_CARDS_ITEM,
      TRANSITION_FILES_AND_DETAILS_ITEM,
      WELCOME_KIT_AND_LETTERS_ITEM,
      MEMBER_SERVICES_ITEM,
    ].includes(navigationConstant)
  ) {
    return MEMBER_EXPERIENCE_MODE;
  }

  // Plan Design items - navigate to plan-design mode instead of specific tabs
  if (
    [
      GENERAL_ITEM,
      XML_ITEM,
      ACCUMULATORS_GENERAL_ITEM,
      DEDUCTIBLE_ITEM,
      MAXIMUM_OUT_OF_POCKET_ITEM,
      OTHER_CAP_ITEM,
      STANDARD_ITEM,
      UNBREAKABLE_ITEM,
      COMPOUND_ITEM,
      DISPENSE_ITEM,
      PLAN_DESIGN_LIST_ITEM,
      PLAN_DESIGN_REVIEW_ITEM,
    ].includes(navigationConstant)
  ) {
    return PLAN_DESIGN_MODE;
  }

  // Products and Services items
  if (
    [
      PRODUCT_SET_UP_ITEM,
      PHARMACY_NETWORK_ITEM,
      RXB_PRODUCTS_ITEM,
      PBM_PRODUCTS_ITEM,
      THIRD_PARTY_PRODUCTS_ITEM,
      ADD_ON_PRODUCTS_ITEM,
      PRODUCTS_AND_SERVICES_REVIEW_ITEM,
    ].includes(navigationConstant)
  ) {
    return PRODUCTS_AND_SERVICES_MODE;
  }

  return 'main';
};

/**
 * Helper function to get navigation constants for a given section
 */
export const getSectionNavigationConstants = (section: string): string[] => {
  switch (section) {
    case CLIENT_PROFILE_MODE:
      return [
        CLIENT_INFORMATION_ITEM,
        PHARMACY_BENEFITS_MANAGER_ITEM,
        IN_HOUSE_PHARMACY_ITEM,
        IMPLEMENTATION_ITEM,
        ELIGIBILITY_ITEM,
        CLAIMS_COVER_ITEM,
        FSA_HRA_HSA_ITEM,
      ];
    case MEMBER_EXPERIENCE_MODE:
      return [
        ID_CARDS_ITEM,
        TRANSITION_FILES_AND_DETAILS_ITEM,
        WELCOME_KIT_AND_LETTERS_ITEM,
        MEMBER_SERVICES_ITEM,
        MEMBER_SERVICES_REVIEW_ITEM,
      ];
    case PRODUCTS_AND_SERVICES_MODE:
      return [
        PRODUCT_SET_UP_ITEM,
        PHARMACY_NETWORK_ITEM,
        RXB_PRODUCTS_ITEM,
        PBM_PRODUCTS_ITEM,
        THIRD_PARTY_PRODUCTS_ITEM,
        ADD_ON_PRODUCTS_ITEM,
        PRODUCTS_AND_SERVICES_REVIEW_ITEM,
      ];
    case PLAN_DESIGN_MODE:
      return [
        GENERAL_ITEM,
        XML_ITEM,
        PHARMACY_ACCUMULATORS_ITEM,
        ACCUMULATORS_GENERAL_ITEM,
        DEDUCTIBLE_ITEM,
        MAXIMUM_OUT_OF_POCKET_ITEM,
        OTHER_CAP_ITEM,
        PATIENT_PAY_ITEM,
        STANDARD_ITEM,
        UNBREAKABLE_ITEM,
        COMPOUND_ITEM,
        DISPENSE_ITEM,
        PLAN_DESIGN_REVIEW_ITEM,
      ];
    case CLINICAL_DESIGN_MODE:
      return [
        FORMULARY_SETUP_ITEM,
        FORMULARY_INCLUSION_ITEM,
        FORMULARY_EXCLUSION_ITEM,
        DRUG_CLASS_ITEM,
        CONDITION_SPECIFIC_ITEM,
        REGULATORY_REQUIREMENT_ITEM,
        DIABETICS_COVERAGE_ITEM,
        UTILIZATION_MANAGEMENT_ITEM,
        INDEPENDENT_REVIEW_ITEM,
        AFFORDABLE_CARE_ITEM,
        PREVENTATIVE_LIST_AND_VACCINE_ITEM,
        BUNDLE_ITEM,
        PRIOR_AUTHORIZATION_ITEM,
        QUANTITY_LIMIT_ITEM,
        STEP_THERAPY_ITEM,
      ];
    default:
      return [];
  }
};
