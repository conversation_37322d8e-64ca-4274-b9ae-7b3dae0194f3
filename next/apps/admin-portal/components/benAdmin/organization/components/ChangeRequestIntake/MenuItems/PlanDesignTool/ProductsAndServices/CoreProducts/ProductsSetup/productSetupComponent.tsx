import { Flex } from '@chakra-ui/react';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useValidateByPageFromContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
import { useValidationErrorsForForm } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useValidationErrorsForForm';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  PHARMACY_NETWORK_ITEM,
  PRODUCT_SET_UP_ITEM,
} from '../../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../../Navigation/uiContextEnum';
import { useProductSetupForm } from './productSetupForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const ProductSetupComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const { refetch, validationData, isLoading } =
    useValidateByPageFromContext(PRODUCT_SET_UP_ITEM);

  const { helpCenterData, isFetching } = useHelpCenter(PRODUCT_SET_UP_ITEM);

  const uiContextIndex =
    getUIContextFromNavigationConstant(PRODUCT_SET_UP_ITEM);

  //   const submitHandler = useSaveChangeRequestHandler(formMethods);

  //   const handleSaveAndExit = () => {
  //     // Submit the current form data
  //     const currentValues = formMethods.getValues();
  //     submitHandler(currentValues);
  //   };
  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useProductSetupForm(
    currentDetails as Partial<OrganizationDetails>
  );

  const { createContinueHandler, isContinueLoading } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  // When the user clicks "Continue", run any internal continue logic then update the active menu item.
  const handleContinue = createContinueHandler(
    PRODUCT_SET_UP_ITEM,
    PHARMACY_NETWORK_ITEM
  );

  const contextIndex = getUIContextFromNavigationConstant(PRODUCT_SET_UP_ITEM);

  useValidationErrorsForForm({
    subCategories,
    formMethods,
    validationData,
    contextIndex,
  });

  // Use continue loading state or validation loading state, whichever is true
  const isProcessing = isContinueLoading || isLoading;
  return (
    <Flex justify="space-between">
      <GenericForm
        formMethods={formMethods}
        formName="Core Products - Product Set Up"
        formDescription=""
        subCategories={subCategories}
        onContinue={handleContinue}
        // onSaveExit={handleSaveAndExit}
        isProcessing={isProcessing}
      />
      <HelpCenter
        validationResults={
          uiContextIndex
            ? validationData?.results?.[uiContextIndex]?.validation_results
            : undefined
        }
        helpContent={helpCenterData}
        isLoading={isFetching}
      />
    </Flex>
  );
};

export default ProductSetupComponent;
