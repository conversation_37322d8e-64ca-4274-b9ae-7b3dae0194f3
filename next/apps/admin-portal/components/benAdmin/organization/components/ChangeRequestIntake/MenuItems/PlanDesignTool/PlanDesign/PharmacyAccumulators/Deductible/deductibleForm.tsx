import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from '@chakra-ui/react';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin';
import { AccumulationDeductible } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useValidateByPageFromContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { getDedectibleBasePath } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/deductiblePAConfig';
import { useModalFormValidation } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useModalFormValidation';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineForm<PERSON>ield,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { DEDUCTIBLE_ITEM } from '../../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../../Navigation/uiContextEnum';
import { productNames } from '../../../productNameConstants';
import { currencyValidation, smallintValidation } from '../../../validations';

interface DeductibleFormModalProps {
  initialData?: Partial<AccumulationDeductible>;
  onSave?: (data: any) => void;
  onCancel?: () => void;
  itemIndex?: number;
  isNewItem?: boolean;
  productName: string;
  parentFormMethods?: any;
}

export const DeductibleFormModal: FC<DeductibleFormModalProps> = ({
  initialData = {},
  onSave,
  onCancel,
  itemIndex = -1,
  isNewItem = true,
  productName,
  parentFormMethods,
}) => {
  const {
    yesNoMap,
    esiAccumPeriodMap,
    benefitPeriodsMap,
    benefitPeriodLengthMap,
    carryoverPhaseMap,
    accumDrugListMap,
    drugTypeStatusMap,
    formularyStatusMap,
    integratedMap,
    networkApplicabilityMap,
    networkStatusMap,
    pharmacyChannelAccumMap,
    sharedIndicatorMap,
    embeddedMap,
    yesNoViewBenefitMap,
  } = usePicklistMaps();

  const basePath = getDedectibleBasePath(isNewItem, itemIndex);

  const formMethods = useForm({
    defaultValues: { [basePath]: initialData },
    shouldUnregister: false,
  });

  const currentData = formMethods.watch(basePath) || initialData;

  const [doesDeductibleApply, setDoesDeductibleApply] = useState<number>(
    Number(initialData?.apply_ind) || 0
  );

  useEffect(() => {
    formMethods.setValue(basePath, initialData);
    setDoesDeductibleApply(Number(initialData?.apply_ind) || 0);
  }, [basePath, formMethods, initialData]);

  useEffect(() => {
    const subscription = formMethods.watch((_, { name }) => {
      if (name === `${basePath}.apply_ind`) {
        const latestValue = formMethods.getValues(`${basePath}.apply_ind`);
        setDoesDeductibleApply(Number(latestValue));
      }
    });
    return () => subscription.unsubscribe();
  }, [formMethods, basePath]);

  const planDesignIndex = getIndexFromURL() ?? 0;
  const { refetch, isLoading } = useValidateByPageFromContext(DEDUCTIBLE_ITEM);
  const saveHandler = useSaveChangeRequestHandler(
    parentFormMethods || {},
    false,
    true
  );
  const validateModal = useModalFormValidation({
    refetch,
    navigationConstant: DEDUCTIBLE_ITEM,
    getUIContextFromNavigationConstant,
    planDesignIndex,
    itemIndex,
    isNewItem,
    basePath,
    formMethods,
  });

  const handleFormSubmit = useCallback(async () => {
    if (!onSave) {
      return;
    }

    try {
      // Get the submitted data from modal form
      const submitted = formMethods.getValues(basePath);
      let finalData = isNewItem
        ? { ...initialData, ...submitted }
        : { ...currentData, ...submitted };

      // If doesDeductibleApply === 0, set effective_date to today's date
      if (doesDeductibleApply === 0) {
        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        const formattedToday = `${yyyy}-${mm}-${dd}`;
        finalData = {
          ...finalData,
          effective_date: formattedToday,
        };
      }

      // Handle parent form update and save
      if (parentFormMethods && saveHandler) {
        const parentValues = parentFormMethods.getValues();

        // In edit mode, update parent form with modal data before validation
        if (!isNewItem) {
          // Update the specific deductible item in the parent form
          if (
            !parentValues.plan?.plan_designs?.[planDesignIndex]
              ?.plan_design_details?.[0]?.accumulation_deductible
          ) {
            // Initialize the array if it doesn't exist
            if (!parentValues.plan) parentValues.plan = {};
            if (!parentValues.plan.plan_designs)
              parentValues.plan.plan_designs = [];
            if (!parentValues.plan.plan_designs[planDesignIndex])
              parentValues.plan.plan_designs[planDesignIndex] = {};
            if (
              !parentValues.plan.plan_designs[planDesignIndex]
                .plan_design_details
            )
              parentValues.plan.plan_designs[
                planDesignIndex
              ].plan_design_details = [{}];
            if (
              !parentValues.plan.plan_designs[planDesignIndex]
                .plan_design_details[0].accumulation_deductible
            ) {
              parentValues.plan.plan_designs[
                planDesignIndex
              ].plan_design_details[0].accumulation_deductible = [];
            }
          }

          // Update the deductible item in the parent form
          const deductibleArray =
            parentValues.plan.plan_designs[planDesignIndex]
              .plan_design_details[0].accumulation_deductible;
          deductibleArray[itemIndex] = finalData;

          // Set the updated values back to parent form
          parentFormMethods.reset(parentValues, { keepDirty: false });
        }

        // Save the form data
        await saveHandler(parentValues);
      }

      // Only run validation in edit mode, skip in create mode
      if (!isNewItem) {
        const hasValidationErrors = await validateModal();

        if (hasValidationErrors) {
          return;
        }
      }

      onSave(finalData);
    } catch (error) {
      console.error('Validation error:', error);
    }
  }, [
    onSave,
    formMethods,
    basePath,
    isNewItem,
    initialData,
    currentData,
    doesDeductibleApply,
    parentFormMethods,
    saveHandler,
    validateModal,
    planDesignIndex,
    itemIndex,
  ]);

  const formConfig = useMemo(() => {
    return {
      subCategories: [
        defineSubCategory('', '', [
          ...(productName === productNames.CMK_360 ||
          productName === productNames.ESI_360 ||
          productName === productNames.OPT_360 ||
          productName === productNames.IRX_360
            ? [
                defineInlineFieldGroup([
                  defineFormField(
                    'Does Deductible Apply?',
                    'dropdownSelect',
                    `${basePath}.apply_ind`,
                    currentData?.apply_ind,
                    {
                      isRequired: true,
                      optionsMap: yesNoMap,
                      validations: smallintValidation,
                    }
                  ),
                  ...(doesDeductibleApply === 1
                    ? [
                        defineFormField(
                          'Accums Tier Name',
                          'input',
                          `${basePath}.accums_tier_name`,
                          currentData?.accums_tier_name,
                          {
                            placeholder: 'Enter Accums Tier Name',
                            validations: z
                              .string()
                              .max(
                                255,
                                'Accums Tier Name must be less than 255 characters'
                              )
                              .optional()
                              .nullable(),
                          }
                        ),
                      ]
                    : []),
                ]),
                ...(doesDeductibleApply === 1
                  ? [
                      ...(productName === productNames.CMK_360 ||
                      productName === productNames.ESI_360 ||
                      productName === productNames.OPT_360 ||
                      productName === productNames.IRX_360
                        ? [
                            defineInlineFieldGroup([
                              defineFormField(
                                'Accums Tier Effective Date',
                                'datepicker',
                                `${basePath}.effective_date`,
                                currentData?.effective_date,
                                {
                                  validations: z.date({
                                    invalid_type_error:
                                      'Effective Date must be a valid date',
                                  }),
                                }
                              ),
                              defineFormField(
                                'Accums Tier End Date',
                                'datepicker',
                                `${basePath}.expiration_date`,
                                currentData?.expiration_date,
                                {
                                  validations: z
                                    .date({
                                      invalid_type_error:
                                        'End Date must be a valid date',
                                    })
                                    .optional()
                                    .nullable(),
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Accums Tier PBC Order',
                                'input',
                                `${basePath}.pbc_order`,
                                currentData?.pbc_order,
                                {
                                  placeholder: 'Enter Accums Tier PBC Order',
                                  validations: smallintValidation,
                                }
                              ),
                              defineFormField(
                                'Deductible Accumulation Period',
                                'dropdownSelect',
                                `${basePath}.accum_period_ind`,
                                currentData?.accum_period_ind,
                                {
                                  infoText: 'Deductible Accumulation Period',
                                  optionsMap: esiAccumPeriodMap,
                                  validations: z.string().optional(),
                                }
                              ),
                            ]),
                          ]
                        : []),
                      defineInlineFieldGroup([
                        ...(productName === productNames.CMK_360 ||
                        productName === productNames.ESI_360 ||
                        productName === productNames.OPT_360 ||
                        productName === productNames.IRX_360
                          ? [
                              defineFormField(
                                'Specify Deductible Accumulation Period',
                                'dropdownSelect',
                                `${basePath}.specify_accum_period_ind`,
                                currentData?.specify_accum_period_ind,
                                {
                                  infoText:
                                    'Specify Deductible Accumulation Period',
                                  optionsMap: benefitPeriodsMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]
                          : []),
                        ...(productName === productNames.ESI_360
                          ? [
                              defineFormField(
                                'Benefit Period Length',
                                'dropdownSelect',
                                `${basePath}.benefit_period_length_ind`,
                                currentData?.benefit_period_length_ind,
                                {
                                  infoText:
                                    "Whether the accumulator resets every year, lasts for the patient's lifetime, or other (specify).",
                                  optionsMap: benefitPeriodLengthMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]
                          : []),
                      ]),
                      defineInlineFieldGroup([
                        ...(productName === productNames.ESI_360
                          ? [
                              defineFormField(
                                'Benefit Period Length - Other',
                                'input',
                                `${basePath}.benefit_period_length_other`,
                                currentData?.benefit_period_length_other,
                                {
                                  placeholder:
                                    'Enter Benefit Period Length - Other',
                                  infoText:
                                    'Used to specify the Benefit Period, if not calendar or lifetime.',
                                  validations: smallintValidation,
                                }
                              ),
                            ]
                          : []),
                        ...(productName === productNames.CMK_360 ||
                        productName === productNames.ESI_360 ||
                        productName === productNames.OPT_360 ||
                        productName === productNames.IRX_360
                          ? [
                              defineFormField(
                                'Do Priming Balances Apply?',
                                'dropdownSelect',
                                `${basePath}.priming_balances_ind`,
                                currentData?.priming_balances_ind,
                                {
                                  optionsMap: yesNoMap,
                                  infoText:
                                    'Used for mid plan-year changes to credit existing member deductible spend to the new plan.',
                                  validations: smallintValidation,
                                }
                              ),
                            ]
                          : []),
                      ]),
                      ...(productName === productNames.CMK_360 ||
                      productName === productNames.ESI_360 ||
                      productName === productNames.OPT_360 ||
                      productName === productNames.IRX_360
                        ? [
                            defineInlineFieldGroup([
                              defineFormField(
                                'Carryover Phase',
                                'dropdownSelect',
                                `${basePath}.carryover_phase_ind`,
                                currentData?.carryover_phase_ind,
                                {
                                  infoText:
                                    'Denotes whether any part of the benefit carries over from one benefit/accum year to the next.',
                                  optionsMap: carryoverPhaseMap,
                                  validations: smallintValidation,
                                }
                              ),
                              defineFormField(
                                'Describe Carryover Phase',
                                'input',
                                `${basePath}.describe_carryover_phase`,
                                currentData?.describe_carryover_phase,
                                {
                                  infoText:
                                    'Provides additional details for the carryover phase.',
                                  placeholder: 'Describe Carryover Phase',
                                  validations: z
                                    .string()
                                    .max(
                                      255,
                                      'Describe Carryover Phase must be less than 255 characters'
                                    )
                                    .optional()
                                    .nullable(),
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Deductible Integrated?',
                                'dropdownSelect',
                                `${basePath}.integrated_ind`,
                                currentData?.integrated_ind,
                                {
                                  infoText:
                                    'Whether MOOP is Rx-only (separate) or combined with medical (integrated).',
                                  optionsMap: integratedMap,
                                }
                              ),
                              defineFormField(
                                'Deductible Embedded?',
                                'dropdownSelect',
                                `${basePath}.embedded_ind`,
                                currentData?.embedded_ind,
                                {
                                  infoText:
                                    'Embedded Accums = Each member must meet the individual MOOP.',
                                  optionsMap: embeddedMap,
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Individual Plan Deductible Amount',
                                'input',
                                `${basePath}.individual_plan_amount`,
                                currentData?.individual_plan_amount,
                                {
                                  placeholder:
                                    'Enter Individual Plan Deductible Amount',
                                  validations: smallintValidation,
                                }
                              ),
                              defineFormField(
                                'Family Plan Deductible Amount',
                                'input',
                                `${basePath}.family_plan_amount`,
                                currentData?.family_plan_amount,
                                {
                                  placeholder:
                                    'Enter Family Plan Deductible Amount',
                                  validations: smallintValidation,
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Employee +1 Dependent Deductible Amount',
                                'input',
                                `${basePath}.employee_1_dep_amount`,
                                currentData?.employee_1_dep_amount,
                                {
                                  placeholder:
                                    'Enter Employee +1 Dependent Deductible Amount',
                                  validations: currencyValidation,
                                }
                              ),
                              defineFormField(
                                'Individual Deductible within Family',
                                'input',
                                `${basePath}.individual_within_family_amount`,
                                currentData?.individual_within_family_amount,
                                {
                                  infoText:
                                    'For non-embedded plans. Some plans only allow the individual to hit up to a certain amount of the family deductible.',
                                  placeholder:
                                    'Enter Individual Deductible within Family Amount',
                                  validations: currencyValidation,
                                }
                              ),
                            ]),
                          ]
                        : []),
                      defineInlineFieldGroup([
                        ...(productName === productNames.CMK_360 ||
                        productName === productNames.ESI_360 ||
                        productName === productNames.OPT_360 ||
                        productName === productNames.IRX_360
                          ? [
                              defineFormField(
                                'Does Deductible Apply to Maximum Out of Pocket?',
                                'dropdownSelect',
                                `${basePath}.apply_to_moop_ind`,
                                currentData?.apply_to_moop_ind,
                                {
                                  optionsMap: yesNoMap,
                                }
                              ),
                            ]
                          : []),
                        ...(productName === productNames.CMK_360 ||
                        productName === productNames.OPT_360 ||
                        productName === productNames.IRX_360
                          ? [
                              defineFormField(
                                'Deductible Applies to Retail, Mail & Paper',
                                'dropdownSelect',
                                `${basePath}.apply_retail_mail_paper_ind`,
                                currentData?.apply_retail_mail_paper_ind,
                                {
                                  infoText:
                                    'Any claim from retail, mail or submitted via paper applies to deductible.',
                                  optionsMap: yesNoMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]
                          : []),
                      ]),

                      ...(productName === productNames.CMK_360 ||
                      productName === productNames.ESI_360 ||
                      productName === productNames.OPT_360 ||
                      productName === productNames.IRX_360
                        ? [
                            defineInlineFieldGroup([
                              defineFormField(
                                'Penalties Apply to Deductible',
                                'dropdownSelect',
                                `${basePath}.penalties_apply_ind`,
                                currentData?.penalties_apply_ind,
                                {
                                  infoText: 'Penalties Apply to Deductible',
                                  optionsMap: yesNoViewBenefitMap,
                                  validations: smallintValidation,
                                }
                              ),
                              defineFormField(
                                'Copay Apply During Deductible Phase',
                                'dropdownSelect',
                                `${basePath}.copay_apply_during_deductible_phase_ind`,
                                currentData?.copay_apply_during_deductible_phase_ind,
                                {
                                  optionsMap: yesNoMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]),

                            defineInlineFieldGroup([
                              defineFormField(
                                'Does Deductible Apply to Brand Medications Only?',
                                'dropdownSelect',
                                `${basePath}.apply_brand_only_ind`,
                                currentData?.apply_brand_only_ind,
                                {
                                  optionsMap: yesNoMap,
                                  validations: smallintValidation,
                                }
                              ),
                              defineFormField(
                                'Does Deductible Apply to Specialty Medications Only?',
                                'dropdownSelect',
                                `${basePath}.apply_specialty_only_ind`,
                                currentData?.apply_specialty_only_ind,
                                {
                                  optionsMap: yesNoMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]),
                          ]
                        : []),
                      ...(productName === productNames.ESI_360
                        ? [
                            defineInlineFieldGroup([
                              defineFormField(
                                'Shared Indicator',
                                'dropdownSelect',
                                `${basePath}.shared_ind`,
                                currentData?.shared_ind,
                                {
                                  infoText: 'Shared Indicator',
                                  optionsMap: sharedIndicatorMap,
                                  validations: smallintValidation,
                                }
                              ),
                              defineFormField(
                                'Drug Type Status',
                                'dropdownSelect',
                                `${basePath}.drug_type_status_ind`,
                                currentData?.drug_type_status_ind,
                                {
                                  optionsMap: drugTypeStatusMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]),
                          ]
                        : []),
                      defineInlineFieldGroup([
                        ...(productName === productNames.ESI_360
                          ? [
                              defineFormField(
                                'Formulary Status',
                                'dropdownSelect',
                                `${basePath}.formulary_status_ind`,
                                currentData?.formulary_status_ind,
                                {
                                  optionsMap: formularyStatusMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]
                          : []),
                        ...(productName === productNames.CMK_360 ||
                        productName === productNames.ESI_360 ||
                        productName === productNames.OPT_360 ||
                        productName === productNames.IRX_360
                          ? [
                              defineFormField(
                                'Network Status',
                                'dropdownSelect',
                                `${basePath}.network_status_ind`,
                                currentData?.network_status_ind,
                                {
                                  infoText: 'Network Status',
                                  optionsMap: networkStatusMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]
                          : []),
                      ]),
                      ...(productName === productNames.CMK_360 ||
                      productName === productNames.OPT_360 ||
                      productName === productNames.IRX_360
                        ? [
                            defineInlineFieldGroup([
                              defineFormField(
                                'Specialty Deductible',
                                'input',
                                `${basePath}.specialty_deductible_amount`,
                                currentData?.specialty_deductible_amount,
                                {
                                  infoText: 'Specialty Deductible',
                                  placeholder: 'Enter Specialty Deductible',
                                  validations: currencyValidation,
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Deductible Applies to Brand',
                                'dropdownSelect',
                                `${basePath}.apply_brand_ind`,
                                currentData?.apply_brand_ind,
                                {
                                  infoText: 'Deductible Applies to Brand',
                                  optionsMap: yesNoMap,
                                  placeholder: 'Deductible Applies to Brand',
                                }
                              ),
                              defineFormField(
                                'Deductible Applies to Retail',
                                'dropdownSelect',
                                `${basePath}.apply_retail_ind`,
                                currentData?.apply_retail_ind,
                                {
                                  infoText: 'Deductible Applies to Retail',
                                  optionsMap: yesNoMap,
                                  placeholder: 'Deductible Applies to Retail',
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Deductible Applies to Specialty',
                                'dropdownSelect',
                                `${basePath}.apply_specialty_ind`,
                                currentData?.apply_specialty_ind,
                                {
                                  infoText: 'Deductible Applies to Specialty',
                                  optionsMap: yesNoMap,
                                  placeholder:
                                    'Deductible Applies to Specialty',
                                }
                              ),
                              defineFormField(
                                'Deductible Applies to Generic',
                                'dropdownSelect',
                                `${basePath}.apply_generic_ind`,
                                currentData?.apply_generic_ind,
                                {
                                  infoText: 'Deductible Applies to Generic',
                                  optionsMap: yesNoMap,
                                  placeholder: 'Deductible Applies to Generic',
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Deductible Applies to Mail',
                                'dropdownSelect',
                                `${basePath}.apply_mail_ind`,
                                currentData?.apply_mail_ind,
                                {
                                  infoText: 'Deductible Applies to Mail',
                                  optionsMap: yesNoMap,
                                  placeholder: 'Deductible Applies to Mail',
                                }
                              ),
                              defineFormField(
                                'Deductible Applies to Diabetic',
                                'dropdownSelect',
                                `${basePath}.apply_diabetic_ind`,
                                currentData?.apply_diabetic_ind,
                                {
                                  infoText: 'Deductible Applies to Diabetic',
                                  optionsMap: yesNoMap,
                                  placeholder: 'Deductible Applies to Diabetic',
                                }
                              ),
                            ]),
                          ]
                        : []),
                      ...(productName === productNames.ESI_360
                        ? [
                            defineInlineFieldGroup([
                              defineFormField(
                                'Network Applicability',
                                'dropdownSelect',
                                `${basePath}.network_applicability_ind`,
                                currentData?.network_applicability_ind,
                                {
                                  optionsMap: networkApplicabilityMap,
                                  validations: smallintValidation,
                                }
                              ),
                              defineFormField(
                                'Pharmacy Channel',
                                'dropdownSelect',
                                `${basePath}.pharmacy_channel_ind`,
                                currentData?.pharmacy_channel_ind,
                                {
                                  infoText: 'Pharmacy Channel',
                                  optionsMap: pharmacyChannelAccumMap,
                                  validations: smallintValidation,
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Include Drug List',
                                'dropdownSelect',
                                `${basePath}.include_drug_list_ind`,
                                currentData?.include_drug_list_ind,
                                {
                                  placeholder: 'Include Drug List',
                                  optionsMap: accumDrugListMap,
                                  allowSearch: true,
                                }
                              ),
                              defineFormField(
                                'Exclude Drug List',
                                'dropdownSelect',
                                `${basePath}.exclude_drug_list_ind`,
                                currentData?.exclude_drug_list_ind,
                                {
                                  placeholder: 'Exclude Drug List',
                                  optionsMap: accumDrugListMap,
                                  allowSearch: true,
                                }
                              ),
                            ]),
                            defineInlineFieldGroup([
                              defineFormField(
                                'Deductible Notes',
                                'textarea',
                                `${basePath}.notes`,
                                currentData?.notes,
                                {
                                  placeholder: 'Enter Deductible Notes',
                                  validations: z
                                    .string()
                                    .max(
                                      2000,
                                      'Deductible Notes must be less than 2000 characters'
                                    )
                                    .optional()
                                    .nullable(),
                                  rows: 5,
                                  customProps: {
                                    minHeight: '120px',
                                    overflow: 'hidden',
                                  },
                                }
                              ),
                            ]),
                          ]
                        : []),
                    ]
                  : []),
              ]
            : []),
        ]),
      ],
    };
  }, [
    productName,
    basePath,
    currentData,
    yesNoMap,
    doesDeductibleApply,
    esiAccumPeriodMap,
    benefitPeriodsMap,
    benefitPeriodLengthMap,
    carryoverPhaseMap,
    integratedMap,
    embeddedMap,
    yesNoViewBenefitMap,
    sharedIndicatorMap,
    drugTypeStatusMap,
    formularyStatusMap,
    networkStatusMap,
    networkApplicabilityMap,
    pharmacyChannelAccumMap,
    accumDrugListMap,
  ]);

  return (
    <>
      <ModalBody>
        <GenericForm
          formMethods={formMethods}
          formName="Deductible"
          formDescription="Here's a brief description placeholder of what this page will have the user work or make changes to. It should be able to provide context and information to the user to confidently answer information."
          subCategories={formConfig?.subCategories || []}
          showButtons={false}
          isInModal
          isProcessing={isLoading}
        />
      </ModalBody>
      <ModalFooter>
        <Button variant="outline" mr={3} onClick={onCancel}>
          Cancel
        </Button>
        <Button
          colorScheme="green"
          onClick={() => {
            console.log('Save button clicked');
            handleFormSubmit();
          }}
          isLoading={isLoading}
        >
          Save
        </Button>
      </ModalFooter>
    </>
  );
};
