import { useUser } from '@auth0/nextjs-auth0/client';
import {
  Box,
  Button,
  Flex,
  Heading,
  HStack,
  Icon,
  Tag,
  Text,
} from '@chakra-ui/react';
import {
  useIsIntakeOverview,
  useSaveChangeRequestHandler,
  useShowOrganizationHeader,
} from 'apps/admin-portal/components/benAdmin';
import { ChangeRequest } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { UseFormReturn } from 'react-hook-form';
import { FiClock, FiUser } from 'react-icons/fi';

import Breadcrumbs from '../Navigation/breadcrumbs';

export const IntakeHeader = ({
  formMethods,
  getBreadcrumbPaths,
  onBreadcrumbNavigate,
  planName,
  planEffectiveDate,
}: {
  formMethods: UseFormReturn<any>;
  getBreadcrumbPaths: () => any;
  onBreadcrumbNavigate: (id: string) => void;
  planName: string;
  planEffectiveDate: string;
}) => {
  const changeRequest: ChangeRequest | null = JSON.parse(
    sessionStorage.getItem('selectedChangeRequest') || 'null'
  );
  const isLandingPage = useIsIntakeOverview();
  const saveChangeRequestHandler = useSaveChangeRequestHandler(
    formMethods,
    false
  );

  const showOrganizationHeader = useShowOrganizationHeader();

  const organizationName = formMethods.getValues('organization.name');

  // Format the go live date
  const goLiveDate = new Date(
    `${changeRequest?.target_effective_date}T00:00:00`
  );
  const formattedDate = goLiveDate.toLocaleDateString('en-US', {
    month: 'numeric',
    day: 'numeric',
    year: 'numeric',
  });

  const today = new Date();
  const timeDiff = goLiveDate.getTime() - today.getTime();
  const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

  const getDaysLeftText = () => {
    if (daysDiff > 0) {
      return `${daysDiff} Days Left`;
    } else if (daysDiff === 0) {
      return 'Today';
    } else {
      return `${Math.abs(daysDiff)} Days Active`;
    }
  };

  const getDaysLeftColor = () => {
    if (daysDiff > 30) return 'green';
    if (daysDiff > 0) return 'orange';
    return 'blue';
  };

  const user = useUser();

  // Get assignment status - this would come from your actual data
  const isAssignedToMe = changeRequest?.updater?.email === user.user?.email;
  const assignmentText = isAssignedToMe
    ? 'Assigned to Me'
    : 'Not Assigned to Me';

  // Get status
  const status = changeRequest?.status_value || 'Pending';

  // Format last saved time
  const formatLastSaved = () => {
    const lastSaved = changeRequest?.updated_date || new Date().toISOString();
    const date = new Date(lastSaved);
    return date.toLocaleString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
      month: 'numeric',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <Box
      bg={isLandingPage ? 'white' : '#f9f9f9'}
      mt={8}
      borderRadius="lg"
      boxShadow={isLandingPage ? 'lg' : undefined}
      border={isLandingPage ? '1px solid' : undefined}
      borderColor={isLandingPage ? 'gray.200' : undefined}
      zIndex={9999}
    >
      <Flex px={2} py={4} direction="column" gap={3}>
        <Breadcrumbs
          paths={getBreadcrumbPaths()}
          onNavigate={onBreadcrumbNavigate}
        />
        {showOrganizationHeader && (
          <Box>
            <Flex justify="space-between" align="flex-start" mb={3}>
              <Box>
                {organizationName && (
                  <Heading color="#69696A" size="md" mt={2} mb={2}>
                    {organizationName}
                  </Heading>
                )}
                <Text color="#718096" fontSize="md" mt={1}>
                  {planName} - {planEffectiveDate}
                </Text>
              </Box>
              <Button
                variant="outline"
                size="sm"
                borderRadius="md"
                _hover={{ bg: 'gray.50' }}
                onClick={() =>
                  saveChangeRequestHandler(formMethods.getValues())
                }
              >
                Exit Change Request
              </Button>
            </Flex>

            <Flex
              align="center"
              gap={4}
              color="#718096"
              fontSize="sm"
              borderTop="1px solid"
              borderColor="gray.200"
              pt={3}
            >
              <HStack spacing={2}>
                <Text>Effective {formattedDate}</Text>
              </HStack>

              <HStack spacing={2}>
                <Icon as={FiClock} color={getDaysLeftColor() + '.500'} />
                <Tag
                  size="sm"
                  colorScheme={getDaysLeftColor()}
                  borderRadius="full"
                >
                  {getDaysLeftText()}
                </Tag>
              </HStack>

              <HStack spacing={2}>
                <Icon as={FiUser} />
                <Text>{assignmentText}</Text>
              </HStack>

              <Tag
                size="sm"
                colorScheme={status === 'Published' ? 'green' : 'yellow'}
                borderRadius="full"
              >
                {status}
              </Tag>

              <Box ml="auto" color="#A0AEC0" fontSize="xs">
                Last Updated {formatLastSaved()}
              </Box>
            </Flex>
          </Box>
        )}
      </Flex>
    </Box>
  );
};
