import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { clientConfig } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/ClientProfile/Config/clientConfig';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { productNames } from '../../productNameConstants';

/**
 * usePharmacyBenefitsManager Hook
 *
 * Generates form subcategories (sections) based on the provided organization details.
 * Uses dot notation for field names so that the final form JSON is nested.
 *
 * @param currentDetails - Partial organization data used to prefill form fields.
 * @returns An object containing the generated subCategories.
 */
export function usePharmacyBenefitsManager(
  currentDetails: Partial<OrganizationDetails>
) {
  const { medicalVendorMap } = usePicklistMaps();
  const productName = currentDetails.plan?.product.name;

  // Build the subcategories using the helper functions.
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Chosen PBM',
          'input',
          clientConfig.chosen_pbm_legal_entity_name,
          currentDetails?.plan?.product?.vendor?.name,
          {
            placeholder: 'Select an Option',
            validations: z
              .string()
              .max(255, 'Value cannot exceed 255 characters'),
          }
        ),
        defineFormField(
          'Carrier Number',
          'input',
          clientConfig.carrier_number,
          currentDetails?.plan?.carrier_number,
          {
            isRequired: true,
            infoText: 'Can be found on the Sold Form',
            placeholder: 'Enter Carrier Number',
            validations: z
              .string({ required_error: 'Carrier Number is required.' })
              .max(100, 'Carrier Number cannot exceed 100 characters'),
          }
        ),
      ]),
      // BADMIN-909 rm later
      ...(productName === productNames.CMK_360 ||
      productName === productNames.ESI_360 ||
      productName === productNames.OPT_360 ||
      productName === productNames.IRX_360 ||
      productName === productNames.CMK_DIRECT ||
      productName === productNames.ESI_DIRECT
        ? [
            defineInlineFieldGroup([
              defineFormField(
                'Medical Vendor',
                'dropdownSelect',
                clientConfig.medical_vendor_ind,
                currentDetails?.plan?.medical_vendor_ind,
                {
                  placeholder: 'Select Medical Vendor',
                  optionsMap: medicalVendorMap,
                  infoText: 'Select the medical vendor for this plan',
                }
              ),
            ]),
          ]
        : []),
    ]),

    // BADMIN-909 rm later
    ...(productName !== productNames.ESI_360
      ? [
          defineSubCategory('Optum or CVS', '', [
            defineInlineFieldGroup([
              defineFormField(
                'Account Number (only if Optum or CVS)',
                'input',
                clientConfig.account_number,
                currentDetails?.plan?.account_number,
                {
                  isRequired: true,
                  infoText:
                    "Usually an abbreviation of the client's name. Account number used for OPT and CMK clients only.",
                  placeholder: 'Enter Account Number',
                  validations: z
                    .string({
                      required_error:
                        'Account Number is required for Optum/CVS.',
                    })
                    .max(15, 'Account Number cannot exceed 100 characters'),
                }
              ),
              defineFormField('', 'text', '', undefined, {}),
            ]),
          ]),
        ]
      : []),

    // BADMIN-909 rm later
    ...(productName === productNames.ESI_360 ||
    productName === productNames.CMK_DIRECT ||
    productName === productNames.ESI_DIRECT
      ? [
          defineSubCategory('ESI Only', '', [
            defineInlineFieldGroup([
              defineFormField(
                'Contract Name',
                'input',
                clientConfig.contract_name,
                currentDetails?.plan?.contract_name,
                {
                  placeholder: 'Enter Contract Name',
                  validations: z
                    .string()
                    .max(100, 'Contract Name cannot exceed 100 characters'),
                }
              ),
              defineFormField(
                'Paid Contract Number',
                'input',
                clientConfig.paid_contract_number,
                currentDetails?.plan?.paid_contract_number,
                {
                  isRequired: true,
                  infoText:
                    '8 digits long and normally RXB + abbreviation of the client name. Ex: RXBAMUNE for Amuneal Manufacturing Corporation',
                  placeholder: 'Enter Paid Contract Number',
                  validations: z
                    .string({
                      required_error:
                        'Paid Contract Number is required for ESI.',
                    })
                    .min(1, 'Paid Contract Number cannot be empty for ESI.')
                    .max(100, 'Value cannot exceed 100 characters'),
                }
              ),
            ]),

            defineInlineFieldGroup([
              defineFormField(
                'Group Umbrella Number',
                'input',
                clientConfig.group_umbrella_number,
                currentDetails?.plan?.group_umbrella_number,
                {
                  isRequired: true,
                  infoText:
                    'Umbrella Group number is shown on the ID card. The Umbrella Group number is the Paid Contract Number, minus the last letter',
                  placeholder: 'Enter Group Umbrella Number',
                  validations: z
                    .string({
                      required_error:
                        'Group Umbrella Number is required for ESI.',
                    })
                    .min(1, 'Group Umbrella Number cannot be empty for ESI.')
                    .max(100, 'Value cannot exceed 100 characters'),
                }
              ),
            ]),
          ]),
        ]
      : []),
  ];

  return {
    subCategories,
  };
}
