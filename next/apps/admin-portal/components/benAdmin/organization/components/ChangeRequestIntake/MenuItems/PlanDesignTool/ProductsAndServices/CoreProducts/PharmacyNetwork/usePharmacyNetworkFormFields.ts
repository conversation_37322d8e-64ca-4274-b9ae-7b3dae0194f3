import {
  Feature,
  OrganizationDetails,
  Plan,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PLAN_DESIGNS_BASE_PATH } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/coreConfig';
import {
  buildPicklistsMap,
  getOptionsMap,
  mapFieldType,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/Configurable/fieldUtils';
import {
  buildFieldPath,
  findMatchingPlanFeatureItem,
  findMatchingPlanFeatures,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planNavigation';
import {
  defineDropdownField,
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { useURLIndex } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/useUrlParameters';
import { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  getIdMap,
  syncAcrossPlanDesigns,
} from '../../../ClinicalDesigns/DynamicForm/paramsUtils';

type FieldType = 'dropdownSelect' | 'input' | 'checkboxGroup' | 'radioGroup';

export const nameOrder = {
  // Below are static keys given to us by the backend.
  // I was told that the below will never change even though the name on the screen might change.
  // They aren't primary keys...but should still never be changed and we should use these to sort these fields.
  maintenanceNetwork: [
    ['Maintenance Pharmacy Network'],
    ['Home Delivery Program'],
    ['Mandatory Mail Grace Fills'],
  ],
  specialtyNetwork: [
    ['Specialty Pharmacy Network'],
    ['Specialty Grace Fills at Retail'],
  ],
  retailNetwork: [['Retail Pharmacy Network']],
  esiXmlFields: [
    ['esi_retail_network'],
    ['esi_direct_custom_network'],
    ['ninety_day_program_xml'],
  ],
};

/**
 * Hook to dynamically generate subCategories for the Pharmacy Network feature with custom grouping.
 * @param feature The Pharmacy Network feature object
 * @param currentDetails The current organization details
 * @param formMethods The react-hook-form methods for managing form state
 * @returns An object containing the generated subCategories
 */
export function usePharmacyNetworkFormFields(
  feature: Feature,
  currentDetails: Partial<OrganizationDetails>,
  formMethods: UseFormReturn<any>
) {
  const urlIndex = useURLIndex(undefined, { debug: false });

  // Memoize picklistsMap to avoid recomputation
  const picklistsMap = useMemo(
    () => buildPicklistsMap(currentDetails.picklists || []),
    [currentDetails.picklists]
  );

  // Use memoization to prevent regenerating fields unnecessarily
  const subCategories = useMemo(() => {
    // Early return if feature is invalid
    if (!feature || !feature.feature_items) {
      return [];
    }

    const matchingPlanFeatures = findMatchingPlanFeatures(
      currentDetails.plan as Plan,
      feature,
      urlIndex || 0
    );

    // Helper function to generate fields for a given set of feature items
    const generateFields = (items: typeof feature.feature_items) =>
      items.map((item) => {
        const matchingItem = findMatchingPlanFeatureItem(
          matchingPlanFeatures,
          item
        );

        // Determine field type based on field_type_label
        const fieldType = mapFieldType(item.field_type_label) as FieldType;

        // Get options map for selection fields
        const optionsMap = getOptionsMap(picklistsMap, item);

        // Build the field path with explicit URL index
        const fieldPath = buildFieldPath(matchingItem, urlIndex);

        // Get the current value from the plan structure
        const currentValue = matchingItem ? matchingItem.value : '';
        const idMap = getIdMap(fieldPath, formMethods);

        const fieldOptions: any = {
          infoText: '',
          placeholder: `Enter ${item.label}`,
          isRequired: false,
        };

        if (fieldType === 'dropdownSelect' && optionsMap) {
          fieldOptions.optionsMap = optionsMap;
          fieldOptions.placeholder = `Select ${item.label}`;
          fieldOptions.showToggle = true;
          fieldOptions.toggleLabel = 'Differs from Plan Design';
          fieldOptions.formPath = PLAN_DESIGNS_BASE_PATH;
          fieldOptions.sync = true;
          fieldOptions.idMap = idMap;
          fieldOptions.syncFunction = syncAcrossPlanDesigns;
          return defineDropdownField(
            item.label || '',
            fieldPath,
            currentValue,
            optionsMap,
            fieldOptions
          );
        }

        if (['checkboxGroup', 'radioGroup'].includes(fieldType) && optionsMap) {
          fieldOptions.optionsMap = optionsMap;
        }
        if (fieldType === 'input') {
          fieldOptions.showToggle = true;
          fieldOptions.toggleLabel = 'Differs from Plan Design';
          fieldOptions.formPath = PLAN_DESIGNS_BASE_PATH;
          fieldOptions.sync = true;
          fieldOptions.idMap = idMap;
          fieldOptions.syncFunction = syncAcrossPlanDesigns;
        }

        return defineFormField(
          item.label || '',
          fieldType,
          fieldPath,
          currentValue,
          fieldOptions
        );
      });

    // Helper function to find feature items by name
    const findFeatureItemsByName = (names: string[]) => {
      return (
        feature.feature_items?.filter((item) =>
          names.some(
            (name) =>
              item.name === name ||
              item.picklist_name === name ||
              item.label === name
          )
        ) || []
      );
    };

    // Helper function to create subcategory for a section
    const createSubcategoryForSection = (
      sectionName: string,
      orderedNames: string[][]
    ) => {
      const allFields: any[] = [];

      // Process each group of names in order
      orderedNames.forEach((nameGroup) => {
        const matchingItems = findFeatureItemsByName(nameGroup);
        if (matchingItems.length > 0) {
          const fields = generateFields(matchingItems);
          allFields.push(...fields);
        }
      });

      // Group fields into inline groups of 2
      const inlineGroups = [];
      for (let i = 0; i < allFields.length; i += 2) {
        inlineGroups.push(defineInlineFieldGroup(allFields.slice(i, i + 2)));
      }

      return defineSubCategory(sectionName, '', inlineGroups);
    };

    // Create subcategories based on nameOrder
    const subCategories: any[] = [];

    // Process each section in nameOrder
    Object.entries(nameOrder).forEach(([sectionKey, orderedNames]) => {
      if (orderedNames.length > 0) {
        // Format section name with spaces and capitalization
        const formattedSectionName = sectionKey
          .replace(/([A-Z])/g, ' $1') // Add space before capital letters
          .replace(/^./, (str) => str.toUpperCase()) // Capitalize first letter
          .replace(/\b(esi|xml)\b/gi, (match) => match.toUpperCase()) // Convert ESI and XML to uppercase
          .trim(); // Remove leading space

        const sectionSubcategory = createSubcategoryForSection(
          formattedSectionName,
          orderedNames
        );
        if (sectionSubcategory.fields.length > 0) {
          subCategories.push(sectionSubcategory);
        }
      }
    });

    // If no fields were found in any section, fall back to the original logic
    if (subCategories.length === 0) {
      const fields = generateFields(feature.feature_items);
      const inlineGroups = [];
      for (let i = 0; i < fields.length; i += 2) {
        inlineGroups.push(defineInlineFieldGroup(fields.slice(i, i + 2)));
      }

      return [
        defineSubCategory(
          feature.label || feature.name || '',
          '',
          inlineGroups
        ),
      ];
    }

    return subCategories;
  }, [feature, currentDetails.plan, urlIndex, picklistsMap, formMethods]);

  return { subCategories };
}
