import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { getAccumsGeneralPaths } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/accumsGeneralConfig';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { productNames } from '../../../productNameConstants';
import { currencyValidation } from '../../../validations';
/**
 * useFormDemo Hook
 *
 * Generates form subcategories (sections) based on the provided organization details.
 * Uses dot notation for field names so that the final form JSON is nested.
 *
 * @param org - Partial organization data used to prefill form fields.
 * @returns An object containing the generated subCategories and navigation handlers.
 */
export function useAccumsGeneralForm(
  currentDetails: Partial<OrganizationDetails>,
  index = 0
) {
  const urlIndex = getIndexFromURL();
  const indexToUse = urlIndex !== undefined ? urlIndex : index;
  const accumsGeneralConfig = getAccumsGeneralPaths(indexToUse);
  const planDesignDetail =
    currentDetails?.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0];

  const productName = currentDetails?.plan?.product?.name;

  const { yesNoMap, medicalIntegrationTierMap } = usePicklistMaps();

  // Build the subcategories using the helper functions.
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        ...(productName === productNames.CMK_360 ||
        productName === productNames.ESI_360 ||
        productName === productNames.OPT_360 ||
        productName === productNames.IRX_360
          ? [
              defineFormField(
                'Accumulators Integrated with Medical',
                'dropdownSelect',
                accumsGeneralConfig.accum_integrated_ind,
                planDesignDetail?.accum_integrated_ind,
                {
                  infoText: 'Accumulators Integrated with Medical',
                  optionsMap: yesNoMap,
                }
              ),
              defineFormField('', 'text', '', undefined, {}),
            ]
          : []),
      ]),

      defineInlineFieldGroup([
        ...(productName === productNames.CMK_360 ||
        productName === productNames.OPT_360
          ? [
              defineFormField(
                'Plan Max Coverage Amount',
                'input',
                accumsGeneralConfig.max_coverage_amount,
                planDesignDetail?.max_coverage_amount,
                {
                  infoText:
                    'If there is an overall maximum benefit for the plan (no coverage is paid by plan after this amount is reached). Please put in a dollar value (leave blank if no maximum applies).',
                  placeholder: 'Enter Plan Max Coverage Amount',
                  validations: currencyValidation.optional(),
                }
              ),
              defineFormField('', 'text', '', undefined, {}),
            ]
          : []),
        ...(productName === productNames.CMK_360
          ? [
              defineFormField(
                'Medical Integration Tier',
                'dropdownSelect',
                accumsGeneralConfig.medical_integration_tier_ind,
                planDesignDetail?.medical_integration_tier_ind,
                {
                  infoText:
                    'This field is for Caremark only. This is the accumulation batch file that determines if accumulations are done in real-time or batch processed.',
                  optionsMap: medicalIntegrationTierMap,
                  validations: z.string().optional(),
                }
              ),
            ]
          : []),
      ]),

      ...(productName === productNames.CMK_360 ||
      productName === productNames.ESI_360 ||
      productName === productNames.OPT_360
        ? [
            defineInlineFieldGroup([
              defineFormField(
                'Separate Accums Retail and IHP',
                'dropdownSelect',
                accumsGeneralConfig.separate_accums_retail_ihp_ind,
                planDesignDetail?.separate_accums_retail_ihp_ind,
                {
                  infoText: 'Do you want to separate Accums Retail and IHP ?',
                  placeholder: 'Select Separate Accums Retail and IHP',
                  optionsMap: yesNoMap,
                }
              ),
              defineFormField(
                'IHP Ded Cross-Apply to Non-IHP Ded',
                'dropdownSelect',
                accumsGeneralConfig.ihp_cross_apply_ind,
                planDesignDetail?.ihp_cross_apply_ind,
                {
                  placeholder: 'Select IHP Ded Cross-Apply to Non-IHP Ded',
                  infoText:
                    'Do you want IHP Deduction to Cross-Apply for Non-IHP Deduction',
                  optionsMap: yesNoMap,
                }
              ),
            ]),
          ]
        : []),
    ]),
  ];

  return {
    subCategories,
  };
}
