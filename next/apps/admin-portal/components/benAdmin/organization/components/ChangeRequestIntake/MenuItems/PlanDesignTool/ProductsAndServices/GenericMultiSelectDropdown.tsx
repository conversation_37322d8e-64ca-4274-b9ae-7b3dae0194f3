import { ChevronDownIcon } from '@chakra-ui/icons';
import {
  <PERSON>,
  Button,
  Checkbox,
  Divider,
  HS<PERSON>ck,
  Menu,
  Menu<PERSON><PERSON>on,
  MenuList,
  Text,
  VStack,
} from '@chakra-ui/react';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

interface GenericMultiSelectDropdownProps {
  data: any;
  label?: string;
  selectedItems: any;
  setSelectedItems: (items: any) => void;
  tag?: string;
  itemsKey: string;
  disabledItems?: Set<number>;
}

// Utility to get the object's id property (generic) - consistent with GenericForm
const getObjectId = (obj: any): string => {
  if (!obj || typeof obj !== 'object') return '';
  const idKey = Object.keys(obj).find((k) => k.toLowerCase().includes('id'));
  const id = idKey ? obj[idKey] : '';
  return id !== undefined && id !== null ? String(id) : '';
};

export const GenericMultiSelectDropdown: React.FC<
  GenericMultiSelectDropdownProps
> = ({
  data,
  label = 'Select options',
  selectedItems,
  setSelectedItems,
  tag,
  itemsKey,
  disabledItems = new Set(),
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [menuWidth, setMenuWidth] = useState<string | number>('75%');
  const [refreshKey, setRefreshKey] = useState(0);

  // Add function to check if an item is disabled
  const isItemDisabled = useCallback(
    (item: any) => {
      const itemId = getObjectId(item);
      return itemId ? disabledItems.has(Number(itemId)) : false;
    },
    [disabledItems]
  );

  // Force refresh when selectedItems change significantly
  useEffect(() => {
    setRefreshKey((prev) => prev + 1);
  }, [selectedItems[itemsKey]]);

  // Memoize and sort products
  const products = useMemo(() => {
    let productList = data?.[itemsKey] || [];

    // First sort by tag if provided
    if (tag && productList.length > 0) {
      productList = [...productList].sort((a, b) => {
        const aTag = (a[tag as keyof typeof a] || '').toString().toLowerCase();
        const bTag = (b[tag as keyof typeof b] || '').toString().toLowerCase();
        return aTag.localeCompare(bTag);
      });
    }

    // Then sort disabled items to the bottom
    return [...productList].sort((a, b) => {
      const aDisabled = isItemDisabled(a);
      const bDisabled = isItemDisabled(b);

      if (aDisabled && !bDisabled) return 1;
      if (!aDisabled && bDisabled) return -1;
      return 0;
    });
  }, [data, itemsKey, tag, isItemDisabled]);

  useEffect(() => {
    if (buttonRef.current) {
      setMenuWidth(buttonRef.current.offsetWidth);
    }
  }, []);

  // Toggle parent or child selection
  const handleToggle = useCallback(
    (item: any, option?: string) => {
      const itemId = getObjectId(item);
      if (!itemId) return;

      setSelectedItems((prevState: any) => {
        const currentItems = prevState[itemsKey] || [];

        if (option) {
          // Child option toggled
          const existingParent = currentItems.find(
            (i: any) => getObjectId(i) === itemId
          );

          if (existingParent) {
            const prevOptions = existingParent.product_options || [];
            let newOptions: string[];

            if (prevOptions.includes(option)) {
              // Remove option
              newOptions = prevOptions.filter((o: string) => o !== option);
            } else {
              // Add option
              newOptions = [...prevOptions, option];
            }

            if (newOptions.length === 0) {
              // Remove parent if no options left
              return {
                ...prevState,
                [itemsKey]: currentItems.filter(
                  (i: any) => getObjectId(i) !== itemId
                ),
              };
            } else {
              // Update parent with new options
              const updatedParent = {
                ...existingParent,
                product_options: newOptions,
              };
              return {
                ...prevState,
                [itemsKey]: currentItems.map((i: any) =>
                  getObjectId(i) === itemId ? updatedParent : i
                ),
              };
            }
          } else {
            // Add new parent with this option
            const newProduct = { ...item, product_options: [option] };
            return {
              ...prevState,
              [itemsKey]: [...currentItems, newProduct],
            };
          }
        } else {
          // Parent selection
          const existingIndex = currentItems.findIndex(
            (i: any) => getObjectId(i) === itemId
          );

          if (existingIndex >= 0) {
            // Remove parent
            return {
              ...prevState,
              [itemsKey]: currentItems.filter(
                (i: any) => getObjectId(i) !== itemId
              ),
            };
          } else {
            // Add parent with all options if it has them
            const newProduct = item.product_options
              ? { ...item, product_options: [...item.product_options] }
              : { ...item };
            return {
              ...prevState,
              [itemsKey]: [...currentItems, newProduct],
            };
          }
        }
      });
    },
    [itemsKey, setSelectedItems]
  );

  // Check if parent/child is selected - directly check current state to avoid memoization issues
  const isChecked = useCallback(
    (item: any, option?: string) => {
      const id = getObjectId(item);
      if (!id) return false;

      // Get current selected items directly from state
      const currentItems = selectedItems[itemsKey] || [];
      const selectedItem = currentItems.find((i: any) => getObjectId(i) === id);

      if (option) {
        // Check if child option is selected
        return selectedItem?.product_options?.includes(option) || false;
      }

      if (item.product_options && item.product_options.length > 0) {
        // Parent with options: checked if we have a selected item AND all children are selected
        const allChildrenSelected =
          selectedItem &&
          selectedItem.product_options &&
          Array.isArray(selectedItem.product_options) &&
          selectedItem.product_options.length === item.product_options.length;

        return allChildrenSelected;
      }

      // Simple parent without options
      return !!selectedItem;
    },
    [selectedItems, itemsKey]
  );

  // Indeterminate state for parent (some but not all children selected)
  const isIndeterminate = useCallback(
    (item: any) => {
      const id = getObjectId(item);
      if (!id || !item.product_options || item.product_options.length === 0) {
        return false;
      }

      // Get current selected items directly from state
      const currentItems = selectedItems[itemsKey] || [];
      const selectedItem = currentItems.find((i: any) => getObjectId(i) === id);
      const selectedOptions = selectedItem?.product_options || [];

      return (
        selectedOptions.length > 0 &&
        selectedOptions.length < item.product_options.length
      );
    },
    [selectedItems, itemsKey]
  );

  // Clear all selections
  const handleClearAll = useCallback(() => {
    setSelectedItems((prev: any) => {
      const newState = {
        ...prev,
        [itemsKey]: [],
      };
      return newState;
    });
  }, [setSelectedItems, itemsKey]);



  // Add function to check if an option is disabled
  const isOptionDisabled = useCallback(
    (item: any, option: string) => {
      const itemId = getObjectId(item);
      if (!itemId) return false;

      // If the parent item is disabled, all options are disabled
      if (disabledItems.has(Number(itemId))) return true;

      // Check if this specific option is already selected in watched ancillaries
      const currentItems = selectedItems[itemsKey] || [];
      const selectedItem = currentItems.find(
        (i: any) => getObjectId(i) === itemId
      );
      return selectedItem?.product_options?.includes(option) || false;
    },
    [disabledItems, selectedItems, itemsKey]
  );

  return (
    <Box pt={5}>
      <Box display="flex" alignItems="center">
        <Menu closeOnSelect={false} closeOnBlur={true}>
          <MenuButton
            as={Button}
            ref={buttonRef}
            rightIcon={<ChevronDownIcon />}
            w="75%"
            justifyContent="space-between"
            textAlign="left"
            fontWeight="normal"
            bg="white"
            borderWidth="1px"
            borderColor="gray.300"
            borderRadius="md"
            boxShadow="sm"
            _focus={{ boxShadow: '0 0 0 1px #38A169' }}
            _hover={{ borderColor: '#38A169' }}
          >
            {label}
          </MenuButton>
          <MenuList
            minW="262px"
            maxH="400px"
            overflowY="auto"
            p={2}
            w={menuWidth}
          >
            <VStack align="stretch" spacing={1}>
              {products.map((item: any, index: number) => {
                const itemId = getObjectId(item);
                const stableKey = `${itemId || `item-${index}`}-${refreshKey}`;
                const isDisabled = isItemDisabled(item);

                return (
                  <Box key={stableKey}>
                    <HStack>
                      <Checkbox
                        key={`parent-${stableKey}`}
                        isChecked={isChecked(item)}
                        isIndeterminate={isIndeterminate(item)}
                        onChange={() => handleToggle(item)}
                        isDisabled={isDisabled}
                        opacity={isDisabled ? 0.6 : 1}
                      >
                        <Text
                          fontWeight={item.product_options ? 'bold' : 'normal'}
                          color={isDisabled ? 'gray.500' : 'inherit'}
                        >
                          {tag && item[tag as keyof typeof item] ? (
                            <span style={{ fontWeight: 'bold' }}>
                              ({item[tag as keyof typeof item]})
                            </span>
                          ) : null}
                          {tag && item[tag as keyof typeof item] ? ' ' : ''}
                          {item.name}
                        </Text>
                      </Checkbox>
                    </HStack>
                    {item.product_options &&
                      item.product_options.length > 0 && (
                        <VStack pl={6} align="stretch" spacing={0}>
                          {item.product_options.map((option: string) => {
                            const isOptionDisabledState = isOptionDisabled(
                              item,
                              option
                            );
                            return (
                              <Checkbox
                                key={`child-${stableKey}-${option}`}
                                isChecked={isChecked(item, option)}
                                onChange={() => handleToggle(item, option)}
                                isDisabled={isOptionDisabledState}
                                opacity={isOptionDisabledState ? 0.6 : 1}
                              >
                                <Text
                                  color={
                                    isOptionDisabledState
                                      ? 'gray.500'
                                      : 'inherit'
                                  }
                                >
                                  {option}
                                </Text>
                              </Checkbox>
                            );
                          })}
                        </VStack>
                      )}
                    <Divider my={1} />
                  </Box>
                );
              })}
            </VStack>
          </MenuList>
        </Menu>
        {(selectedItems[itemsKey]?.length || 0) > 0 && (
          <Button
            size="sm"
            colorScheme="red"
            ml={2}
            onClick={handleClearAll}
            variant="outline"
          >
            Clear
          </Button>
        )}
      </Box>
    </Box>
  );
};

export default GenericMultiSelectDropdown;
