import {
  buildGroupedSidebarItems,
  CLINICAL_DESIGN_MODE,
  mapPlanFeaturesToMetadata,
  OrganizationDetails,
  SidebarConfig,
} from 'apps/admin-portal/components/benAdmin';
import { Feature } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { UseFormReturn } from 'react-hook-form';

import ReviewClinicalDesigns from './Review/clinicalDesignReview';

export const getDynamicClinicalDesignConfig = (
  changeRequest: OrganizationDetails,
  formMethods: UseFormReturn<any>,
  navigateFn?: (section: string, tab: string) => void,
  activeItemId?: string // Add optional activeItemId parameter for consistency
): SidebarConfig => {
  const navigateToClinicalDesignItem = (id: string) =>
    navigateFn ? navigateFn(CLINICAL_DESIGN_MODE, id) : undefined;

  const features = formMethods.watch('plan.plan_designs.0.plan_features');

  // Early return if no plan designs or features available
  if (!features || !Array.isArray(features)) {
    return { sections: [] };
  }

  // Map plan features to metadata features using shared utility
  // Exclude Pharmacy Network as it belongs under Products & Services
  const relevantFeatures: Feature[] = mapPlanFeaturesToMetadata(
    features,
    changeRequest.features,
    ['PharmacyNetwork']
  );

  // Build items with auto-grouping for similar feature names
  // Exclude Pharmacy Network
  const items = buildGroupedSidebarItems(
    relevantFeatures,
    formMethods,
    navigateToClinicalDesignItem,
    undefined, // No specific includes
    ['PharmacyNetwork'] // Exclude Pharmacy Network,
  );

  // Add review section at the end
  items.push({
    id: 'CLINICAL_DESIGN_REVIEW',
    label: 'Review & Save',
    component: (
      <ReviewClinicalDesigns
        formMethods={formMethods}
        onUpdateActiveItem={navigateToClinicalDesignItem}
      />
    ),
  });

  return {
    sections: [
      {
        title: 'CLINICAL DESIGN',
        items,
      },
    ],
  };
};
