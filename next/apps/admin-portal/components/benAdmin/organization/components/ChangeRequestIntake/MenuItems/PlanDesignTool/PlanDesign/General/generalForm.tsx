import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { getGeneralPaths } from '../../../../../Tabs/PlanDesign/PlanDesign/Config/generalConfig';
import { getXmlPaths } from '../../../../../Tabs/PlanDesign/PlanDesign/Config/xmlConfig';
import { productNames } from '../../productNameConstants';
import { phoneValidation, yesNoValidation } from '../../validations';

/**
 * useFormDemo Hook
 *
 * Generates form subcategories (sections) based on the provided organization details.
 * Uses dot notation for field names so that the final form JSON is nested.
 *
 * @param org - Partial organization data used to prefill form fields.
 * @returns An object containing the generated subCategories and navigation handlers.
 */
export function useGeneralForm(
  currentDetails: Partial<OrganizationDetails>,
  index = 0
) {
  const urlIndex = getIndexFromURL();
  const {
    diabeticSuppliesCopayMap,
    prepackagedCopaysMap,
    planDesignTypeMap,
    benefitPeriodsMap,
    yesNoMap,
    healthcareReformStatusMap,
    compoundManagementProgramMap,
    esiAccumPeriodMap,
  } = usePicklistMaps();
  const indexToUse = urlIndex !== undefined ? urlIndex : index;
  const designDetails =
    currentDetails?.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0];

  const productName = currentDetails?.plan?.product?.name;

  // Use existing config with the determined index
  const generalConfig = getGeneralPaths(indexToUse);

  // Use existing config with the determined index
  const xmlConfig = getXmlPaths(indexToUse);
  const esi = designDetails?.plan_design_detail_esi?.[0];

  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Benefit Name',
          'input',
          generalConfig.name,
          currentDetails?.plan?.plan_designs?.[indexToUse]?.name,
          {
            isRequired: true,
            placeholder: 'Benefit Name',
            validations: z
              .string()
              .max(255, 'Benefit Name must be less than 255 characters'),
          }
        ),
        defineFormField(
          'Benefit Order',
          'input',
          generalConfig.sort_seq,
          currentDetails?.plan?.plan_designs?.[indexToUse]?.sort_seq,
          {
            infoText:
              'The order in which benefits will appear on the PDX (1,2,3, etc).',
            placeholder: 'Benefit Order',
            validations: z
              .number()
              .int('Must be an integer')
              .min(-32768, 'Value must be at least -32,768')
              .max(32767, 'Value must be at most 32,767'),
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Benefit Plan Type',
          'dropdownSelect',
          generalConfig.type_ind,
          currentDetails?.plan?.plan_designs?.[indexToUse]?.type_ind,
          {
            optionsMap: planDesignTypeMap,
            isRequired: true,
            infoText: 'Denotes whether the benefit is a copay plan or HDHP.',
          }
        ),
        defineFormField(
          'Plan Design Benefit Period',
          'dropdownSelect',
          generalConfig.benefit_period_ind,
          designDetails?.benefit_period_ind,
          {
            optionsMap: benefitPeriodsMap,
            isRequired: true,
          }
        ),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Effective Date',
          'datepicker',
          generalConfig.effective_date,
          designDetails?.effective_date,
          {
            validations: z.date(),
          }
        ),
        defineFormField(
          'End Date',
          'datepicker',
          generalConfig.expiration_date,
          designDetails?.expiration_date,
          {
            validations: z.date().optional(),
          }
        ),
      ]),
    ]),
    ...(productName === productNames.CMK_360 ||
    productName === productNames.ESI_360 ||
    productName === productNames.OPT_360 ||
    productName === productNames.IRX_360 ||
    productName === productNames.CMK_DIRECT ||
    productName === productNames.ESI_DIRECT
      ? [
          defineSubCategory('', '', [
            defineInlineFieldGroup([
              defineFormField(
                'Union Benefit',
                'dropdownSelect',
                generalConfig.union_benefit_ind,
                designDetails?.union_benefit_ind,
                {
                  optionsMap: yesNoMap,
                }
              ),
              defineFormField(
                'Healthcare Reform Status',
                'dropdownSelect',
                generalConfig.healthcare_reform_status,
                designDetails?.healthcare_reform_status,
                {
                  optionsMap: healthcareReformStatusMap,
                  infoText:
                    'Indicates whether or not the plan is subject to ACA requirements.',
                }
              ),
            ]),
            defineInlineFieldGroup([
              defineFormField(
                'Overview Notes',
                'textarea',
                generalConfig.overview_notes,
                designDetails?.overview_notes,
                {
                  placeholder: 'Overview Notes',
                  validations: z
                    .string()
                    .max(
                      2000,
                      'Overview Notes must be less than 2000 characters'
                    )
                    .optional(),
                  rows: 5,
                  customProps: {
                    minHeight: '120px',
                    overflow: 'hidden',
                  },
                }
              ),
            ]),
          ]),
        ]
      : []),
    ...(productName === productNames.CMK_360 ||
    productName === productNames.ESI_360 ||
    productName === productNames.OPT_360 ||
    productName === productNames.IRX_360 ||
    productName === productNames.CMK_DIRECT ||
    productName === productNames.ESI_DIRECT
      ? [
          defineSubCategory('If Benefit Plan Type = HDHP, show', '', [
            defineInlineFieldGroup([
              defineFormField(
                'Qualified HDHP',
                'dropdownSelect',
                generalConfig.qualified_hdhp_ind,
                designDetails?.qualified_hdhp_ind,
                {
                  optionsMap: yesNoMap,
                  infoText:
                    'Does the plan meet current IRS guidelines to be a qualified HDHP?',
                }
              ),
              defineFormField('', 'text', '', undefined, {}),
            ]),
          ]),
        ]
      : []),
    ...(productName === productNames.ESI_360 ||
    productName === productNames.CMK_DIRECT ||
    productName === productNames.ESI_DIRECT
      ? [
          defineSubCategory('ESI Only', '', [
            defineFormField(
              'ESI BPLID',
              'input',
              generalConfig.esi_bplid,
              designDetails?.esi_bplid,
              {
                placeholder: 'ESI BPLID',
                validations: z.string(),
                isDisabled: true,
              }
            ),
          ]),
        ]
      : []),
    ...(productName === productNames.CMK_360 ||
    productName === productNames.ESI_360 ||
    productName === productNames.OPT_360 ||
    productName === productNames.IRX_360 ||
    productName === productNames.CMK_DIRECT ||
    productName === productNames.ESI_DIRECT
      ? [
          defineSubCategory('', '', [
            defineInlineFieldGroup([
              defineFormField(
                'Diabetic Meds and Supplies Copays',
                'dropdownSelect',
                generalConfig.diabetic_supplies_copay_setup_ind,
                designDetails?.diabetic_supplies_copay_setup_ind,
                {
                  optionsMap: diabeticSuppliesCopayMap,
                }
              ),
              defineFormField(
                'Copays for Pre-Packaged Drugs',
                'dropdownSelect',
                generalConfig.pre_packaged_copays_ind,
                designDetails?.pre_packaged_copays_ind,
                {
                  optionsMap: prepackagedCopaysMap,
                  infoText:
                    'Copay amount for drugs dispensed in 90 Day, unbreakable packaging.',
                }
              ),
            ]),
            defineInlineFieldGroup([
              defineFormField(
                'Diabetic Meds and Supplies Description',
                'textarea',
                generalConfig.diabetic_supplies_description,
                designDetails?.diabetic_supplies_description,
                {
                  placeholder: 'Description for diabetic meds and supplies',
                  validations: z.string().optional(),
                  rows: 5,
                  customProps: {
                    minHeight: '120px',
                    overflow: 'hidden',
                  },
                }
              ),
            ]),
            defineInlineFieldGroup([
              defineFormField(
                'Drug Specific Copay Note',
                'textarea',
                generalConfig.drug_specific_copay_note,
                designDetails?.drug_specific_copay_note,
                {
                  placeholder: 'Drug Specific Copay Note',
                  validations: z.string().optional(),
                }
              ),
            ]),
          ]),
        ]
      : []),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Does Deductible Apply to Maximum Out of Pocket?',
          'dropdownSelect',
          xmlConfig.deductible_apply_to_moop_ind,
          esi?.deductible_apply_to_moop_ind,

          {
            optionsMap: yesNoMap,
            validations: yesNoValidation,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Accumulation Period',
          'dropdownSelect',
          xmlConfig.deductible_moop_accum_period_ind,
          esi?.deductible_moop_accum_period_ind,

          {
            infoText: 'Accumulation Period (Calendar or Plan Year)',
            optionsMap: esiAccumPeriodMap,
            validations: z.string().optional(),
          }
        ),
        defineFormField(
          'Accumulator Start Date',
          'datepicker',
          xmlConfig.accumulator_start_date,
          esi?.accumulator_start_date,
          {
            validations: z
              .date({
                invalid_type_error: 'End Date must be a valid date',
              })
              .optional()
              .nullable(),
          }
        ),
      ]),
    ]),
    ...(productName === productNames.CMK_360 ||
    productName === productNames.ESI_360 ||
    productName === productNames.OPT_360 ||
    productName === productNames.CMK_DIRECT ||
    productName === productNames.ESI_DIRECT
      ? [
          defineSubCategory('', '', [
            defineInlineFieldGroup([
              defineFormField(
                'Compound Management Program',
                'dropdownSelect',
                generalConfig.compound_management_program_ind,
                designDetails?.compound_management_program_ind,

                {
                  optionsMap: compoundManagementProgramMap,
                }
              ),
            ]),
            defineInlineFieldGroup([
              defineFormField(
                'Non Compound Phone Number',
                'input',
                xmlConfig.non_compound_phone_number,
                esi?.non_compound_phone_number,

                {
                  infoText: 'Enter Non Compound Phone Number',
                  placeholder: 'Non Compound Phone Number',
                  validations: phoneValidation,
                }
              ),
              defineFormField(
                'Compound Phone Number',
                'input',
                xmlConfig.compound_phone_number,
                esi?.compound_phone_number,

                {
                  infoText: 'Enter Compound Phone Number',
                  placeholder: 'Compound Phone Number',
                  validations: phoneValidation,
                }
              ),
            ]),
          ]),
        ]
      : []),
  ];

  return {
    subCategories,
  };
}
