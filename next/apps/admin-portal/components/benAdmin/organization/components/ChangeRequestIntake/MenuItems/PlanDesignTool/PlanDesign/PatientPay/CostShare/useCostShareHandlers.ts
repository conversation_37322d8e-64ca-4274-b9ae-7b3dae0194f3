import { useToast } from '@chakra-ui/react';
import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import { CostShareTier } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PLAN_DESIGNS_BASE_PATH } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/coreConfig';
import { getGeneralPaths } from 'apps/admin-portal/components/benAdmin/organization/components/Tabs/PlanDesign/PlanDesign/Config/generalConfig';
import { useParams } from 'next/navigation';
import { useCallback, useMemo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  duplicateTier,
  getDaysSupplyForFirstPrepackageTier,
  getDaysSupplyForSecondPrepackageTier,
  getIndexFromURL,
} from './utils/costShareUtils';

/**
 * Custom hook to encapsulate all cost share handlers for the CostShareComponent.
 * @param formMethods - The react-hook-form methods
 * @param fieldPath - The path to the cost share tiers in the form
 * @param prePackagedIndFilter - The pre-packaged indicator filter
 * @param tableData - The filtered and transformed table data
 * @param onUpdateActiveItem - Navigation callback
 * @param backIndicator - Navigation back indicator
 * @param continueIndicator - Navigation continue indicator
 * @returns All handler functions and modal state
 */
export function useCostShareHandlers({
  formMethods,
  fieldPath,
  prePackagedIndFilter,
  tableData,
  onUpdateActiveItem,
  backIndicator,
  continueIndicator,
}: {
  formMethods: UseFormReturn<any>;
  fieldPath: string;
  prePackagedIndFilter?: number;
  tableData: any[];
  onUpdateActiveItem?: (id: string) => void;
  backIndicator?: string;
  continueIndicator?: string;
}) {
  const toast = useToast();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTierForModal, setSelectedTierForModal] =
    useState<CostShareTier | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [isDuplicating, setIsDuplicating] = useState<number | null>(null);
  const planDesignIndex = useMemo(() => getIndexFromURL(), []);
  const prepackagedCopaysInd = formMethods.watch(
    getGeneralPaths(planDesignIndex).pre_packaged_copays_ind
  );
  const params = useParams();
  const changeRequestId = params?.changeRequestId;

  const { useApiQuery } = useBenAdmin();
  const {
    refetch: { createObject: createCostShareTier },
  } = useApiQuery([
    {
      key: 'createObject',
      pathParams: {
        id: changeRequestId,
      },
      queryParams: {
        plan_design_id: formMethods.watch(
          `${PLAN_DESIGNS_BASE_PATH}.${planDesignIndex}.plan_design_id`
        ),
        object_name: 'cost_share_tier',
      },
      options: {
        enabled: false,
      },
    },
  ]);

  // Consolidated update handler
  const updateFormTiers = useCallback(
    (updater: (currentTiers: any[]) => any[]) => {
      const currentTiers = formMethods.getValues(fieldPath) || [];
      const updatedTiers = updater(currentTiers);
      formMethods.setValue(fieldPath, updatedTiers, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      });
    },
    [formMethods, fieldPath]
  );

  // Add new tier handler
  const handleAddNew = useCallback(async () => {
    setIsAdding(true);
    try {
      const { data: newTier } = await createCostShareTier();

      // For combined view (prePackagedIndFilter is undefined), preserve NULL from API
      // For separate views, use the specific filter value
      const finalPrePackagedInd =
        prePackagedIndFilter !== undefined
          ? prePackagedIndFilter.toString()
          : newTier.pre_packaged_ind; // Keep original API value (likely null)

      const newTierWithType = {
        ...newTier,
        pre_packaged_ind: finalPrePackagedInd,
      };

      updateFormTiers((currentTiers) => [...currentTiers, newTierWithType]);

      toast({
        title: 'Success',
        description: 'New cost share tier created successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error creating new cost share tier:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to create new cost share tier',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsAdding(false);
    }
  }, [updateFormTiers, prePackagedIndFilter, toast]);

  // Delete row handler
  const handleDeleteRow = useCallback(
    (rowIndex: number) => {
      const dataRow = tableData[rowIndex];
      if (!dataRow) return;
      updateFormTiers((currentTiers) =>
        currentTiers.filter((_: any, index: number) => index !== dataRow._index)
      );
    },
    [tableData, updateFormTiers]
  );

  // Duplicate row handler
  const handleDuplicateRow = useCallback(
    async (rowIndex: number) => {
      const dataRow = tableData[rowIndex];
      if (!dataRow) return;
      setIsDuplicating(rowIndex);
      try {
        // const identifierName = await getIdentifier('cost_share_tier_name');
        const { data: newTierObject } = await createCostShareTier();
        updateFormTiers((currentTiers) => {
          const tierToDuplicate = currentTiers[dataRow._index];
          if (!tierToDuplicate) return currentTiers;
          const duplicatedTier = {
            ...duplicateTier(tierToDuplicate, currentTiers),
            name: newTierObject.name,
            cost_share_tier_id: newTierObject.cost_share_tier_id,
          };
          return [
            ...currentTiers.slice(0, dataRow._index + 1),
            duplicatedTier,
            ...currentTiers.slice(dataRow._index + 1),
          ];
        });
        toast({
          title: 'Success',
          description: 'Cost share tier duplicated successfully',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } catch (error) {
        console.error('Error duplicating cost share tier:', error);
        toast({
          title: 'Error',
          description:
            error instanceof Error
              ? error.message
              : 'Failed to duplicate cost share tier',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsDuplicating(null);
      }
    },
    [tableData, updateFormTiers, toast]
  );

  // Modal edit handler
  const handleEditInModal = useCallback(
    (rowIndex: number) => {
      const dataRow = tableData[rowIndex];
      if (!dataRow) return;
      const currentTiers = formMethods.getValues(fieldPath) || [];
      const tierToEdit = currentTiers[dataRow._index];
      if (tierToEdit) {
        setSelectedTierForModal({
          ...tierToEdit,
          _originalIndex: dataRow._index,
        });
        setIsModalOpen(true);
      }
    },
    [tableData, formMethods, fieldPath]
  );

  // When Generate Prepackage Copay Tiers is selected, it creates two duplicates with slightly modified info in each duplicate
  const handleGeneratePrepackageTiers = useCallback(
    async (rowIndex: number) => {
      const dataRow = tableData[rowIndex];
      if (!dataRow) return;
      setIsDuplicating(rowIndex);
      try {
        const { data: firstBenefitTier } = await createCostShareTier();
        const { data: secondBenefitTier } = await createCostShareTier();
        updateFormTiers((currentTiers) => {
          const tierToDuplicate = currentTiers[dataRow._index];
          if (!tierToDuplicate) return currentTiers;
          const firstDuplicatedTier = {
            ...duplicateTier(tierToDuplicate, currentTiers),
            name: firstBenefitTier.name,
            cost_share_tier_id: firstBenefitTier.cost_share_tier_id,
            drug_list_ind: '116021',
            esi_copay_type_ind: '19',
            include_in_pbc: '0',
            include_in_pdx: '0',
            pbc_print_order: null,
            pdx_print_order: null,
            days_supply: getDaysSupplyForFirstPrepackageTier(
              prepackagedCopaysInd,
              tierToDuplicate.days_supply
            ),
          };
          const secondDuplicatedTier = {
            ...duplicateTier(tierToDuplicate, [
              ...currentTiers,
              firstDuplicatedTier,
            ]),
            name: secondBenefitTier.name,
            cost_share_tier_id: secondBenefitTier.cost_share_tier_id,
            drug_list_ind: '127367',
            esi_copay_type_ind: '19',
            include_in_pbc: '0',
            include_in_pdx: '0',
            pbc_print_order: null,
            pdx_print_order: null,
            days_supply: getDaysSupplyForSecondPrepackageTier(
              tierToDuplicate.days_supply
            ),
          };
          return [
            ...currentTiers.slice(0, dataRow._index + 1),
            firstDuplicatedTier,
            secondDuplicatedTier,
            ...currentTiers.slice(dataRow._index + 1),
          ];
        });
      } catch (error) {
        console.error('Error duplicating cost share tier:', error);
        toast({
          title: 'Error',
          description:
            error instanceof Error
              ? error.message
              : 'Failed to generate prepackage tiers',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsDuplicating(null);
      }
    },
    [tableData, updateFormTiers, toast]
  );

  // Modal save handler
  const handleModalSave = useCallback(
    (updatedTier: CostShareTier & { _originalIndex?: number }) => {
      const originalIndex = updatedTier._originalIndex;
      if (originalIndex === undefined) return;
      updateFormTiers((currentTiers) => {
        const updatedTiers = [...currentTiers];
        const { _originalIndex, ...cleanTier } = updatedTier;
        updatedTiers[originalIndex] = cleanTier;
        return updatedTiers;
      });
      setIsModalOpen(false);
      setSelectedTierForModal(null);
    },
    [updateFormTiers]
  );

  // Modal close handler
  const handleModalClose = useCallback(() => {
    setIsModalOpen(false);
    setSelectedTierForModal(null);
  }, []);

  // Navigation handlers
  const handleBack = useCallback(() => {
    if (backIndicator && onUpdateActiveItem) {
      onUpdateActiveItem(backIndicator);
    }
  }, [backIndicator, onUpdateActiveItem]);

  const handleContinue = useCallback(() => {
    if (continueIndicator && onUpdateActiveItem) {
      onUpdateActiveItem(continueIndicator);
    }
  }, [continueIndicator, onUpdateActiveItem]);

  // Cell update handler
  const handleCellUpdate = useCallback(
    (rowIndex: number, key: string, value: any) => {
      const dataRow = tableData[rowIndex];
      if (!dataRow) return;
      const formPath = `${fieldPath}[${dataRow._index}].${key}`;
      formMethods.setValue(formPath, value, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      });
    },
    [tableData, fieldPath, formMethods]
  );

  return {
    isModalOpen,
    selectedTierForModal,
    handleAddNew,
    handleDeleteRow,
    handleDuplicateRow,
    handleGeneratePrepackageTiers,
    handleEditInModal,
    handleModalSave,
    handleModalClose,
    handleBack,
    handleContinue,
    handleCellUpdate,
    isAdding,
    isDuplicating,
  };
}
