import {
  PLAN_DESIGNS_BASE_PATH,
  PlanDesign,
  PlanFeature,
  PlanFeatureItem,
} from 'apps/admin-portal/components/benAdmin';
import { UseFormReturn } from 'react-hook-form';

interface FeatureIndices {
  featureIndex: number;
  itemIndex: number;
}

interface IdMap {
  key: string;
  value: number;
}

// Get all plan design indices
const getPlanDesignIndices = (formMethods: any): number[] => {
  const planDesigns = formMethods.getValues(PLAN_DESIGNS_BASE_PATH);
  if (!Array.isArray(planDesigns)) return [];
  return Array.from({ length: planDesigns.length }, (_, i) => i);
};

// Find feature indices for a given plan design
const findFeatureIndices = (
  objectIndex: number,
  featureIdMap: IdMap,
  itemIdMap: IdMap,
  formMethods: any
): FeatureIndices | null => {
  const planDesign = formMethods.getValues(
    `${PLAN_DESIGNS_BASE_PATH}[${objectIndex}]`
  );
  if (!planDesign) return null;

  const planFeatures = planDesign?.plan_features;
  if (!planFeatures) return null;

  for (
    let featureIndex = 0;
    featureIndex < planFeatures.length;
    featureIndex++
  ) {
    const feature = planFeatures[featureIndex];
    if (feature[featureIdMap.key] === featureIdMap.value) {
      const featureItems = feature?.plan_feature_items;
      if (!featureItems) continue;

      for (let itemIndex = 0; itemIndex < featureItems.length; itemIndex++) {
        if (featureItems[itemIndex]?.[itemIdMap.key] === itemIdMap.value) {
          return { featureIndex, itemIndex };
        }
      }
    }
  }
  return null;
};

// Extract feature item and ID from the form path
const getBasePath = (path: string) => {
  const firstArrayIndex = path.indexOf('[');
  if (firstArrayIndex === -1) return path;

  // Find the path to the object containing the array
  const basePath = path.substring(0, firstArrayIndex);
  const lastArrayIndex = path.lastIndexOf(']');
  if (lastArrayIndex === -1) return basePath;

  // Return everything up to and including the last bracket
  return path.substring(0, lastArrayIndex + 1);
};

// Get item idMap from name
export const getIdMap = (name: string, formMethods: UseFormReturn<any>) => {
  const basePath = getBasePath(name);

  const item = formMethods?.getValues?.(basePath) as PlanFeatureItem;

  return getItemIdMap(item);
};

// Get ancillary idMap from name
export const getAncillaryIdMap = (
  name: string,
  formMethods: UseFormReturn<any>
) => {
  const basePath = getBasePath(name);
  const ancillary = formMethods?.getValues?.(basePath) as any;

  if (!ancillary || !ancillary.product_id) return null;

  return {
    key: 'product_id',
    value: ancillary.product_id,
  };
};

export const getFeatureIdMap = (feature: PlanFeature): IdMap | null => {
  if (!feature) return null;
  return feature.product_class_feature_id
    ? {
        key: 'product_class_feature_id',
        value: feature.product_class_feature_id,
      }
    : feature.product_feature_id
    ? { key: 'product_feature_id', value: feature.product_feature_id }
    : null;
};

export const getItemIdMap = (item: PlanFeatureItem): IdMap | null => {
  if (!item) return null;
  return item.product_class_feature_item_id
    ? {
        key: 'product_class_feature_item_id',
        value: item.product_class_feature_item_id,
      }
    : item.product_feature_item_id
    ? { key: 'product_feature_item_id', value: item.product_feature_item_id }
    : null;
};

const copyItemForFeature = (
  sourceItem: PlanFeatureItem,
  targetPlanFeatureId: number | undefined | null
): PlanFeatureItem => {
  const addedItem = { ...sourceItem };
  delete (addedItem as any).plan_feature_item_id;
  addedItem.plan_feature_id = targetPlanFeatureId;
  if (addedItem.plan_feature_id === null) {
    delete addedItem.plan_feature_id;
  }
  addedItem.value = null; // Set to null for new items
  return addedItem;
};

const copyFeatureForDesign = (
  sourceFeature: PlanFeature,
  targetPlanDesignId: number | undefined
): PlanFeature => {
  const addedFeature = { ...sourceFeature };
  delete (addedFeature as any).plan_feature_id;
  addedFeature.plan_design_id = targetPlanDesignId;
  addedFeature.plan_feature_items = addedFeature.plan_feature_items.map(
    (item) => copyItemForFeature(item, null) // Since new feature has no id yet
  );
  return addedFeature;
};

const syncFeatureItems = (
  targetFeature: PlanFeature,
  sourceFeature: PlanFeature
): PlanFeature => {
  const sourceItems = sourceFeature.plan_feature_items || [];
  const updatedTargetItems: PlanFeatureItem[] = [];

  sourceItems.forEach((sourceItem) => {
    const sourceIdMap = getItemIdMap(sourceItem);
    if (!sourceIdMap) return;

    const matchingTargetItem = targetFeature.plan_feature_items?.find((i) => {
      const iMap = getItemIdMap(i);
      return (
        iMap && iMap.key === sourceIdMap.key && iMap.value === sourceIdMap.value
      );
    });

    if (matchingTargetItem) {
      updatedTargetItems.push(matchingTargetItem);
    } else {
      updatedTargetItems.push(
        copyItemForFeature(sourceItem, targetFeature.plan_feature_id)
      );
    }
  });

  targetFeature.plan_feature_items = updatedTargetItems;
  return targetFeature;
};

export const syncPlanDesignStructure = (
  targetDesign: PlanDesign,
  sourceDesign: PlanDesign
) => {
  const sourceFeatures = sourceDesign.plan_features || [];
  const updatedTargetFeatures: PlanFeature[] = [];

  sourceFeatures.forEach((sourceFeature) => {
    const sourceIdMap = getFeatureIdMap(sourceFeature);
    if (!sourceIdMap) return;

    const matchingTargetFeature = targetDesign.plan_features?.find((f) => {
      const fMap = getFeatureIdMap(f);
      return (
        fMap && fMap.key === sourceIdMap.key && fMap.value === sourceIdMap.value
      );
    });

    if (matchingTargetFeature) {
      updatedTargetFeatures.push(
        syncFeatureItems(matchingTargetFeature, sourceFeature)
      );
    } else {
      updatedTargetFeatures.push(
        copyFeatureForDesign(sourceFeature, targetDesign.plan_design_id)
      );
    }
  });

  targetDesign.plan_features = updatedTargetFeatures;

  // Cleanup null plan_feature_id in items
  updatedTargetFeatures.forEach((feature) => {
    feature.plan_feature_items.forEach((item) => {
      if (item.plan_feature_id === null) {
        delete item.plan_feature_id;
      }
    });
  });
};

// Add at the top after imports
export const WATCHED_ANCILLARY_FIELDS = [
  'effective_date',
  'expiration_date',
  'eligibility_export_ind',
  'pharmacy_claim_export_ind',
  'notes',
];

// Update syncAcrossPlanDesigns
// Updated syncAcrossPlanDesigns function without _differs logic
export const syncAcrossPlanDesigns = (
  sync: boolean,
  toggleValue: boolean,
  name: string,
  value: string | number | null,
  formMethods: any
): void => {
  if (!formMethods || typeof formMethods.getValues !== 'function') {
    console.warn(
      'formMethods is undefined or missing getValues method in syncAcrossPlanDesigns'
    );
    return;
  }

  // If sync is disabled, exit early
  if (!sync) return;

  let isAncillary = false;
  let parsed: any = null;
  let fieldName = '';

  // Parse for ancillary
  const ancillaryMatches = name.match(
    /plan_designs\[(\d+)\]\.plan_design_ancillaries\[(\d+)\]\.(.+)/
  );
  if (ancillaryMatches) {
    isAncillary = true;
    parsed = {
      planDesignIndex: parseInt(ancillaryMatches[1]),
      ancillaryIndex: parseInt(ancillaryMatches[2]),
    };
    fieldName = ancillaryMatches[3];
  } else {
    // Original feature parse
    const featureMatches = name.match(
      /plan_designs\[(\d+)\]\.plan_features\[(\d+)\]\.plan_feature_items\[(\d+)\]/
    );
    if (!featureMatches) return;
    parsed = {
      planDesignIndex: parseInt(featureMatches[1]),
      featureIndex: parseInt(featureMatches[2]),
      itemIndex: parseInt(featureMatches[3]),
    };
  }

  if (!parsed) return;

  const { planDesignIndex } = parsed;

  let matchCriteria: any = null;

  if (isAncillary) {
    const currentAncillaryPath = `${PLAN_DESIGNS_BASE_PATH}[${planDesignIndex}].plan_design_ancillaries[${parsed.ancillaryIndex}]`;
    const currentAncillary = formMethods.getValues(currentAncillaryPath);
    if (!currentAncillary) return;
    matchCriteria = {
      product_id: currentAncillary.product_id,
      product_options: currentAncillary.product_options,
    };
  } else {
    const currentFeaturePath = `${PLAN_DESIGNS_BASE_PATH}[${planDesignIndex}].plan_features[${parsed.featureIndex}]`;
    const currentFeature = formMethods.getValues(currentFeaturePath);
    if (!currentFeature) return;

    const featureIdMap = getFeatureIdMap(currentFeature);
    if (!featureIdMap) return;

    const itemIdMap = getIdMap(name, formMethods);
    if (!itemIdMap) return;

    matchCriteria = { featureIdMap, itemIdMap };
  }

  const planDesignIndices = getPlanDesignIndices(formMethods);

  planDesignIndices.forEach((designIndex) => {
    if (designIndex === planDesignIndex) return;

    let syncName = '';

    if (isAncillary) {
      const targetAncillariesPath = `${PLAN_DESIGNS_BASE_PATH}[${designIndex}].plan_design_ancillaries`;
      const targetAncillaries =
        formMethods.getValues(targetAncillariesPath) || [];
      let matchingIndex = -1;
      for (let i = 0; i < targetAncillaries.length; i++) {
        const anc = targetAncillaries[i];
        if (
          anc.product_id === matchCriteria.product_id &&
          anc.product_options === matchCriteria.product_options
        ) {
          matchingIndex = i;
          break;
        }
      }
      if (matchingIndex === -1) return;
      syncName = `${targetAncillariesPath}[${matchingIndex}].${fieldName}`;
    } else {
      const featureIndices = findFeatureIndices(
        designIndex,
        matchCriteria.featureIdMap,
        matchCriteria.itemIdMap,
        formMethods
      );
      if (!featureIndices) return;
      syncName = `${PLAN_DESIGNS_BASE_PATH}[${designIndex}].plan_features[${featureIndices.featureIndex}].plan_feature_items[${featureIndices.itemIndex}].value`;
    }

    // Simply sync the value without checking for any toggle states
    formMethods.setValue(syncName, value, {
      shouldValidate: true,
      shouldDirty: true,
      shouldTouch: true,
    });
  });
};

// New optimized function to compare feature item values across all plan designs
export const compareFeatureItemValuesAcrossPlanDesigns = (
  formMethods: any,
  planDesignIndex: number,
  featureIndex: number,
  itemIndex: number
): { shouldInherit: boolean; value: any } => {
  const planDesigns = formMethods.getValues(PLAN_DESIGNS_BASE_PATH);
  if (!Array.isArray(planDesigns) || planDesigns.length <= 1) {
    return { shouldInherit: false, value: null };
  }

  // Get the current feature item path
  const currentFeaturePath = `${PLAN_DESIGNS_BASE_PATH}[${planDesignIndex}].plan_features[${featureIndex}]`;
  const currentFeature = formMethods.getValues(currentFeaturePath);
  if (!currentFeature) return { shouldInherit: false, value: null };

  const featureIdMap = getFeatureIdMap(currentFeature);
  if (!featureIdMap) return { shouldInherit: false, value: null };

  const currentItemPath = `${PLAN_DESIGNS_BASE_PATH}[${planDesignIndex}].plan_features[${featureIndex}].plan_feature_items[${itemIndex}]`;
  const currentItem = formMethods.getValues(currentItemPath);
  if (!currentItem) return { shouldInherit: false, value: null };

  const itemIdMap = getItemIdMap(currentItem);
  if (!itemIdMap) return { shouldInherit: false, value: null };

  // Collect all values for this feature item across all plan designs
  const values: any[] = [];
  const planDesignIndices = getPlanDesignIndices(formMethods);

  for (const designIndex of planDesignIndices) {
    const featureIndices = findFeatureIndices(
      designIndex,
      featureIdMap,
      itemIdMap,
      formMethods
    );

    if (featureIndices) {
      const valuePath = `${PLAN_DESIGNS_BASE_PATH}[${designIndex}].plan_features[${featureIndices.featureIndex}].plan_feature_items[${featureIndices.itemIndex}].value`;
      const value = formMethods.getValues(valuePath);
      if (value != null) {
        values.push(value);
      }
    }
  }

  // If we have values and they're all the same, we should inherit
  if (values.length > 0 && values.every((val) => val === values[0])) {
    return { shouldInherit: true, value: values[0] };
  }

  return { shouldInherit: false, value: null };
};

// New optimized function to compare ancillary field values across all plan designs
export const compareAncillaryFieldValuesAcrossPlanDesigns = (
  formMethods: any,
  planDesignIndex: number,
  ancillaryIndex: number,
  fieldName: string
): { shouldInherit: boolean; value: any } => {
  const planDesigns = formMethods.getValues(PLAN_DESIGNS_BASE_PATH);
  if (!Array.isArray(planDesigns) || planDesigns.length <= 1) {
    return { shouldInherit: false, value: null };
  }

  // Get the current ancillary path
  const currentAncillaryPath = `${PLAN_DESIGNS_BASE_PATH}[${planDesignIndex}].plan_design_ancillaries[${ancillaryIndex}]`;
  const currentAncillary = formMethods.getValues(currentAncillaryPath);
  if (!currentAncillary) return { shouldInherit: false, value: null };

  const matchCriteria = {
    product_id: currentAncillary.product_id,
    product_options: currentAncillary.product_options,
  };

  // Collect all values for this ancillary field across all plan designs
  const values: any[] = [];
  const planDesignIndices = getPlanDesignIndices(formMethods);

  for (const designIndex of planDesignIndices) {
    const targetAncillariesPath = `${PLAN_DESIGNS_BASE_PATH}[${designIndex}].plan_design_ancillaries`;
    const targetAncillaries =
      formMethods.getValues(targetAncillariesPath) || [];

    for (let i = 0; i < targetAncillaries.length; i++) {
      const anc = targetAncillaries[i];
      if (
        anc.product_id === matchCriteria.product_id &&
        anc.product_options === matchCriteria.product_options
      ) {
        const fieldPath = `${targetAncillariesPath}[${i}].${fieldName}`;
        const value = formMethods.getValues(fieldPath);
        if (value != null) {
          values.push(value);
        }
        break;
      }
    }
  }

  // If we have values and they're all the same, we should inherit
  if (values.length > 0 && values.every((val) => val === values[0])) {
    return { shouldInherit: true, value: values[0] };
  }

  return { shouldInherit: false, value: null };
};

// New optimized function to sync values to new plan design based on comparison
export const syncValuesToNewPlanDesign = (
  formMethods: any,
  newPlanDesignIndex: number
): void => {
  const planDesigns = formMethods.getValues(PLAN_DESIGNS_BASE_PATH);
  if (!Array.isArray(planDesigns) || planDesigns.length <= 1) return;

  // Get the first plan design as reference
  const firstDesign = formMethods.getValues(`${PLAN_DESIGNS_BASE_PATH}[0]`);

  // Sync features
  if (firstDesign.plan_features && Array.isArray(firstDesign.plan_features)) {
    firstDesign.plan_features.forEach((feat: any, featIdx: number) => {
      if (feat.plan_feature_items && Array.isArray(feat.plan_feature_items)) {
        feat.plan_feature_items.forEach((item: any, itemIdx: number) => {
          if (item.value != null) {
            const comparison = compareFeatureItemValuesAcrossPlanDesigns(
              formMethods,
              0, // Use first plan design as reference
              featIdx,
              itemIdx
            );

            if (comparison.shouldInherit) {
              // Find the corresponding feature and item in the new plan design
              const newDesign = formMethods.getValues(
                `${PLAN_DESIGNS_BASE_PATH}[${newPlanDesignIndex}]`
              );
              if (
                newDesign.plan_features &&
                Array.isArray(newDesign.plan_features)
              ) {
                const featureIdMap = getFeatureIdMap(feat);
                if (featureIdMap) {
                  // Find matching feature in new plan design
                  for (
                    let newFeatIdx = 0;
                    newFeatIdx < newDesign.plan_features.length;
                    newFeatIdx++
                  ) {
                    const newFeat = newDesign.plan_features[newFeatIdx];
                    if (newFeat[featureIdMap.key] === featureIdMap.value) {
                      if (
                        newFeat.plan_feature_items &&
                        Array.isArray(newFeat.plan_feature_items)
                      ) {
                        const itemIdMap = getItemIdMap(item);
                        if (itemIdMap) {
                          // Find matching item in new plan design
                          for (
                            let newItemIdx = 0;
                            newItemIdx < newFeat.plan_feature_items.length;
                            newItemIdx++
                          ) {
                            const newItem =
                              newFeat.plan_feature_items[newItemIdx];
                            if (newItem[itemIdMap.key] === itemIdMap.value) {
                              const newValuePath = `${PLAN_DESIGNS_BASE_PATH}[${newPlanDesignIndex}].plan_features[${newFeatIdx}].plan_feature_items[${newItemIdx}].value`;
                              formMethods.setValue(
                                newValuePath,
                                comparison.value,
                                {
                                  shouldValidate: true,
                                  shouldDirty: true,
                                  shouldTouch: true,
                                }
                              );
                              break;
                            }
                          }
                        }
                      }
                      break;
                    }
                  }
                }
              }
            }
          }
        });
      }
    });
  }

  // Sync ancillaries
  if (
    firstDesign.plan_design_ancillaries &&
    Array.isArray(firstDesign.plan_design_ancillaries)
  ) {
    firstDesign.plan_design_ancillaries.forEach((anc: any, ancIdx: number) => {
      WATCHED_ANCILLARY_FIELDS.forEach((field: string) => {
        const comparison = compareAncillaryFieldValuesAcrossPlanDesigns(
          formMethods,
          0, // Use first plan design as reference
          ancIdx,
          field
        );

        if (comparison.shouldInherit) {
          // Find the corresponding ancillary in the new plan design
          const newDesign = formMethods.getValues(
            `${PLAN_DESIGNS_BASE_PATH}[${newPlanDesignIndex}]`
          );
          if (
            newDesign.plan_design_ancillaries &&
            Array.isArray(newDesign.plan_design_ancillaries)
          ) {
            for (
              let newAncIdx = 0;
              newAncIdx < newDesign.plan_design_ancillaries.length;
              newAncIdx++
            ) {
              const newAnc = newDesign.plan_design_ancillaries[newAncIdx];
              if (
                newAnc.product_id === anc.product_id &&
                newAnc.product_options === anc.product_options
              ) {
                const newFieldPath = `${PLAN_DESIGNS_BASE_PATH}[${newPlanDesignIndex}].plan_design_ancillaries[${newAncIdx}].${field}`;
                formMethods.setValue(newFieldPath, comparison.value, {
                  shouldValidate: true,
                  shouldDirty: true,
                  shouldTouch: true,
                });
                break;
              }
            }
          }
        }
      });
    });
  }
};
