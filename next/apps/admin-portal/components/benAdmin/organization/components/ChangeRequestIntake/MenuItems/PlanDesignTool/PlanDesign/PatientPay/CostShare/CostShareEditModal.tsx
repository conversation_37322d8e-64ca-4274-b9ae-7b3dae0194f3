import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalO<PERSON>lay,
  VStack,
} from '@chakra-ui/react';
import { CostShareTier } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import React, { useCallback, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import z from 'zod';

import {
  INCLUDE_PBC_OPTIONS,
  LESSER_GREATER_OPTIONS,
  RowValidationState,
} from './utils/costShareUtils';

type ValidationEntry = {
  validation_message?: string;
  field: string;
  message: string;
};

type RowValidation = {
  hasErrors: boolean;
  hasWarnings: boolean;
  fieldErrors: Record<string, ValidationEntry>;
  fieldWarnings: Record<string, ValidationEntry>;
};

interface CostShareEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  tier: CostShareTier | null;
  onSave: (updatedTier: CostShareTier) => void;
  prePackagedIndFilter?: number;
  /** Whether this is an ESI product - determines if ESI-only fields should be shown */
  isESI?: boolean;
  validationMap?: Map<number, RowValidationState>;
  refetch?: () => Promise<any>;
}

// Form field factory to reduce repetition
const createFormField = (
  label: string,
  type:
    | 'input'
    | 'datepicker'
    | 'dropdownSelect'
    | 'text'
    | 'textarea'
    | 'radioGroup'
    | 'checkboxGroup',
  key: string,
  value: any,
  options?: any
) => defineFormField(label, type, key, value || '', options || {});

// Create form subcategories factory
const createSubCategories = (
  tier: CostShareTier | null,
  maps: any,
  isESI = false
): SubCategoryType[] => [
  defineSubCategory('Basic Information', '', [
    createFormField('Cost Share Tier Name', 'input', 'name', tier?.name, {
      placeholder: 'Enter tier name...',
      isDisabled: true,
    }),
    createFormField(
      'Effective Date',
      'datepicker',
      'effective_date',
      tier?.effective_date,
      {
        placeholder: 'Select effective date',
      }
    ),
    createFormField(
      'Expiration Date',
      'datepicker',
      'expiration_date',
      tier?.expiration_date,
      {
        placeholder: 'Select expiration date',
      }
    ),
  ]),
  defineSubCategory('Tier Configuration', '', [
    createFormField('Drug Tier', 'dropdownSelect', 'tier_ind', tier?.tier_ind, {
      optionsMap: maps.costShareTierMap,
      placeholder: 'Select tier...',
      allowSearch: true,
    }),
    createFormField(
      'Pharmacy Channel',
      'dropdownSelect',
      'pharmacy_channel_ind',
      tier?.pharmacy_channel_ind,
      {
        optionsMap: maps.pharmacyChannelMap,
        placeholder: 'Select channel...',
        allowSearch: true,
      }
    ),
    createFormField(
      'Days Supply',
      'dropdownSelect',
      'days_supply',
      tier?.days_supply,
      {
        optionsMap: maps.costShareTierDaysSupplyMap,
        placeholder: 'Select days...',
      }
    ),
  ]),
  defineSubCategory('Cost Configuration', '', [
    createFormField(
      'Copay Amount',
      'input',
      'co_pay_amount',
      tier?.co_pay_amount,
      {
        placeholder: 'Enter amount...',
      }
    ),
    createFormField(
      'Coinsurance %',
      'input',
      'co_insurance_pct',
      tier?.co_insurance_pct,
      {
        placeholder: 'Enter percentage...',
      }
    ),
    createFormField(
      'Lesser/Greater',
      'dropdownSelect',
      'co_insurance_ltgt_ind',
      tier?.co_insurance_ltgt_ind,
      {
        optionsMap: LESSER_GREATER_OPTIONS,
        placeholder: 'Select...',
      }
    ),
    createFormField(
      'Min Amount',
      'input',
      'co_insurance_min_amount',
      tier?.co_insurance_min_amount,
      {
        placeholder: 'Enter minimum amount...',
      }
    ),
    createFormField(
      'Max Amount',
      'input',
      'co_insurance_max_amount',
      tier?.co_insurance_max_amount,
      {
        placeholder: 'Enter maximum amount...',
      }
    ),
  ]),
  defineSubCategory('Additional Settings', '', [
    createFormField(
      'Network Status',
      'dropdownSelect',
      'network_status_ind',
      tier?.network_status_ind,
      {
        optionsMap: maps.networkStatusMap,
        placeholder: 'Select status...',
      }
    ),
    createFormField(
      'Prepackage Indicator',
      'dropdownSelect',
      'pre_packaged_ind',
      tier?.pre_packaged_ind,
      {
        optionsMap: maps.yesNoMap,
        placeholder: 'Select...',
      }
    ),
    // ESI-only fields
    ...(isESI
      ? [
          createFormField(
            'Drug List',
            'dropdownSelect',
            'drug_list_ind',
            tier?.drug_list_ind,
            {
              optionsMap: maps.esiDrugListMap,
              placeholder: 'Select list...',
              allowSearch: true,
            }
          ),
          createFormField(
            'Copay Type Indicator',
            'dropdownSelect',
            'esi_copay_type_ind',
            tier?.esi_copay_type_ind,
            {
              optionsMap: maps.esiCopayTierMap,
              placeholder: 'Select type...',
            }
          ),
          createFormField(
            'Copay Channel Delivery System',
            'dropdownSelect',
            'esi_copay_channel_ind',
            tier?.esi_copay_channel_ind,
            {
              optionsMap: maps.esiCopayChannelMap,
              placeholder: 'Select channel...',
            }
          ),
          createFormField(
            'Copay Structure',
            'dropdownSelect',
            'esi_copay_structure_ind',
            tier?.esi_copay_structure_ind,
            {
              optionsMap: maps.esiCopayStructureMap,
              placeholder: 'Select structure...',
            }
          ),
          createFormField(
            'Copay Network',
            'dropdownSelect',
            'esi_copay_network_ind',
            tier?.esi_copay_network_ind,
            {
              optionsMap: maps.esiCopayNetworkMap,
              placeholder: 'Select network...',
            }
          ),
        ]
      : []),
    createFormField(
      'Include PBC',
      'dropdownSelect',
      'include_in_pbc',
      tier?.include_in_pbc,
      {
        optionsMap: INCLUDE_PBC_OPTIONS,
        placeholder: 'Select...',
      }
    ),
    createFormField(
      'Include in PDX',
      'dropdownSelect',
      'include_in_pdx',
      tier?.include_in_pdx,
      {
        optionsMap: INCLUDE_PBC_OPTIONS,
        placeholder: 'Select...',
      }
    ),
    createFormField(
      'PBC Print Order',
      'input',
      'pbc_print_order',
      tier?.pbc_print_order,
      {
        placeholder: 'Enter print order...',
      }
    ),
    createFormField(
      'PDX Print Order',
      'input',
      'pdx_print_order',
      tier?.pdx_print_order,
      {
        placeholder: 'Enter print order...',
      }
    ),
    createFormField(
      'Drug List Note',
      'textarea',
      'drug_list_notes',
      tier?.drug_list_notes,
      {
        placeholder: 'Notes',
        infoText: 'Enter Drug List Note',
        validations: z
          .string()
          .max(2000, 'Value must be less than 2000 characters')
          .optional()
          .nullable(),
        rows: 1,
        customProps: {
          minHeight: '40px',
          style: {
            minHeight: '40px',
          },
        },
      }
    ),
  ]),
];

const CostShareEditModal: React.FC<CostShareEditModalProps> = ({
  isOpen,
  onClose,
  tier,
  onSave,
  prePackagedIndFilter = 0,
  isESI = false,
  validationMap,
  refetch,
}) => {
  const picklistMaps = usePicklistMaps();

  // Initialize form with tier data
  const modalFormMethods = useForm({
    defaultValues: tier || {},
    mode: 'onChange',
  });

  // Reset form when tier changes - optimized to only reset when tier actually changes
  useEffect(() => {
    if (tier && isOpen) {
      modalFormMethods.reset(tier);
    }

    if (!isOpen || !tier) return;

    const originalIndex = (tier as any)._originalIndex;
    if (originalIndex == null) return;
    const rowValidation = validationMap?.get(originalIndex) as
      | RowValidation
      | undefined;
    if (!rowValidation) return;

    const { fieldErrors = {}, fieldWarnings = {} } = rowValidation;
    Object.entries(fieldErrors).forEach(([fieldKey, entry]) => {
      modalFormMethods.setError(fieldKey as keyof CostShareTier, {
        type: 'field-error',
        message: entry.message || entry?.validation_message,
      });
    });

    Object.entries(fieldWarnings).forEach(([fieldKey, entry]) => {
      modalFormMethods.setError(fieldKey as keyof CostShareTier, {
        type: 'field-warning',
        message: entry.message || entry?.validation_message,
      });
    });
  }, [tier, isOpen, modalFormMethods, validationMap]);

  // Memoized subcategories - only recreate when tier, maps, or ESI status change
  const subCategories = useMemo(
    () => createSubCategories(tier, picklistMaps, isESI),
    [tier, picklistMaps, isESI]
  );

  // Memoized modal title
  const modalTitle = useMemo(
    () => `Edit Cost Share Tier${tier?.name ? ` - ${tier.name}` : ''}`,
    [tier?.name]
  );

  // Optimized handlers
  const handleSave = useCallback(() => {
    const formData = modalFormMethods.getValues();
    const updatedTier: CostShareTier = {
      ...formData,
      pre_packaged_ind: prePackagedIndFilter,
    };
    onSave(updatedTier);
    onClose();
  }, [modalFormMethods, onSave, onClose, prePackagedIndFilter]);

  const handleCancel = useCallback(() => {
    if (tier) {
      modalFormMethods.reset(tier);
    }
    onClose();
  }, [tier, modalFormMethods, onClose]);

  // Early return if no tier
  if (!tier) return null;

  const isFormDirty = modalFormMethods.formState.isDirty;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleCancel}
      size="4xl"
      scrollBehavior="inside"
    >
      <ModalOverlay />
      <ModalContent maxW="800px">
        <ModalHeader>{modalTitle}</ModalHeader>
        <ModalCloseButton />

        <ModalBody pb={6}>
          <VStack spacing={6} align="stretch">
            <GenericForm
              formName="Cost Share Tier Details"
              formDescription="Edit the cost share tier configuration below."
              subCategories={subCategories}
              formMethods={modalFormMethods}
              showButtons={false}
              isInModal={true}
            />
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button
            colorScheme="gray"
            mr={3}
            onClick={handleCancel}
            variant="outline"
          >
            Cancel
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSave}
            isDisabled={!isFormDirty}
          >
            Save Changes
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default React.memo(CostShareEditModal);
