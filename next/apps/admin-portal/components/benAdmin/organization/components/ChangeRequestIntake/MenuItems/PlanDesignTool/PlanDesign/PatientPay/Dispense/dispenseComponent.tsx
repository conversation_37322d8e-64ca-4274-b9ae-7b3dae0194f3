import { Flex } from '@chakra-ui/react';
import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import {
  ACCUMULATORS_GENERAL_ITEM,
  COMPOUND_ITEM,
  //   useSaveChangeRequestHandler,
} from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useValidateByPageFromContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
// import { createPlanDesignSubmitHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planSubmitHandlers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { normalizePlanDesignFieldPath } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/filterUtils';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import React, { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../../IntakeComponents/HelpCenter/HelpCenter';
import { DISPENSE_ITEM } from '../../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../../Navigation/uiContextEnum';
import { useDispenseAsWrittenForm } from './dispenseForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const DispenseComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();
  const planDesignIndex = getIndexFromURL();

  const { useApiQuery } = useBenAdmin();
  const { picklist: dispenseAsWritten } = useApiQuery([
    {
      key: 'picklist',
      queryParams: { names: 'DispenseAsWritten' },
      options: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        cacheTime: 10 * 60 * 1000, // 10 minutes
      },
    },
  ]);

  console.log(dispenseAsWritten);

  const { refetch, validationData, isLoading } =
    useValidateByPageFromContext(DISPENSE_ITEM);

  const { helpCenterData, isFetching } = useHelpCenter(
    DISPENSE_ITEM,
    'plan_design',
    planDesignIndex
  );

  const uiContextIndex = getUIContextFromNavigationConstant(DISPENSE_ITEM);

  //   const baseSaveHandler = useSaveChangeRequestHandler(formMethods);

  // Create an enhanced submit handler that handles multi-plan updates
  //   const submitHandler = createPlanDesignSubmitHandler(baseSaveHandler);

  // Handle save and exit action
  //   const handleSaveAndExit = useCallback(() => {
  //     const currentValues = getValues();
  //     submitHandler(currentValues);
  //   }, [getValues, submitHandler]);

  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useDispenseAsWrittenForm(
    currentDetails as Partial<OrganizationDetails>
  );

  const { createContinueHandler, isContinueLoading } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  // Create the continue handler for this specific page
  const handleContinue = createContinueHandler(
    DISPENSE_ITEM,
    ACCUMULATORS_GENERAL_ITEM
  );

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(COMPOUND_ITEM);
    }
  };

  useEffect(() => {
    const UI_CONTEXT_INDEX = getUIContextFromNavigationConstant(DISPENSE_ITEM);

    if (UI_CONTEXT_INDEX) {
      const currentPageValidation = validationData?.results?.[UI_CONTEXT_INDEX];

      if (!currentPageValidation) return;

      const { errors = [], warnings = [] } = currentPageValidation;

      // Handle errors
      errors.forEach(({ field, message }) => {
        const normalizedPath = normalizePlanDesignFieldPath(field, true);
        if (normalizedPath && message) {
          formMethods.setError(normalizedPath, {
            type: 'field-error',
            message,
          });
        }
      });

      // Handle warnings
      warnings.forEach(({ field, message }) => {
        const normalizedPath = normalizePlanDesignFieldPath(field, true);
        if (normalizedPath && message) {
          formMethods.setError(normalizedPath, {
            type: 'field-warning',
            message,
          });
        }
      });
    }
  }, [validationData, formMethods]);

  // Use continue loading state or validation loading state, whichever is true
  const isProcessing = isContinueLoading || isLoading;
  return (
    <Flex justify="space-between">
      <GenericForm
        formMethods={formMethods}
        formName="Dispense as Written"
        formDescription=""
        subCategories={subCategories}
        onContinue={handleContinue}
        onBack={handleBack}
        // onSaveExit={handleSaveAndExit}
        isProcessing={isProcessing}
      />
      <HelpCenter
        validationResults={
          uiContextIndex
            ? validationData?.results?.[uiContextIndex]?.validation_results
            : undefined
        }
        helpContent={helpCenterData}
        isLoading={isFetching}
      />
    </Flex>
  );
};

export default DispenseComponent;
