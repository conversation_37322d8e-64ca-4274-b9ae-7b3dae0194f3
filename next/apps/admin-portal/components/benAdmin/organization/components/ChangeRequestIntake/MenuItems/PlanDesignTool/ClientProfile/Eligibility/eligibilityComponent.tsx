import { Flex } from '@chakra-ui/react';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useValidateByPageFromContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
import { useValidationErrorsForForm } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useValidationErrorsForForm';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  CLAIMS_COVER_ITEM,
  ELIGIBILITY_ITEM,
  IMPLEMENTATION_ITEM,
} from '../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../Navigation/uiContextEnum';
import { useEligibilityForm } from './eligibilityForm';

const EligibilityComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  const { watch } = formMethods;
  const currentDetails = watch();

  const { refetch, validationData, isLoading } =
    useValidateByPageFromContext(ELIGIBILITY_ITEM);

  const { helpCenterData, isFetching } = useHelpCenter(ELIGIBILITY_ITEM);

  const uiContextIndex = getUIContextFromNavigationConstant(ELIGIBILITY_ITEM);

  const { subCategories } = useEligibilityForm(
    currentDetails as Partial<OrganizationDetails>,
    formMethods
  );

  //   const submitHandler = useSaveChangeRequestHandler(formMethods);

  //   const handleSaveAndExit = () => {
  //     // Submit the current form data
  //     const currentValues = formMethods.getValues();
  //     submitHandler(currentValues);
  //   };

  const { createContinueHandler, isContinueLoading } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  const handleContinue = createContinueHandler(
    ELIGIBILITY_ITEM,
    CLAIMS_COVER_ITEM
  );

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(IMPLEMENTATION_ITEM);
    }
  };

  const contextIndex = getUIContextFromNavigationConstant(ELIGIBILITY_ITEM);

  useValidationErrorsForForm({
    subCategories,
    formMethods,
    validationData,
    contextIndex,
  });

  // Use continue loading state or validation loading state, whichever is true
  const isProcessing = isContinueLoading || isLoading;

  return (
    <Flex justify="space-between">
      <GenericForm
        formMethods={formMethods}
        formName="Eligibility"
        formDescription=""
        subCategories={subCategories}
        onContinue={handleContinue}
        onBack={handleBack}
        // onSaveExit={handleSaveAndExit}
        isProcessing={isProcessing}
      />
      <HelpCenter
        validationResults={
          uiContextIndex
            ? validationData?.results?.[uiContextIndex]?.validation_results
            : undefined
        }
        helpContent={helpCenterData}
        isLoading={isFetching}
      />
    </Flex>
  );
};

export default EligibilityComponent;
