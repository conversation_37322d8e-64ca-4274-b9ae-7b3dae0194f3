// ReviewProductsAndServices.tsx
import {
  PLAN_DESIGN_LIST_ITEM,
  usePlanDesignSections,
} from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useIsESIProduct } from 'apps/admin-portal/components/benAdmin/organization/hooks/useIsESIHook';
import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import FormReviewSummary from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/FormReviewSummary';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

interface ReviewProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
  currentDetails?: Partial<OrganizationDetails>;
}

const ReviewPlanDesigns: React.FC<ReviewProps> = ({
  formMethods,
  onUpdateActiveItem,
  currentDetails,
}) => {
  const isESI = useIsESIProduct(formMethods);
  const saveHandler = useSaveChangeRequestHandler(formMethods);
  const formSections = usePlanDesignSections(currentDetails, isESI);

  const handleNavigation = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(PLAN_DESIGN_LIST_ITEM);
    }
  };

  const handleSaveExit = () => {
    const formData = formMethods.getValues();
    saveHandler(formData);
  };

  return (
    <FormReviewSummary
      title="Review and Save Changes"
      description=""
      subtitle="Plan Design"
      formMethods={formMethods}
      formSections={formSections}
      onBack={handleNavigation}
      onSaveExit={handleSaveExit}
    />
  );
};

export default ReviewPlanDesigns;
