import { Flex } from '@chakra-ui/react';
import {
  DISPENSE_ITEM,
  PLAN_DESIGNS_BASE_PATH,
  UNBREAKABLE_ITEM,
  //   useSaveChangeRequestHandler,
} from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useValidateByPageFromContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
// import { createPlanDesignSubmitHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planSubmitHandlers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { useSearchParams } from 'next/navigation';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../../IntakeComponents/HelpCenter/HelpCenter';
import { COMPOUND_ITEM } from '../../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../../Navigation/uiContextEnum';
import { useCompoundForm } from './compoundForm';

interface CompoundComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

/**
 * CompoundComponent
 *
 * Handles the compound coverage configuration for a plan design.
 * Allows users to specify if compounds are covered and select which
 * patient pay tier applies to compounds.
 */
const CompoundComponent: React.FC<CompoundComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch, getValues, setValue } = formMethods;
  const currentDetails = watch();
  const searchParams = useSearchParams();
  const emptyState = searchParams?.get('emptyState') === 'true';

  const urlIndex = getIndexFromURL();
  const indexToUse = urlIndex !== undefined ? urlIndex : 0;
  const planDesignsDestails = `${PLAN_DESIGNS_BASE_PATH}.${indexToUse}.plan_design_details.0`;
  const basePath = `${planDesignsDestails}.cost_share_design`;

  const { refetch, validationData, isLoading } =
    useValidateByPageFromContext(COMPOUND_ITEM);

  const { helpCenterData, isFetching } = useHelpCenter(
    COMPOUND_ITEM,
    'plan_design',
    indexToUse
  );

  const uiContextIndex = getUIContextFromNavigationConstant(COMPOUND_ITEM);

  // Get initial compounds_ind value for disabled state
  const initialCompoundsInd =
    currentDetails.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0]
      ?.compounds_ind;

  const [isCompoundSelectDisabled, setIsCompoundSelectDisabled] = useState(
    initialCompoundsInd !== '1' && initialCompoundsInd !== 1
  );

  //   const baseSaveHandler = useSaveChangeRequestHandler(formMethods);
  //   const submitHandler = createPlanDesignSubmitHandler(baseSaveHandler);

  // Create options map from cost share designs
  const costShareDesignOptions = useMemo(() => {
    const designs = getValues(basePath) || [];

    if (!Array.isArray(designs)) {
      return {};
    }

    return designs.reduce((acc, design) => {
      if (design.tier_signature) {
        acc[design.tier_signature] = design.tier_signature;
      }
      return acc;
    }, {} as Record<string, string>);
  }, [getValues, basePath]);

  //   const handleSaveAndExit = useCallback(() => {
  //     const currentValues = getValues();
  //     submitHandler(currentValues);
  //   }, [getValues, submitHandler]);

  const handleCostShareDesignChange = useCallback(
    (value: string) => {
      setValue(`${planDesignsDestails}.selected_compound_design`, value);
      setValue(`${planDesignsDestails}.compounds_ind`, '1');
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [setValue, formMethods, planDesignsDestails]
  );

  const handleCompoundsCoveredChange = useCallback(
    (value: string) => {
      const isYes = value === '1';
      setIsCompoundSelectDisabled(!isYes);

      if (!isYes) {
        setValue(`${planDesignsDestails}.selected_compound_design`, '');
      }
    },
    [setValue, planDesignsDestails]
  );

  useEffect(() => {
    if (emptyState) {
      return;
    }

    const subscription = watch((value) => {
      const planDesignDetails =
        value.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0];

      if (!planDesignDetails) {
        return;
      }

      // Handle plan design details change
      setIsCompoundSelectDisabled(
        planDesignDetails?.compounds_ind !== '1' &&
          planDesignDetails?.compounds_ind !== 1
      );
    });
    return () => subscription.unsubscribe();
  }, [watch, indexToUse, emptyState]);

  const { createContinueHandler, isContinueLoading } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  const handleContinue = createContinueHandler(COMPOUND_ITEM, DISPENSE_ITEM);

  const handleBack = () => {
    onUpdateActiveItem?.(UNBREAKABLE_ITEM);
  };

  // Build form subcategories
  const { subCategories } = useCompoundForm(
    currentDetails as Partial<OrganizationDetails>,
    costShareDesignOptions,
    handleCostShareDesignChange,
    indexToUse,
    isCompoundSelectDisabled,
    handleCompoundsCoveredChange
  );

  useEffect(() => {
    const UI_CONTEXT_INDEX = getUIContextFromNavigationConstant(COMPOUND_ITEM);

    if (UI_CONTEXT_INDEX) {
      const currentPageValidation = validationData?.results?.[UI_CONTEXT_INDEX];

      if (!currentPageValidation) return;

      const { errors = [], warnings = [] } = currentPageValidation;

      // Handle errors
      errors.forEach(({ field, message }) => {
        const normalizedPath = `plan.${field}`;
        if (normalizedPath && message) {
          formMethods.setError(normalizedPath, {
            type: 'field-error',
            message,
          });
        }
      });

      // Handle warnings
      warnings.forEach(({ field, message }) => {
        const normalizedPath = `plan.${field}`;
        if (normalizedPath && message) {
          formMethods.setError(normalizedPath, {
            type: 'field-warning',
            message,
          });
        }
      });
    }
  }, [validationData, formMethods]);

  // Use continue loading state or validation loading state, whichever is true
  const isProcessing = isContinueLoading || isLoading;

  return (
    <Flex justify="space-between">
      <GenericForm
        formMethods={formMethods}
        formName="Compounds"
        formDescription=""
        subCategories={subCategories}
        onContinue={handleContinue}
        onBack={handleBack}
        // onSaveExit={handleSaveAndExit}
        isProcessing={isProcessing}
      />
      <HelpCenter
        validationResults={
          uiContextIndex
            ? validationData?.results?.[uiContextIndex]?.validation_results
            : undefined
        }
        helpContent={helpCenterData}
        isLoading={isFetching}
      />
    </Flex>
  );
};

export default CompoundComponent;
