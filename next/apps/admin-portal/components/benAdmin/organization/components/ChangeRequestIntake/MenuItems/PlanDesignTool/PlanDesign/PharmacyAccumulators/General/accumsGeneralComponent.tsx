import { Flex } from '@chakra-ui/react';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useValidateByPageFromContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import { normalizePlanDesignFieldPath } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/filterUtils';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import React, { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  ACCUMULATORS_GENERAL_ITEM,
  DEDUCTIBLE_ITEM,
  DISPENSE_ITEM,
} from '../../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../../Navigation/uiContextEnum';
import { useAccumsGeneralForm } from './accumsGeneralForm';

interface ClientInformationComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}

const AccumsGeneralComponent: React.FC<ClientInformationComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { watch } = formMethods;
  const currentDetails = watch();
  const planDesignIndex = getIndexFromURL();

  const { refetch, validationData, isLoading } = useValidateByPageFromContext(
    ACCUMULATORS_GENERAL_ITEM
  );

  const { helpCenterData, isFetching } = useHelpCenter(
    ACCUMULATORS_GENERAL_ITEM,
    'plan_design',
    planDesignIndex
  );

  const uiContextIndex = getUIContextFromNavigationConstant(
    ACCUMULATORS_GENERAL_ITEM
  );

  //   const submitHandler = useSaveChangeRequestHandler(formMethods);

  //   const handleSaveAndExit = () => {
  //     // Submit the current form data
  //     const currentValues = formMethods.getValues();
  //     submitHandler(currentValues);
  //   };

  // Build form subCategories and get the original continue/back handlers.
  const { subCategories } = useAccumsGeneralForm(
    currentDetails as Partial<OrganizationDetails>,
    0
  );

  const { createContinueHandler, isContinueLoading } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  // Create the continue handler for this specific page
  const handleContinue = createContinueHandler(
    ACCUMULATORS_GENERAL_ITEM,
    DEDUCTIBLE_ITEM
  );

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(DISPENSE_ITEM);
    }
  };

  useEffect(() => {
    const UI_CONTEXT_INDEX = getUIContextFromNavigationConstant(
      ACCUMULATORS_GENERAL_ITEM
    );

    if (UI_CONTEXT_INDEX) {
      const currentPageValidation = validationData?.results?.[UI_CONTEXT_INDEX];

      if (!currentPageValidation) return;

      const { errors = [], warnings = [] } = currentPageValidation;

      // Handle errors
      errors.forEach(({ field, message }) => {
        const normalizedPath = normalizePlanDesignFieldPath(field, true);
        if (normalizedPath && message) {
          formMethods.setError(normalizedPath, {
            type: 'field-error',
            message,
          });
        }
      });

      // Handle warnings
      warnings.forEach(({ field, message }) => {
        const normalizedPath = normalizePlanDesignFieldPath(field, true);
        if (normalizedPath && message) {
          formMethods.setError(normalizedPath, {
            type: 'field-warning',
            message,
          });
        }
      });
    }
  }, [validationData, formMethods]);

  // Use continue loading state or validation loading state, whichever is true
  const isProcessing = isContinueLoading || isLoading;
  return (
    <Flex justify="space-between">
      <GenericForm
        formMethods={formMethods}
        formName="Pharmacy Accumulators - General"
        formDescription=""
        subCategories={subCategories}
        // onSaveExit={handleSaveAndExit}
        onContinue={handleContinue}
        onBack={handleBack}
        isProcessing={isProcessing}
      />
      <HelpCenter
        validationResults={
          uiContextIndex
            ? validationData?.results?.[uiContextIndex]?.validation_results
            : undefined
        }
        helpContent={helpCenterData}
        isLoading={isFetching}
      />
    </Flex>
  );
};

export default AccumsGeneralComponent;
