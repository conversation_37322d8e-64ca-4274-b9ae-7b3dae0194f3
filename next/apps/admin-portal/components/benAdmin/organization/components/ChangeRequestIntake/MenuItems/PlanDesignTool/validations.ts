import { z } from 'zod';

/** Default validations */
export const defaultInputValidation = z.string().max(255);

/** Currency: up to 8 digits with optional 2 decimals, transformed to number */
export const currencyValidation = z
  .union([
    z
      .string()
      .regex(/^\d{1,8}(\.\d{1,2})?$/, {
        message: 'Value must be a valid number less than 1000000000',
      })
      .transform((val) => parseFloat(val)),
    z.number(),
    z.null(),
  ])
  .transform((val) => (val === null ? null : Number(val)));

/** Nullable small integer (-32768 to 32767) */
export const smallintValidation = z
  .number()
  .int('Must be an integer')
  .min(-32768, 'Value must be at least -32,768')
  .max(32767, 'Value must be at most 32,767')
  .nullable();

/** Strictly positive small integer (1 to 32767) */
export const strictlyPositiveSmallintValidation = z
  .number()
  .int('Must be an integer')
  .min(1, 'Value must be at least 1')
  .max(32767, 'Value must be at most 32,767');

/** Phone number: exactly 10 digits */
export const phoneValidation = z
  .string()
  .regex(/^\d{10}$/, 'Phone number must be 10 numeric digits only.');

/**
 * Schema for an object with effective and optional expiration dates.
 * Ensures expiration_date, if present, is not before effective_date.
 */
const dateComparisonSchema = z
  .object({
    effective_date: z.date({
      // Must be a valid date
      required_error: 'Effective Date is required',
      invalid_type_error: 'Effective Date must be a valid date',
    }),
    expiration_date: z
      .date({
        // Must be a valid date if provided
        invalid_type_error: 'End Date must be a valid date',
      })
      .optional(), // Field can be missing
  })
  .refine(
    // Cross-field validation logic
    (data) => {
      // Pass if expiration_date is missing or not before effective_date
      return !(
        data.expiration_date && data.effective_date > data.expiration_date
      );
    },
    {
      // Error message for the refinement
      message: 'End Date must be on or after the Effective Date',
      path: ['expiration_date'], // Attach error to this field
    }
  );

/**
 * Defines the nested structure to apply `dateComparisonSchema` within OrganizationDetails.
 * Uses optional, nullable, and passthrough to flexibly target validation only at
 * `plan.plan_designs[].plan_design_details[].accumulation_deductible[]` elements,
 * ignoring other fields and structure variations.
 */
export const organizationFormSchema = z
  .object({
    plan: z
      .object({
        plan_designs: z
          .array(
            z
              .object({
                plan_design_details: z
                  .array(
                    z // Objects within 'plan_design_details'
                      .object({
                        accumulation_deductible: z
                          .array(dateComparisonSchema)
                          .optional()
                          .nullable(),
                      })
                      .passthrough()
                  )
                  .optional()
                  .nullable(),
              })
              .passthrough()
          )
          .optional()
          .nullable(),
      })
      .passthrough()
      .optional()
      .nullable(),
  })
  .passthrough();
