import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { transitionConfig } from '../../../../../Tabs/PlanDesign/MemberExperience/Config/transitionConfig';
import { productNames } from '../../productNameConstants';

const percentValidation = z
  .string()
  .regex(/^(\d{1,3})(\.\d{1,2})?$/, {
    message: 'Must be a valid percentage with up to 2 decimal places',
  })
  .refine((value) => parseFloat(value) <= 100, {
    message: 'Percentage cannot exceed 100',
  })
  .transform((val) => parseFloat(val));

export const useTransitionFilesForm = (
  currentDetails: Partial<OrganizationDetails>,
  formMethods: UseFormReturn<OrganizationDetails>
) => {
  const {
    accumTransferMap,
    historicalClaimsMap,
    transitionFileMap,
    carrierToCarrierMap,
    yesNoMap,
  } = usePicklistMaps();

  const plan_transition = currentDetails?.plan?.plan_transition;
  const currentPBM = currentDetails?.plan?.product?.vendor?.legal_entity_id;
  const productName = currentDetails?.plan?.product?.name;

  const planId = currentDetails?.plan?.plan_id;
  if (!plan_transition?.plan_id && planId) {
    formMethods?.setValue('plan.plan_transition.plan_id', planId);
  }
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      ...(productName === productNames.CMK_360 ||
      productName === productNames.ESI_360 ||
      productName === productNames.OPT_360 ||
      productName === productNames.IRX_360 ||
      productName === productNames.CMK_DIRECT ||
      productName === productNames.ESI_DIRECT
        ? [
            defineInlineFieldGroup([
              defineFormField(
                'Historical Claims File',
                'dropdownSelect',
                transitionConfig.historical_claims_ind,
                currentDetails?.plan?.plan_transition?.historical_claims_ind,
                {
                  optionsMap: historicalClaimsMap,
                  placeholder: 'Select History Claim',
                  infoText:
                    'Specifies if a historical claims file will be obtained for this client.',
                }
              ),
              defineFormField(
                'ORT Transition File',
                'dropdownSelect',
                transitionConfig.ort_transition_file_ind,
                currentDetails?.plan?.plan_transition?.ort_transition_file_ind,
                {
                  optionsMap: transitionFileMap,
                  placeholder: 'Select ORT Transition File',
                  infoText:
                    'Specifies if an Open Refill Transfer file will be obtained (transfers mail order refills to the new PBM mail order pharmacy).',
                }
              ),
            ]),
            defineInlineFieldGroup([
              defineFormField(
                'Mail Order Percentage',
                'input',
                transitionConfig.mail_order_percentage,
                currentDetails?.plan?.plan_transition?.mail_order_percentage,
                {
                  placeholder: 'Enter Mail Order Percentage',
                  infoText:
                    'Estimated percentage of members currently using mail order/home delivery.',
                  validations: percentValidation.optional(),
                }
              ),
              defineFormField(
                'PA File',
                'dropdownSelect',
                transitionConfig.pa_file_ind,
                currentDetails?.plan?.plan_transition?.pa_file_ind,
                {
                  optionsMap: transitionFileMap,
                  placeholder: 'Select PA File',
                  infoText:
                    'Whether or not a Prior Authorization file will be obtained for this client.',
                }
              ),
            ]),
          ]
        : []),

      defineInlineFieldGroup([
        ...(productName === productNames.CMK_360 ||
        productName === productNames.ESI_360 ||
        productName === productNames.OPT_360 ||
        productName === productNames.IRX_360 ||
        productName === productNames.CMK_DIRECT ||
        productName === productNames.ESI_DIRECT
          ? [
              defineFormField(
                'Outbound Claims Files',
                'input',
                transitionConfig.outbound_claims_file,
                currentDetails?.plan?.plan_transition?.outbound_claims_file,
                {
                  placeholder: 'Enter Outbound Claims Files',
                  infoText:
                    'Do claims files need to be sent to another vendor, and if so, what vendor?',
                  validations: z
                    .string()
                    .max(255, 'Value cannot exceed 255 characters'),
                }
              ),
            ]
          : []),
        ...(productName === productNames.CMK_360 ||
        productName === productNames.ESI_360 ||
        productName === productNames.OPT_360 ||
        productName === productNames.CMK_DIRECT ||
        productName === productNames.ESI_DIRECT
          ? [
              defineFormField(
                'Carrier to Carrier',
                'dropdownSelect',
                transitionConfig.carrier_to_carrier_ind,
                currentDetails?.plan?.plan_transition?.carrier_to_carrier_ind,
                {
                  optionsMap: carrierToCarrierMap,
                  placeholder: 'Select Carrier Mapping',
                  infoText:
                    'Moves existing Caremark clients to the RxB/CMK arrangement .',
                }
              ),
            ]
          : []),
      ]),
      defineInlineFieldGroup([
        ...(productName === productNames.CMK_360 ||
        productName === productNames.ESI_360 ||
        productName === productNames.OPT_360 ||
        productName === productNames.IRX_360 ||
        productName === productNames.CMK_DIRECT ||
        productName === productNames.ESI_DIRECT
          ? [
              defineFormField(
                'Accum Priming Balance File',
                'dropdownSelect',
                transitionConfig.accum_priming_balance_file_ind,
                currentDetails?.plan?.plan_transition
                  ?.accum_priming_balance_file_ind,
                {
                  infoText: 'Accum Priming Balance File',
                  optionsMap: yesNoMap,
                  placeholder: 'Select Yes/No',
                }
              ),
            ]
          : []),
        ...(productName === productNames.CMK_360 ||
        productName === productNames.ESI_360 ||
        productName === productNames.OPT_360 ||
        productName === productNames.IRX_360 ||
        productName === productNames.CMK_DIRECT ||
        productName === productNames.ESI_DIRECT
          ? [
              defineFormField(
                'Include Weight Loss Drug in PA file',
                'dropdownSelect',
                transitionConfig.include_weight_loss_drug_ind,
                currentDetails?.plan?.plan_transition
                  ?.include_weight_loss_drug_ind,
                {
                  optionsMap: yesNoMap,
                  placeholder: 'Select Yes/No',
                  infoText:
                    'Do you want to include Weight Loss Drug in PA file ?',
                }
              ),
            ]
          : []),
      ]),
      defineInlineFieldGroup([
        ...(productName === productNames.CMK_360 ||
        productName === productNames.ESI_360 ||
        productName === productNames.OPT_360 ||
        productName === productNames.IRX_360 ||
        productName === productNames.CMK_DIRECT ||
        productName === productNames.ESI_DIRECT
          ? [
              defineFormField(
                'Transition Notes',
                'textarea',
                transitionConfig.notes,
                currentDetails?.plan?.plan_transition?.notes,
                {
                  placeholder: 'Enter your notes',
                  validations: z
                    .string()
                    .max(2000, 'Value cannot exceed 2000 characters'),
                }
              ),
            ]
          : []),
      ]),
    ]),
  ];
  if (
    currentPBM === 2 ||
    productName === productNames.ESI_360 ||
    productName === productNames.CMK_DIRECT ||
    productName === productNames.ESI_DIRECT
  )
    // Express Scripts
    subCategories.push(
      defineSubCategory('ESI Only', '', [
        defineInlineFieldGroup([
          defineFormField(
            'Accum Transfer',
            'dropdownSelect',
            transitionConfig.accum_transfer_ind,
            currentDetails?.plan?.plan_transition?.accum_transfer_ind,
            {
              optionsMap: accumTransferMap,
              placeholder: 'Select Accum Transfer',
              infoText:
                'Specifies whether accums transfer happens at ESI at the individual contract level, or the whole carrier level (rarely used).',
            }
          ),
        ]),
      ])
    );
  return { subCategories };
};
