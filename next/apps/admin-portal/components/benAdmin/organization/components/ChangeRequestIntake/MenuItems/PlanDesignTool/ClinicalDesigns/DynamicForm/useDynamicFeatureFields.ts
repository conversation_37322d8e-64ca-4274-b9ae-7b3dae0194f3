import { PLAN_DESIGNS_BASE_PATH } from 'apps/admin-portal/components/benAdmin';
import {
  Feature,
  OrganizationDetails,
  Plan,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import {
  buildPicklistsMap,
  getOptionsMap,
  mapFieldType,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/Configurable/fieldUtils';
import {
  buildFieldPath,
  findMatchingPlanFeatureItem,
  findMatchingPlanFeatures,
} from 'apps/admin-portal/components/benAdmin/organization/hooks/PlanDesign/planNavigation';
import {
  defineDropdownField,
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { useURLIndex } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/useUrlParameters';
import { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { getIdMap, syncAcrossPlanDesigns } from './paramsUtils';

// Zod validation schema for textarea fields with 2000 character limit
const textareaValidationSchema = z.string().max(2000, {
  message: 'Text cannot exceed 2000 characters',
});

const defaultYesESIConditionSpecificManagementFields = [
  'Cardiovascular Care',
  'Oncology Care',
  'HIV Care Value Program',
  'Multiple Sclerosis Care Value',
  'Rare Conditions Care Value',
  'React with Market Event',
];

/**
 * Hook to dynamically generate form fields for a feature
 * Uses URL parameter detection and optimized field generation
 *
 * @param feature The feature object to generate fields for
 * @param currentDetails The current organization details
 * @returns An object containing the generated subCategories
 */
export function useDynamicFeatureFields(
  feature: Feature,
  currentDetails: Partial<OrganizationDetails>,
  formMethods: UseFormReturn<any>
) {
  // Get the index from URL with polling - this is now provided by our reusable hook
  const urlIndex = useURLIndex(undefined, { debug: false });
  const esiConditionSpecificManagementFeature =
    'ESI-ConditionSpecificManagement';

  // Use memoization to prevent regenerating fields unnecessarily
  const subCategories = useMemo(() => {
    // Early return if feature is invalid
    if (!feature || !feature.feature_items) {
      return [];
    }

    // Get picklists map for quick lookup
    const picklistsMap = buildPicklistsMap(currentDetails.picklists || []);
    const generateSubCategory = (clinicalFeature: Feature) => {
      // Find the matching plan features
      const matchingPlanFeatures = findMatchingPlanFeatures(
        currentDetails.plan as Plan,
        clinicalFeature,
        urlIndex || 0
      );

      // Create fields for all feature items
      const nonTextareaFields: ReturnType<typeof defineFormField>[] = [];
      const textareaFields: ReturnType<typeof defineFormField>[] = [];
      clinicalFeature.feature_items.forEach((item) => {
        // Find the matching plan feature item
        const matchingItem = findMatchingPlanFeatureItem(
          matchingPlanFeatures,
          item
        );

        // Determine field type based on field_type_label
        const fieldType = mapFieldType(item.field_type_label) as any;

        // Get options map for selection fields
        const optionsMap = getOptionsMap(picklistsMap, item);

        // Build the field path with explicit URL index
        const fieldPath = buildFieldPath(matchingItem, urlIndex);

        const idMap = getIdMap(fieldPath, formMethods);

        // Get the current value from the plan structure
        const currentValue = matchingItem ? matchingItem.value : '';

        if (
          clinicalFeature.name === esiConditionSpecificManagementFeature &&
          defaultYesESIConditionSpecificManagementFields.includes(
            item.name || item.label
          )
        ) {
          const currentDefaultValue = formMethods?.getValues(fieldPath);
          if (!currentDefaultValue) {
            formMethods?.setValue(fieldPath, '1');
          }
        }

        let fieldConfig;

        // For dropdown fields, handle toggle features
        if (fieldType === 'dropdownSelect' && optionsMap) {
          // Use defineDropdownField for enhanced dropdown functionality
          fieldConfig = defineDropdownField(
            item.label || '',
            fieldPath,
            currentValue,
            optionsMap,
            {
              infoText: '',
              placeholder: `Select ${item.label}`,
              isRequired: false,
              showToggle: true,
              toggleLabel: 'Differs from Plan Design',
              formPath: PLAN_DESIGNS_BASE_PATH,
              sync: true,
              idMap,
              syncFunction: syncAcrossPlanDesigns,
            }
          );
        } else {
          // For other field types, use the standard defineFormField
          fieldConfig = defineFormField(
            item.label || '',
            fieldType,
            fieldPath,
            currentValue,
            {
              infoText: '',
              placeholder: `Enter ${item.label}`,
              isRequired: false,
              ...(optionsMap &&
              ['checkboxGroup', 'radioGroup', 'dropdownSelect'].includes(
                fieldType
              )
                ? { optionsMap }
                : {}),
              // Add toggle and sync properties for input fields
              ...(fieldType === 'input'
                ? {
                    showToggle: true,
                    toggleLabel: 'Differs from Plan Design',
                    formPath: PLAN_DESIGNS_BASE_PATH,
                    sync: true,
                    idMap,
                    syncFunction: syncAcrossPlanDesigns,
                  }
                : {}),
              ...(fieldType === 'textarea'
                ? {
                    showToggle: true,
                    toggleLabel: 'Differs from Plan Design',
                    formPath: PLAN_DESIGNS_BASE_PATH,
                    rows: 5,
                    sync: true,
                    idMap,
                    syncFunction: syncAcrossPlanDesigns,
                    validations: textareaValidationSchema,
                    customProps: {
                      maxLength: 2000,
                      minHeight: '120px',
                      resize: 'none',
                      overflow: 'hidden',
                      style: {
                        minHeight: '120px',
                      },
                      validations: textareaValidationSchema,
                    },
                  }
                : {}),
            }
          );
        }

        if (fieldType === 'textarea') {
          textareaFields.push(fieldConfig);
        } else {
          nonTextareaFields.push(fieldConfig);
        }
      });

      // Group regular fields into inline groups of 2
      const inlineGroups = [];
      for (let i = 0; i < nonTextareaFields.length; i += 2) {
        inlineGroups.push(
          defineInlineFieldGroup(nonTextareaFields.slice(i, i + 2))
        );
      }

      // Add each textarea field in its own inline group
      textareaFields.forEach((textareaField) => {
        inlineGroups.push(defineInlineFieldGroup([textareaField]));
      });

      // Create a single subcategory with all fields
      return [
        defineSubCategory(
          clinicalFeature.label || clinicalFeature.name || '',
          '',
          inlineGroups
        ),
      ];
    };

    const clinicalFeatureSubCategory = generateSubCategory(feature);
    return clinicalFeatureSubCategory;
  }, [
    feature,
    currentDetails.picklists,
    currentDetails.plan,
    urlIndex,
    formMethods,
  ]);

  return { subCategories };
}
