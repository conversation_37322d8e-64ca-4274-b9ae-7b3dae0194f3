import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import {
  getDispenseAsWrittenPaths,
  getXmlPaths,
} from 'apps/admin-portal/components/benAdmin';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { getIndexFromURL } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/SideNavBar/parameterUtils';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

export interface DispenseAsWrittenOption {
  value: string;
  label: string;
}

/**
 * useFormDemo Hook
 *
 * Generates form subcategories (sections) based on the provided organization details.
 * Uses dot notation for field names so that the final form JSON is nested.
 *
 * @param org - Partial organization data used to prefill form fields.
 * @returns An object containing the generated subCategories and navigation handlers.
 */
export function useDispenseAsWrittenForm(
  currentDetails: Partial<OrganizationDetails>,
  index = 0
) {
  const urlIndex = getIndexFromURL();
  const indexToUse = urlIndex !== undefined ? urlIndex : index;
  const designDetails =
    currentDetails.plan?.plan_designs?.[indexToUse]?.plan_design_details?.[0];

  // Use existing config with the determined index
  const dispenseConfig = getDispenseAsWrittenPaths(indexToUse);
  const xmlConfig = getXmlPaths(indexToUse);
  const esi = designDetails?.plan_design_detail_esi?.[0];

  const { yesNoViewBenefitMap } = usePicklistMaps();

  const { useApiQuery } = useBenAdmin();
  const { picklist: dispenseAsWritten } = useApiQuery([
    {
      key: 'picklist',
      queryParams: { names: 'DispenseAsWritten' },
      options: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        cacheTime: 10 * 60 * 1000, // 10 minutes
      },
    },
  ]);

  // Transform the dispenseAsWritten array into an options map
  const dispenseOptions = (dispenseAsWritten?.DispenseAsWritten || []).reduce(
    (acc: Record<string, string>, item: DispenseAsWrittenOption) => {
      acc[item.value] = item.label;
      return acc;
    },
    {}
  );

  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Dispense as Written',
          'dropdownSelect',
          dispenseConfig.dispense_as_written_ind,
          designDetails?.dispense_as_written_ind,
          {
            isRequired: true,
            optionsMap: dispenseOptions,
          }
        ),
        defineFormField(
          'Dispense as Written Description',
          'input',
          dispenseConfig.dispense_as_written_description,
          designDetails?.dispense_as_written_description,
          {
            infoText:
              'Which copay structure is used and whether a penalty will apply, based on the Rx DAW code as specified by the prescriber.',
            placeholder: 'Enter Description',
            validations: z.string().max(255),
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Dispense as Written Penalties Apply to Deductible',
          'dropdownSelect',
          xmlConfig.penalties_apply_to_deductible_ind,
          esi?.penalties_apply_to_deductible_ind,
          {
            infoText: 'Dispense as Written Penalties Apply to Deductible',
            optionsMap: yesNoViewBenefitMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Dispense as Written Penalties Apply to Maximum Out Of Pocket',
          'dropdownSelect',
          xmlConfig.penalties_apply_to_moop_ind,
          esi?.penalties_apply_to_moop_ind,
          {
            infoText: 'Indicates whether DAW penalties count towards the MOOP.',
            optionsMap: yesNoViewBenefitMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Dispense as Written Penalties Apply After Maximum Out of Pocket',
          'dropdownSelect',
          xmlConfig.penalties_apply_after_moop_ind,
          esi?.penalties_apply_after_moop_ind,
          {
            infoText:
              'Indicates whether DAW penalties are incurred after the MOOP is met.',
            optionsMap: yesNoViewBenefitMap,
          }
        ),
      ]),
    ]),
  ];

  return {
    subCategories,
  };
}
