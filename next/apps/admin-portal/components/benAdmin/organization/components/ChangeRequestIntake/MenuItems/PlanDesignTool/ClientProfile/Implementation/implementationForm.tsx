import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { clientConfig } from '../../../../../Tabs/PlanDesign/ClientProfile/Config/clientConfig';

export const useImplementationForm = (
  currentDetails: Partial<OrganizationDetails>
) => {
  const { implementationTimelineMap } = usePicklistMaps();

  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Implementation Start Date',
          'datepicker',
          clientConfig.implementation_start_date,
          currentDetails?.plan?.implementation_start_date,
          {
            isRequired: true,
            placeholder: 'Select Implementation Start Date',
            validations: z.date(),
          }
        ),
        defineFormField(
          'Implementation Timeline',
          'dropdownSelect',
          clientConfig.implementation_timeline,
          currentDetails?.plan?.implementation_timeline,
          {
            isRequired: true,
            placeholder: 'Select an Option',
            optionsMap: implementationTimelineMap,
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Open Enrollment Start',
          'datepicker',
          clientConfig.open_enrollment_start_date,
          currentDetails?.plan?.open_enrollment_start_date,
          {
            infoText:
              'The earliest date members can enroll in / change benefit elections',
            placeholder: 'Select Open Enrollment Start Date',
            validations: z.date(),
          }
        ),
        defineFormField(
          'Open Enrollment End',
          'datepicker',
          clientConfig.open_enrollment_end_date,
          currentDetails?.plan?.open_enrollment_end_date,
          {
            infoText:
              'The latest date members can enroll in / change benefit elections',
            placeholder: 'Select Open Enrollment End Date',
            validations: z.date(),
          }
        ),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Open Enrollment Support',
          'input',
          clientConfig.open_enrollment_support,
          currentDetails?.plan?.open_enrollment_support,
          {
            infoText:
              'How does client want RxB to support OE? Flyers, meetings, etc.',
            placeholder: 'Enter Open Enrollment Support',
            validations: z
              .string()
              .max(255, 'Open Enrollment Support cannot exceed 255 characters'),
          }
        ),
        defineFormField('', 'text', '', undefined, {}),
      ]),
    ]),
  ];

  return {
    subCategories,
  };
};
