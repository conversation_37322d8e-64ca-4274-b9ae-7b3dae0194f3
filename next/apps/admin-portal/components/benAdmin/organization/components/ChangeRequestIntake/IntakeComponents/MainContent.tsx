import { Box, Text } from '@chakra-ui/react';
import { PlanDesign } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { SidebarConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { useSearchParams } from 'next/navigation';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import {
  CLIENT_PROFILE_MODE,
  CLINICAL_DESIGN_MODE,
  MAIN_MODE,
  MEMBER_EXPERIENCE_MODE,
  PLAN_DESIGN_LIST_ITEM,
  PRODUCTS_AND_SERVICES_MODE,
} from '../Navigation/navigationConstants';
import { IntakeHeader } from './IntakeHeader';

interface MainContentProps {
  formMethods: UseFormReturn<any>;
  currentComponent: React.ReactNode;
  currentMode: string;
  sidebarConfig: SidebarConfig;
  activeItem: string;
  onBreadcrumbNavigate: (id: string) => void;
}

const MainContent: React.FC<MainContentProps> = ({
  formMethods,
  currentComponent,
  currentMode,
  sidebarConfig,
  activeItem,
  onBreadcrumbNavigate,
}) => {
  const searchParams = useSearchParams();

  const excludedModes = [
    MAIN_MODE,
    CLIENT_PROFILE_MODE,
    MEMBER_EXPERIENCE_MODE,
    PRODUCTS_AND_SERVICES_MODE,
    CLINICAL_DESIGN_MODE,
  ];

  const indexParam = searchParams?.get('index');
  // Get plan design info

  const indexToUse = indexParam ? parseInt(indexParam, 10) : undefined;
  const planDesignIndexNum = indexToUse || 0;
  const planDesign = formMethods.getValues(
    `plan.plan_designs.${planDesignIndexNum}`
  ) as PlanDesign;
  const planName = planDesign?.name || 'Selected Plan Design';
  const planEffectiveDate =
    planDesign?.plan_design_details[0]?.effective_date || '';

  // Generate breadcrumb paths based on current state
  const getBreadcrumbPaths = () => {
    const paths = [{ label: 'Project Overview', id: 'preview' }];

    // For main mode, just add Dashboard and return
    if (currentMode === 'main') {
      paths.unshift({ label: 'Dashboard', id: 'dashboard' });
      return paths;
    }

    const items = sidebarConfig?.sections?.[0]?.items || [];

    // Search for active item in both top-level and nested items
    for (const item of items) {
      // Case 1: Match at top level
      if (item.id === activeItem && item.component) {
        paths.push({ label: item.label, id: item.id });
        return paths;
      }

      // Case 2: Match in dropdown
      if (item.hasDropdown && item.dropdownItems) {
        const nestedItem = item.dropdownItems.find(
          (nested) => nested.id === activeItem && nested.component
        );

        if (nestedItem) {
          paths.push({ label: item.label, id: '' }); // Parent with empty id for non-clickable
          paths.push({ label: nestedItem.label, id: nestedItem.id }); // Child (clickable)
          return paths;
        }
      }
    }

    return paths;
  };

  // Format the plan effective date for display
  const formatPlanDate = (dateStr: string) => {
    if (!dateStr) return 'Selected Plan Design Effective Date';
    const date = new Date(`${dateStr}T00:00:00`);
    return date.toLocaleDateString('en-US', {
      month: 'numeric',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <Box flex="1" display="flex" flexDirection="column" overflow="hidden">
      <Box w="97%" mx="auto" borderRadius="lg">
        <IntakeHeader
          formMethods={formMethods}
          getBreadcrumbPaths={getBreadcrumbPaths}
          onBreadcrumbNavigate={onBreadcrumbNavigate}
          planName={planName}
          planEffectiveDate={formatPlanDate(planEffectiveDate)}
        />
      </Box>
      <Box w="98%" mx="auto" px={4}>
        {!excludedModes.includes(currentMode) &&
          activeItem !== PLAN_DESIGN_LIST_ITEM && (
            <Text fontSize="xl" pt={1} pb={4}>
              {planName} - {formatPlanDate(planEffectiveDate)}
            </Text>
          )}
      </Box>
      <Box flex="1" w="98%" mx="auto" overflowY="auto" py={4} pr={4}>
        {currentComponent}
      </Box>
    </Box>
  );
};

export default MainContent;
