// src/components/ChangeRequestIntake/PreviewComponent.tsx
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { OrganizationView } from 'apps/admin-portal/components/benAdmin/organization/OrganizationView';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

interface PreviewComponentProps {
  changeRequest: OrganizationDetails;
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (section: string, tab?: string) => void;
}

const PreviewComponent: React.FC<PreviewComponentProps> = React.memo(
  ({ changeRequest, formMethods, onUpdateActiveItem }) => (
    <OrganizationView
      organizationDetails={changeRequest}
      formMethods={formMethods}
      onUpdateActiveItem={onUpdateActiveItem}
    />
  )
);

PreviewComponent.displayName = 'PreviewComponent';

export default PreviewComponent;
