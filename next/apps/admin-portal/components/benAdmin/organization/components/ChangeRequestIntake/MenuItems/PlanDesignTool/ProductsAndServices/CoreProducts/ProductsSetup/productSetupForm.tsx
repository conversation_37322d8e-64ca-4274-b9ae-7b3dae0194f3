import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';

/**
 * useProductSetupForm Hook
 *
 * Generates form subcategories for core product setup configuration.
 * Uses standardized path structure for products and services.
 *
 * @param currentDetails - Partial organization data used to prefill form fields
 * @returns An object containing the generated subCategories
 */
export function useProductSetupForm(
  currentDetails: Partial<OrganizationDetails>
) {
  // Safely access nested properties with proper type handling
  const coreProducts = currentDetails?.plan?.product;

  // Build the subcategories using the helper functions
  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Core RxB Product Name',
          'input',
          'plan.product.name',
          coreProducts?.name,
          {
            infoText: 'Please enter Core RxB Product Name',
            placeholder: 'Select Core RxB Product Name',
            isRequired: true,
            isDisabled: true,
          }
        ),
        defineFormField('', 'text', '', undefined, {}),
      ]),
    ]),
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        defineFormField(
          'Core RxB Product Effective Date',
          'datepicker',
          'plan.effective_date',
          currentDetails?.plan?.effective_date,
          {
            isRequired: true,
            infoText: 'Choose Core RxB Product Effective Date',
            placeholder: 'Select Core RxB Product Effective Date',
            isDisabled: true,
          }
        ),
        defineFormField(
          'Core RxB Product Termination Date',
          'datepicker',
          'plan.expiration_date',
          currentDetails?.plan?.expiration_date,
          {
            isRequired: true,
            infoText: 'Choose Core RxB Product Termination Date',
            placeholder: 'Select Core RxB Product Termination Date',
            isDisabled: true,
          }
        ),
      ]),
    ]),
  ];

  return {
    subCategories,
  };
}
