import {
  COMPOUND_ITEM,
  XML_ITEM,
  GENERAL_ITEM,
} from 'apps/admin-portal/components/benAdmin';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import CostShareComponent from '../costShareComponent';
import { useIsESIProduct } from 'apps/admin-portal/components/benAdmin/organization/hooks/useIsESIHook';

interface CombinedCostShareComponentProps {
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
  backIndicator?: string;
  continueIndicator?: string;
  title?: string;
  description?: string;
  modalTitle?: string;
  basePath?: string;
  sourceLabel?: string;
}

/**
 * CombinedCostShareComponent - Uses CostShareComponent to show both Standard and Unbreakable cost share tiers in one table
 */
const CombinedCostShareComponent: React.FC<CombinedCostShareComponentProps> = (props) => {
  const isESIProduct = useIsESIProduct(props.formMethods);

  // Use XML_ITEM for ESI products, GENERAL_ITEM for non-ESI products
  const backIndicator = isESIProduct ? XML_ITEM : GENERAL_ITEM;

  return (
    <CostShareComponent
      formMethods={props.formMethods}
      onUpdateActiveItem={props.onUpdateActiveItem}
      title={props.title || 'Cost Share Tiers'}
      description={
        props.description ||
        'Select the cost share tiers that apply to this plan.'
      }
      modalTitle={props.modalTitle || 'Select Cost Share Tiers'}
      prePackagedIndFilter={undefined} // FORCE undefined to show all tiers
      continueIndicator={COMPOUND_ITEM}
      backIndicator={props.backIndicator || backIndicator}
      basePath={props.basePath}
      sourceLabel={props.sourceLabel}
    />
  );
};

export default CombinedCostShareComponent;
