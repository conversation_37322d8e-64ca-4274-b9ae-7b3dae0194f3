import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { SubCategoryType } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { idCardConfig } from '../../../../../Tabs/PlanDesign/MemberExperience/Config/idCardConfig';
import { productNames } from '../../productNameConstants';

/**
 * Remove after demo BADMIN-1494: 2025-07-03
 * Get default RX BIN value based on PBM product name
 */
const getDefaultRxBin = (productName?: string): string => {
  if (!productName) return '';

  const product = productName.toUpperCase();

  if (product.includes('CAREMARK') || product.includes('CMK')) {
    return '004336'; // CMK-360
  } else if (product.includes('EXPRESS SCRIPTS') || product.includes('ESI')) {
    return '610014'; // ESI-360
  } else if (product.includes('OPTUM') || product.includes('OPT')) {
    return '610011'; // OPT-360
  } else if (product.includes('IRX')) {
    return '028314'; // IRX-360
  }

  return '';
};

/**
 * Remove after demo BADMIN-1494: 2025-07-03
 * Get default PCN value based on PBM product name
 */
const getDefaultPcn = (productName?: string): string => {
  if (!productName) return '';

  const product = productName.toUpperCase();

  if (product.includes('CAREMARK') || product.includes('CMK')) {
    return 'ADV'; // CMK-360
  } else if (product.includes('OPTUM') || product.includes('OPT')) {
    return 'IRX'; // OPT-360
  } else if (product.includes('IRX')) {
    return 'LUMN8'; // IRX-360
  }

  return '';
};

/**
 * Get default Rx GRP value based on PBM product name
 */
const getDefaultRxGrp = (
  productName?: string,
  groupUmbrellaValue?: string,
  inHousePharmacyValue?: string
): string => {
  if (!productName) return '';

  const product = productName.toUpperCase();

  if (product === productNames.CMK_360) {
    return 'RX2169'; // CMK-360
  } else if (product === productNames.ESI_360 && groupUmbrellaValue) {
    return `RXB${groupUmbrellaValue}`; // ESI-360
  } else if (product === productNames.OPT_360 && inHousePharmacyValue) {
    // OPT-360
    if (inHousePharmacyValue === '1') {
      return 'RXBENHOSP';
    } else if (inHousePharmacyValue === '0') {
      return 'RXBENEFIT';
    }
  }

  return '';
};

export const useIdCardsForm = (
  currentDetails: Partial<OrganizationDetails>,
  formMethods: UseFormReturn<OrganizationDetails>
) => {
  const {
    idCardsResponsibleMap,
    idCardTypeMap,
    yesNoNaMap,
    employeeIdSourceMap,
    idCardMailingMap,
    yesNoMap,
  } = usePicklistMaps();
  const planMaterial = currentDetails?.plan?.plan_material;
  const planId = currentDetails?.plan?.plan_id;
  const productName = currentDetails?.plan?.product?.name;
  const groupUmbrellaValue = currentDetails?.plan?.group_umbrella_number;
  const lastFourDigitsOfUmbrellaValue = groupUmbrellaValue?.slice(-4);
  const inHousePharmacyValue = currentDetails?.plan?.inhouse_pharmacy_ind;

  // Get default values based on PBM product name
  const defaultRxBin = getDefaultRxBin(productName);
  const defaultPcn = getDefaultPcn(productName);
  const defaultRxGrp = getDefaultRxGrp(
    productName,
    lastFourDigitsOfUmbrellaValue,
    inHousePharmacyValue
  );

  if (!planMaterial?.plan_id && planId) {
    formMethods?.setValue('plan.plan_material.plan_id', planId);
  }

  const subCategories: SubCategoryType[] = [
    defineSubCategory('', '', [
      defineInlineFieldGroup([
        ...(productName === productNames.CMK_360 ||
        productName === productNames.ESI_360 ||
        productName === productNames.OPT_360 ||
        productName === productNames.CMK_DIRECT ||
        productName === productNames.ESI_DIRECT
          ? [
              defineFormField(
                'Responsible for ID Cards',
                'dropdownSelect',
                idCardConfig.id_card_responsible_ind,
                planMaterial?.id_card_responsible_ind,
                {
                  optionsMap: idCardsResponsibleMap,
                  placeholder: 'Select Responsible for ID Cards',
                  infoText:
                    'Designates which entity will print member ID cards.',
                }
              ),
            ]
          : []),

        ...(productName === productNames.CMK_360 ||
        productName === productNames.ESI_360 ||
        productName === productNames.OPT_360 ||
        productName === productNames.IRX_360 ||
        productName === productNames.CMK_DIRECT ||
        productName === productNames.ESI_DIRECT
          ? [
              defineFormField(
                'ID Card Type',
                'dropdownSelect',
                idCardConfig.id_card_type_ind,
                planMaterial?.id_card_type_ind,
                {
                  optionsMap: idCardTypeMap,
                  placeholder: 'Select ID Card Type',
                  infoText:
                    'Specifies whether ID card has Rx information only (separate), or both Rx and Medical (combined).',
                }
              ),
            ]
          : []),
      ]),

      ...(productName === productNames.CMK_360 ||
      productName === productNames.ESI_360 ||
      productName === productNames.OPT_360 ||
      productName === productNames.IRX_360 ||
      productName === productNames.CMK_DIRECT ||
      productName === productNames.ESI_DIRECT
        ? [
            defineInlineFieldGroup([
              defineFormField(
                'Logo on ID Cards',
                'dropdownSelect',
                idCardConfig.id_card_logo_ind,
                planMaterial?.id_card_logo_ind,
                {
                  optionsMap: yesNoNaMap,
                  placeholder: 'Select Logo Applicability',
                  infoText:
                    "Whether the client's logo will be printed on the ID card. Used only when ID Card Type is 'Separate'.",
                }
              ),
              defineFormField('', 'text', '', undefined, {}),
            ]),
            defineInlineFieldGroup([
              defineFormField(
                'Extract Alternate ID',
                'dropdownSelect',
                idCardConfig.employee_id_source_ind,
                planMaterial?.employee_id_source_ind,
                {
                  optionsMap: employeeIdSourceMap,
                  placeholder: 'Select Alternate Id',
                  infoText:
                    'Generated ID cannot be used for combined card type.',
                }
              ),
              defineFormField('', 'text', '', undefined, {}),
            ]),
          ]
        : []),

      defineInlineFieldGroup([
        ...(productName === productNames.CMK_360 ||
        productName === productNames.ESI_360 ||
        productName === productNames.OPT_360 ||
        productName === productNames.IRX_360 ||
        productName === productNames.CMK_DIRECT ||
        productName === productNames.ESI_DIRECT
          ? [
              defineFormField(
                'ID Card Mailing',
                'dropdownSelect',
                idCardConfig.id_card_mailing_ind,
                planMaterial?.id_card_mailing_ind,
                {
                  optionsMap: idCardMailingMap,
                  placeholder: 'Select ID Card Mailing',
                  infoText:
                    "Whether ID cards will be mailed to members' homes directly, or sent to the client's HR office.",
                }
              ),
            ]
          : []),

        ...(productName === productNames.CMK_360 ||
        productName === productNames.OPT_360 ||
        productName === productNames.IRX_360 ||
        productName === productNames.CMK_DIRECT ||
        productName === productNames.ESI_DIRECT
          ? [
              defineFormField(
                'PCN',
                'input',
                idCardConfig.pcn,
                planMaterial?.pcn || defaultPcn,
                {
                  placeholder: 'Enter PCN',
                  validations: z
                    .string()
                    .max(10, 'PCN must not exceed 10 characters'),
                }
              ),
            ]
          : []),
      ]),
      defineInlineFieldGroup([
        defineFormField(
          'Rx BIN',
          'input',
          idCardConfig.rx_bin,
          planMaterial?.rx_bin || defaultRxBin,
          {
            placeholder: 'Enter Rx BIN',
            validations: z
              .string()
              .regex(/^\d+$/, 'Value must be a number')
              .max(10, 'Rx BIN must not exceed 10 digits'),
          }
        ),
        defineFormField('', 'text', '', undefined, {}),
      ]),

      ...(productName === productNames.CMK_360 ||
      productName === productNames.ESI_360 ||
      productName === productNames.OPT_360 ||
      productName === productNames.IRX_360 ||
      productName === productNames.CMK_DIRECT ||
      productName === productNames.ESI_DIRECT
        ? [
            defineInlineFieldGroup([
              defineFormField(
                'Rx GRP',
                'input',
                idCardConfig.rx_grp,
                planMaterial?.rx_grp || defaultRxGrp,
                {
                  placeholder: 'Enter Rx GRP',
                  validations: z
                    .string()
                    .max(10, 'Rx GRP must not exceed 10 digits'), // validations ??
                }
              ),
              defineFormField('', 'text', '', undefined, {}),
            ]),
          ]
        : []),

      ...(productName === productNames.CMK_360 ||
      productName === productNames.ESI_360 ||
      productName === productNames.OPT_360 ||
      productName === productNames.IRX_360 ||
      productName === productNames.CMK_DIRECT ||
      productName === productNames.ESI_DIRECT
        ? [
            defineInlineFieldGroup([
              defineFormField(
                'DataNet Access',
                'dropdownSelect',
                idCardConfig.datanet_access_ind,
                planMaterial?.datanet_access_ind || '1',
                {
                  placeholder: 'Select DataNet Access',
                  optionsMap: yesNoMap,
                  infoText: 'DataNet Access',
                }
              ),
              defineFormField('', 'text', '', undefined, {}),
            ]),
          ]
        : []),

      defineInlineFieldGroup([
        defineFormField(
          'ID Cards & Member Materials Note',
          'textarea',
          idCardConfig.notes,
          planMaterial?.notes,
          {
            placeholder: 'Enter your notes',
            infoText: 'Please enter your Member Materials Notes',
            validations: z
              .string()
              .max(2000, 'Value cannot exceed 2000 characters'),
            rows: 5,
            customProps: {
              minHeight: '120px',
              overflow: 'hidden',
            },
          }
        ),
      ]),
    ]),
  ];
  return { subCategories };
};
