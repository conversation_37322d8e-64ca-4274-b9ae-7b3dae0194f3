import { ChevronDownIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Flex,
  Menu,
  MenuButton,
  MenuI<PERSON>,
  <PERSON>u<PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  ModalFooter,
  useToast,
} from '@chakra-ui/react';
import { PLAN_DESIGNS_BASE_PATH } from 'apps/admin-portal/components/benAdmin';
import {
  OrganizationDetails,
  PlanDesignAncillary,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { ConfirmationPopup } from 'apps/admin-portal/components/benAdmin/organization/components/ConfirmationPopup';
import { usePicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistMaps';
import {
  defineFormField,
  defineInlineFieldGroup,
  defineSubCategory,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React, {
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import {
  getAncillaryIdMap,
  syncAcrossPlanDesigns,
} from '../ClinicalDesigns/DynamicForm/paramsUtils';
import { smallintValidation } from '../validations';

interface GenericProductFormProps {
  initialData?: Partial<PlanDesignAncillary>;
  onSave?: (data: any) => void;
  onCancel?: () => void;
  itemIndex?: number;
  formMethods: UseFormReturn<any>;
  productType: string;
  isModal?: boolean;
  newAncillaryIndices?: number[];
  onAncillaryComplete?: (ancillaryIndex: number) => void;
  onUpdateActiveItem?: (item: string) => void;
  ADD_ON_PRODUCTS_ITEM?: string;
}

// Constants
const PAGINATION = {
  NON_MODAL: 2,
  MODAL: 3,
  INITIAL_VISIBLE: 3,
  MAX_VISIBLE: 15,
} as const;

const WATCHED_FIELDS = [
  'effective_date',
  'expiration_date',
  'pharmacy_claim_export_ind',
  'eligibility_export_ind',
  'notes',
] as const;

// Custom hooks
const useProductOptions = (dataRef: PlanDesignAncillary[]) => {
  const getProductOptionAncillaries = useCallback(
    (productId: number | null) => {
      if (!productId || !dataRef) return [];
      return dataRef
        .map((anc, idx) => ({ ...anc, _idx: idx }))
        .filter(
          (anc) =>
            anc.product_id === productId &&
            Object.prototype.hasOwnProperty.call(anc, 'product_options') &&
            anc.product_options != null
        );
    },
    [dataRef]
  );

  const hasOtherActiveProductOption = useCallback(
    (productId: number | null, excludeIdx: number) => {
      const options = getProductOptionAncillaries(productId);
      return options.some(
        (anc) =>
          anc._idx !== excludeIdx &&
          WATCHED_FIELDS.some((field) => {
            const val = (anc as any)[field];
            return val !== null && val !== undefined && val !== '';
          })
      );
    },
    [getProductOptionAncillaries]
  );

  return { getProductOptionAncillaries, hasOtherActiveProductOption };
};

const usePagination = (
  isModal: boolean,
  dataRef: PlanDesignAncillary[],
  itemIndex: number,
  currentPage: number,
  hasProductOptions: boolean
) => {
  return useMemo(() => {
    if (isModal) {
      const targetProductId = dataRef?.[itemIndex]?.product_id;
      const matchingIndices = targetProductId
        ? dataRef.reduce<number[]>(
            (acc, anc, idx) =>
              anc?.product_id === targetProductId ? [...acc, idx] : acc,
            []
          )
        : [itemIndex];

      const totalPages = Math.ceil(matchingIndices.length / PAGINATION.MODAL);
      const start = currentPage * PAGINATION.MODAL;
      const currentIndices = matchingIndices.slice(
        start,
        start + PAGINATION.MODAL
      );

      return { matchingIndices, totalPages, currentIndices };
    }

    if (!hasProductOptions || !dataRef?.length) {
      return { matchingIndices: [], totalPages: 1, currentIndices: [0] };
    }

    const targetId = dataRef[0].plan_design_id;
    const indices = dataRef.reduce<number[]>(
      (acc, anc, idx) =>
        anc.plan_design_id === targetId ? [...acc, idx] : acc,
      []
    );

    const totalPages = Math.ceil(indices.length / PAGINATION.NON_MODAL);
    const start = currentPage * PAGINATION.NON_MODAL;
    const currentIndices = indices.slice(start, start + PAGINATION.NON_MODAL);

    return { matchingIndices: indices, totalPages, currentIndices };
  }, [isModal, dataRef, itemIndex, currentPage, hasProductOptions]);
};

export const GenericProductForm: FC<GenericProductFormProps> = React.memo(
  ({
    initialData = {},
    onSave,
    onCancel,
    itemIndex = -1,
    formMethods,
    productType,
    isModal = true,
    newAncillaryIndices = [],
    onAncillaryComplete,
    onUpdateActiveItem,
    ADD_ON_PRODUCTS_ITEM,
  }) => {
    // State
    const [currentPage, setCurrentPage] = useState(0);
    const [visibleSubCategories, setVisibleSubCategories] = useState<number>(
      PAGINATION.INITIAL_VISIBLE
    );
    const [currentGroupPage, setCurrentGroupPage] = useState(0);
    const [confirmationState, setConfirmationState] = useState({
      show: false,
      pending: null as { field: string; value: any; index: number } | null,
      loading: false,
    });

    // Refs
    const initializedRef = useRef(false);
    const initialModalStateRef = useRef<PlanDesignAncillary[]>([]);

    // Hooks
    const { yesNoMap } = usePicklistMaps();
    const toast = useToast();

    // Data
    const basePath = `${PLAN_DESIGNS_BASE_PATH}.0.plan_design_ancillaries`;
    const dataRef = formMethods.watch(basePath) as PlanDesignAncillary[];
    const hasProductOptions = !!dataRef?.[0]?.product_options;

    // Custom hooks
    const { getProductOptionAncillaries, hasOtherActiveProductOption } =
      useProductOptions(dataRef);
    const paginationData = usePagination(
      isModal,
      dataRef,
      itemIndex,
      currentPage,
      hasProductOptions
    );

    // Store initial modal state
    useEffect(() => {
      if (isModal && dataRef?.length && !initialModalStateRef.current.length) {
        initialModalStateRef.current = JSON.parse(JSON.stringify(dataRef));
      }
    }, [isModal, dataRef]);

    // Group ancillaries for non-modal mode
    const groupedAncillaries = useMemo(() => {
      if (isModal || !newAncillaryIndices.length || !dataRef?.length) {
        return [];
      }

      const groups = newAncillaryIndices.reduce<Record<string, any[]>>(
        (acc, index) => {
          const ancillary = dataRef[index];
          if (!ancillary) return acc;

          const productId = ancillary.product_id || 'no-product-id';
          if (!acc[productId]) acc[productId] = [];
          acc[productId].push({ index, ancillary });
          return acc;
        },
        {}
      );

      return Object.entries(groups).map(([productId, items]) => ({
        productId,
        items,
        productName: items[0]?.ancillary?.product?.name || 'Unknown Product',
      }));
    }, [isModal, newAncillaryIndices, dataRef]);

    // Initialize form values
    useEffect(() => {
      if (
        !initializedRef.current &&
        dataRef &&
        Object.keys(initialData).length
      ) {
        const setValue = (path: string, data: any) => {
          formMethods.setValue(path, data, {
            shouldValidate: false,
            shouldDirty: true,
            shouldTouch: true,
          });
        };

        if (isModal && itemIndex !== -1 && dataRef[itemIndex]) {
          setValue(`${basePath}.${itemIndex}`, {
            ...dataRef[itemIndex],
            ...initialData,
            product_type: productType,
          });
        } else if (!isModal) {
          setValue(basePath, {
            ...dataRef,
            ...initialData,
            product_type: productType,
          });
        }
        initializedRef.current = true;
      }
    }, [
      dataRef,
      initialData,
      productType,
      formMethods,
      basePath,
      isModal,
      itemIndex,
    ]);

    // Null out other product options helper
    const nullOutOtherProductOptions = useCallback(
      (productId: number | null, excludeIdx: number) => {
        const options = getProductOptionAncillaries(productId);
        options.forEach((anc) => {
          if (anc._idx !== excludeIdx) {
            WATCHED_FIELDS.forEach((field) => {
              formMethods.setValue(`${basePath}.${anc._idx}.${field}`, null, {
                shouldDirty: true,
                shouldTouch: true,
              });
            });
          }
        });
      },
      [getProductOptionAncillaries, formMethods, basePath]
    );

    // Field change handler
    const handleWatchedFieldChange = useCallback(
      (field: string, value: any, index: number) => {
        const ancillary = dataRef?.[index];
        if (!ancillary || !ancillary.product_id) {
          formMethods.setValue(`${basePath}.${index}.${field}`, value, {
            shouldDirty: true,
            shouldTouch: true,
          });
          return;
        }

        if (hasOtherActiveProductOption(ancillary.product_id, index)) {
          setConfirmationState({
            show: true,
            pending: { field, value, index },
            loading: false,
          });
        } else {
          formMethods.setValue(`${basePath}.${index}.${field}`, value, {
            shouldDirty: true,
            shouldTouch: true,
          });

          const currentPlanDesignIdx = 0;
          const fieldPath = `${PLAN_DESIGNS_BASE_PATH}[${currentPlanDesignIdx}].plan_design_ancillaries[${index}].${field}`;

          // No longer checking for toggle value - sync is controlled by the sync prop
          syncAcrossPlanDesigns(
            true, // sync enabled
            false, // toggleValue not used in new implementation
            fieldPath,
            value,
            formMethods
          );
        }
      },
      [dataRef, formMethods, basePath, hasOtherActiveProductOption]
    );

    // Confirmation handlers
    const handleConfirm = useCallback(() => {
      if (!confirmationState.pending) return;
      setConfirmationState((prev) => ({ ...prev, loading: true }));

      const { field, value, index } = confirmationState.pending;
      const ancillary = dataRef?.[index];

      if (ancillary && ancillary.product_id) {
        formMethods.setValue(`${basePath}.${index}.${field}`, value, {
          shouldDirty: true,
          shouldTouch: true,
        });
        nullOutOtherProductOptions(ancillary.product_id, index);

        const currentPlanDesignIdx = 0;
        const fieldPath = `${PLAN_DESIGNS_BASE_PATH}[${currentPlanDesignIdx}].plan_design_ancillaries[${index}].${field}`;

        // No longer checking for toggle value
        syncAcrossPlanDesigns(true, false, fieldPath, value, formMethods);
      }

      setConfirmationState({ show: false, pending: null, loading: false });
    }, [
      confirmationState.pending,
      dataRef,
      formMethods,
      basePath,
      nullOutOtherProductOptions,
    ]);

    const isProductOptionActive = useCallback(
      (index: number) => {
        const ancillary = dataRef?.[index];
        if (!ancillary) return false;

        // Consider a product option "active" if it has any of the watched fields filled
        return WATCHED_FIELDS.some((field) => {
          const val = (ancillary as any)[field];
          return val !== null && val !== undefined && val !== '';
        });
      },
      [dataRef]
    );

    const handleCloseConfirm = useCallback(() => {
      setConfirmationState({ show: false, pending: null, loading: false });
    }, []);

    // Create field configuration
    const createFieldConfig = useCallback(
      (index: number) => {
        const path = `${basePath}.${index}`;
        const ancillary = dataRef?.[index];

        // Only validate if this index is currently visible/selected
        const isActive = paginationData.currentIndices.includes(index);

        const getOnChange = (fieldName: string) =>
          WATCHED_FIELDS.includes(fieldName as any)
            ? (val: any) => handleWatchedFieldChange(fieldName, val, index)
            : undefined;

        const fieldGroups = {
          dates: defineInlineFieldGroup([
            defineFormField(
              'Effective Date',
              'datepicker',
              `${path}.effective_date`,
              ancillary?.effective_date,
              {
                isRequired: isProductOptionActive(index),
                validations: z.date(),
                onChange: getOnChange('effective_date'),
                showAncillaryToggle: true,
                toggleLabel: 'Differs from Plan Design',
                formPath: PLAN_DESIGNS_BASE_PATH,
                sync: true,
                syncFunction: syncAcrossPlanDesigns,
                idMap: getAncillaryIdMap(`${path}.effective_date`, formMethods),
              }
            ),
            defineFormField(
              'Expiration Date',
              'datepicker',
              `${path}.expiration_date`,
              ancillary?.expiration_date,
              {
                isRequired: false,
                onChange: getOnChange('expiration_date'),
                showAncillaryToggle: true,
                toggleLabel: 'Differs from Plan Design',
                formPath: PLAN_DESIGNS_BASE_PATH,
                sync: true,
                syncFunction: syncAcrossPlanDesigns,
                idMap: getAncillaryIdMap(
                  `${path}.expiration_date`,
                  formMethods
                ),
              }
            ),
          ]),
          exports: defineInlineFieldGroup([
            defineFormField(
              'Eligibility Exports',
              'dropdownSelect',
              `${path}.eligibility_export_ind`,
              ancillary?.eligibility_export_ind,
              {
                isRequired: isProductOptionActive(index),
                optionsMap: yesNoMap,
                validations: isActive ? smallintValidation : undefined,
                onChange: getOnChange('eligibility_export_ind'),
                showAncillaryToggle: true,
                toggleLabel: 'Differs from Plan Design',
                formPath: PLAN_DESIGNS_BASE_PATH,
                sync: true,
                syncFunction: syncAcrossPlanDesigns,
                idMap: getAncillaryIdMap(
                  `${path}.eligibility_export_ind`,
                  formMethods
                ),
              }
            ),
            defineFormField(
              'Pharmacy Claims Export',
              'dropdownSelect',
              `${path}.pharmacy_claim_export_ind`,
              ancillary?.pharmacy_claim_export_ind,
              {
                isRequired: isProductOptionActive(index),
                optionsMap: yesNoMap,
                validations: isActive ? smallintValidation : undefined,
                onChange: getOnChange('pharmacy_claim_export_ind'),
                showAncillaryToggle: true,
                toggleLabel: 'Differs from Plan Design',
                formPath: PLAN_DESIGNS_BASE_PATH,
                sync: true,
                syncFunction: syncAcrossPlanDesigns,
                idMap: getAncillaryIdMap(
                  `${path}.pharmacy_claim_export_ind`,
                  formMethods
                ),
              }
            ),
          ]),
          notes: defineInlineFieldGroup([
            defineFormField(
              'Notes',
              'textarea',
              `${path}.notes`,
              ancillary?.notes,
              {
                isRequired: false,
                onChange: getOnChange('notes'),
                validations: undefined,
                showAncillaryToggle: true,
                toggleLabel: 'Differs from Plan Design',
                formPath: PLAN_DESIGNS_BASE_PATH,
                rows: 5,
                customProps: {
                  minHeight: '120px',
                  overflow: 'hidden',
                },
                sync: true,
                syncFunction: syncAcrossPlanDesigns,
              }
            ),
          ]),
        };

        return fieldGroups;
      },
      [
        basePath,
        dataRef,
        paginationData.currentIndices,
        isProductOptionActive,
        yesNoMap,
        handleWatchedFieldChange,
      ]
    );

    // Form configuration
    const formConfig = useMemo(() => {
      const createSubCategory = (item: any, index: number) => {
        const fields = createFieldConfig(index);
        const title = item?.product_options || 'No Product Options';
        return defineSubCategory(title, ``, [
          fields.dates,
          fields.exports,
          fields.notes,
        ]);
      };

      if (isModal) {
        if (!dataRef?.length || !paginationData.currentIndices?.length) {
          return { subCategories: [], hasMore: false };
        }

        const subCategories = paginationData.currentIndices
          .map((index) => createSubCategory(dataRef[index], index))
          .filter(Boolean);

        return { subCategories, hasMore: false };
      }

      if (!groupedAncillaries.length) {
        return { subCategories: [], hasMore: false };
      }

      const group = groupedAncillaries[currentGroupPage];
      if (!group) return { subCategories: [], hasMore: false };

      const subCategories = group.items.map((item: any) =>
        createSubCategory(item.ancillary, item.index)
      );

      return { subCategories, hasMore: false };
    }, [
      isModal,
      dataRef,
      paginationData.currentIndices,
      createFieldConfig,
      groupedAncillaries,
      currentGroupPage,
    ]);

    // Add custom validation function
    const hasAtLeastOneActiveOption = useCallback(() => {
      if (!dataRef || !paginationData.currentIndices?.length) {
        return false;
      }
      // Only require the first visible ancillary to be filled
      const result =
        !!dataRef[paginationData.currentIndices[0]]?.effective_date;

      return result;
    }, [dataRef, paginationData.currentIndices]);

    // Navigation handler
    const handleNavigation = useCallback(
      (direction: 'next' | 'back') => {
        const completeAndNotify = () => {
          if (onAncillaryComplete) {
            newAncillaryIndices.forEach(onAncillaryComplete);
          }
          if (onUpdateActiveItem && ADD_ON_PRODUCTS_ITEM) {
            onUpdateActiveItem(ADD_ON_PRODUCTS_ITEM);
          }
          toast({
            title: 'Add-ons successfully created!',
            status: 'success',
            duration: 4000,
            isClosable: true,
            position: 'top',
          });
          onCancel?.();
        };

        if (direction === 'next') {
          const canProceed = hasAtLeastOneActiveOption();

          if (!canProceed) {
            toast({
              title:
                'Please assign an effective date to at least one product option before continuing.',
              status: 'error',
              duration: 4000,
              isClosable: true,
              position: 'top',
            });
            return;
          }
        }

        if (isModal) {
          if (direction === 'next') {
            if (currentPage < paginationData.totalPages - 1) {
              setCurrentPage((prev) => prev + 1);
            } else {
              onCancel?.();
            }
          } else {
            if (currentPage > 0) {
              setCurrentPage((prev) => prev - 1);
            } else {
              if (initialModalStateRef.current.length > 0) {
                formMethods.setValue(basePath, initialModalStateRef.current, {
                  shouldValidate: false,
                  shouldDirty: false,
                  shouldTouch: false,
                });
              }
              onCancel?.();
            }
          }
        } else {
          if (direction === 'next') {
            if (currentGroupPage < groupedAncillaries.length - 1) {
              setCurrentGroupPage((prev) => prev + 1);
            } else {
              completeAndNotify();
            }
          } else {
            if (currentGroupPage > 0) {
              setCurrentGroupPage((prev) => prev - 1);
            } else {
              onCancel?.();
            }
          }
        }
      },
      [
        currentPage,
        paginationData,
        isModal,
        onAncillaryComplete,
        onUpdateActiveItem,
        ADD_ON_PRODUCTS_ITEM,
        toast,
        onCancel,
        newAncillaryIndices,
        hasAtLeastOneActiveOption,
        formMethods,
        basePath,
        currentGroupPage,
        groupedAncillaries.length,
      ]
    );

    // Handle form submission
    const handleFormSubmit = useCallback(
      (formData: OrganizationDetails) => onSave?.(formData),
      [onSave]
    );

    // UI components
    const PageSelector = useCallback(() => {
      const { totalPages } = paginationData;
      const showPagination =
        (isModal && totalPages > 1) ||
        (!isModal && hasProductOptions && totalPages > 1);

      if (!showPagination) return null;

      return (
        <Menu>
          <MenuButton
            as={Button}
            rightIcon={<ChevronDownIcon />}
            size="sm"
            variant="outline"
            colorScheme="gray"
          >
            Page {currentPage + 1} of {totalPages}
          </MenuButton>
          <MenuList>
            {Array.from({ length: totalPages }, (_, i) => (
              <MenuItem
                key={i}
                onClick={() => setCurrentPage(i)}
                bg={currentPage === i ? 'gray.50' : 'transparent'}
              >
                {currentPage === i && (
                  <ChevronDownIcon
                    transform="rotate(-90deg)"
                    color="gray.500"
                  />
                )}
                Page {i + 1}
              </MenuItem>
            ))}
          </MenuList>
        </Menu>
      );
    }, [paginationData, isModal, hasProductOptions, currentPage]);

    const FooterButtons = useCallback(() => {
      const isLastPage = isModal
        ? currentPage === paginationData.totalPages - 1
        : currentGroupPage === groupedAncillaries.length - 1;
      const isFirstPage = isModal ? currentPage === 0 : currentGroupPage === 0;
      const nextButtonText = isModal
        ? isLastPage
          ? 'Save'
          : 'Next'
        : isLastPage
        ? 'Confirm and Continue'
        : 'Continue to Next Product';

      return (
        <Flex gap={3} ml="auto">
          <Button variant="outline" onClick={() => handleNavigation('back')}>
            {isFirstPage ? 'Cancel' : 'Previous'}
          </Button>
          <Button
            colorScheme="green"
            onClick={
              isModal && isLastPage ? onCancel : () => handleNavigation('next')
            }
          >
            {nextButtonText}
          </Button>
        </Flex>
      );
    }, [
      isModal,
      currentPage,
      paginationData.totalPages,
      currentGroupPage,
      groupedAncillaries.length,
      handleNavigation,
      onCancel,
    ]);

    // Form props
    const formProps = useMemo(() => {
      const currentGroup = groupedAncillaries[currentGroupPage];
      const productName = isModal
        ? dataRef?.[itemIndex]?.product?.name || 'Product Details'
        : currentGroup?.productName || '';

      const getDescription = () => {
        if (!isModal && currentGroup) {
          return (
            <>
              <span style={{ fontWeight: 700 }}>
                {`Choose your options for ${currentGroup.productName} add-ons`}
              </span>
              <br />
              <span>
                Select which add-on products are different by plan-type, if
                applicable, and enter product specific information.
              </span>
            </>
          );
        }

        if (isModal) {
          const matchCount = paginationData.matchingIndices.length;
          const pageInfo =
            paginationData.totalPages > 1
              ? ` (Page ${currentPage + 1} of ${paginationData.totalPages})`
              : '';

          return matchCount > 1
            ? `Enter the details for this product across ${matchCount} plan designs${pageInfo}`
            : 'Enter the details for this product';
        }

        return null;
      };

      return {
        formName: productName,
        subtitle: isModal ? productName : currentGroup?.productName || '',
        formDescription: getDescription(),
      };
    }, [
      groupedAncillaries,
      currentGroupPage,
      isModal,
      dataRef,
      itemIndex,
      paginationData,
      currentPage,
    ]);

    return (
      <>
        {isModal ? (
          <>
            <ModalBody>
              <GenericForm
                formMethods={formMethods}
                formName={formProps.formName}
                formDescription={formProps.formDescription}
                subCategories={formConfig.subCategories}
                showButtons={false}
                isInModal={true}
                onSubmit={
                  onSave
                    ? formMethods.handleSubmit(handleFormSubmit)
                    : undefined
                }
                onBack={() => handleNavigation('back')}
              />
            </ModalBody>
            <ModalFooter>
              <Flex width="100%" justify="space-between" align="center">
                <PageSelector />
                <FooterButtons />
              </Flex>
            </ModalFooter>
          </>
        ) : (
          <Box>
            <GenericForm
              formMethods={formMethods}
              subtitle={formProps.subtitle}
              formDescription={formProps.formDescription}
              subCategories={formConfig.subCategories}
              showButtons={true}
              isInModal={false}
              onSubmit={
                onSave ? formMethods.handleSubmit(handleFormSubmit) : undefined
              }
              onBack={() => handleNavigation('back')}
              onContinue={() => handleNavigation('next')}
              continueButtonText={
                currentGroupPage < groupedAncillaries.length - 1
                  ? 'Continue to Next Product'
                  : 'Confirm and Continue'
              }
            />
            {formConfig.hasMore && (
              <Flex justify="center" mt={4}>
                <Button
                  variant="outline"
                  onClick={() =>
                    setVisibleSubCategories((prev) =>
                      Math.min(prev + 3, PAGINATION.MAX_VISIBLE)
                    )
                  }
                  isDisabled={visibleSubCategories >= PAGINATION.MAX_VISIBLE}
                >
                  Show More Options
                </Button>
              </Flex>
            )}
          </Box>
        )}
        <ConfirmationPopup
          isOpen={confirmationState.show}
          onClose={handleCloseConfirm}
          onConfirm={handleConfirm}
          alertHeader="Switch Product Option?"
          alertBody={
            'Changing this value will remove the previous product option from this plan design and assign the one you just updated as the active product option. Continue?'
          }
          confirmButtonText="Yes, Switch"
          isConfirmLoading={confirmationState.loading}
        />
      </>
    );
  },
  (prevProps, nextProps) =>
    prevProps.itemIndex === nextProps.itemIndex &&
    prevProps.productType === nextProps.productType &&
    prevProps.initialData?.product_id === nextProps.initialData?.product_id
);

GenericProductForm.displayName = 'GenericProductForm';
