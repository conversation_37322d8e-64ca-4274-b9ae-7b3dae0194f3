import { InlineEditColumn } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Table/FormTable';
import { useMemo } from 'react';

import {
  formatCurrency,
  formatDate,
  formatPercentage,
  formatYesNo,
} from './utils/costShareUtils';

/**
 * Calculate column width based on header text length with override support
 */
const calculateColumnWidth = (
  headerText: string,
  min = 50,
  max = 300,
  overrideMap?: Record<string, string>
): string => {
  // Check if there's a custom override for this header text
  if (overrideMap && overrideMap[headerText]) {
    return overrideMap[headerText];
  }

  const baseWidth = headerText.length * 8 + 20;
  const clampedWidth = Math.max(min, Math.min(max, baseWidth));
  return `${clampedWidth}px`;
};

/**
 * Pure function to generate cost share columns, optionally read-only.
 */
export function getCostShareColumns(
  maps: any,
  isESI: boolean,
  columnWidth = '200px',
  readOnly = false,
  showTierType = false,
  customColumnWidths?: Record<string, string>,
  autoSizeColumns = false,
  columnWidthOverrides?: Record<string, string>
): InlineEditColumn[] {
  const editable = (def: boolean) => (readOnly ? false : def);
  const baseColumns: InlineEditColumn[] = [
    {
      key: 'name',
      header: 'Cost Share Tier Name',
      editable: false,
      editType: 'text',
      placeholder: 'Set by system...',
      tooltip: 'Cost Share Tier Name',
    },
    ...(showTierType
      ? [
          {
            key: 'pre_packaged_ind',
            header: 'Tier Type',
            editable: false,
            editType: 'text' as const,
            placeholder: '--',
            tooltip: 'Tier Type',
            format: (value: any) => {
              const numValue = Number(value);
              return numValue === 1 ? 'Unbreakable' : 'Standard';
            },
          },
        ]
      : []),
    {
      key: 'pbc_print_order',
      header: 'PBC Print Order',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Enter order...',
      tooltip: 'PBC Print Order',
      format: (value: any) => value || '--',
    },
    {
      key: 'pdx_print_order',
      header: 'PDX Print Order',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Enter order...',
      tooltip: 'PDX Print Order',
      format: (value: any) => value || '--',
    },
    {
      key: 'effective_date',
      header: 'Effective Date',
      editable: editable(true),
      editType: 'date',
      placeholder: 'Select date',
      tooltip: 'Effective Date',
      format: (value: any) => formatDate(value),
    },
    {
      key: 'expiration_date',
      header: 'Expiration Date',
      editable: editable(true),
      editType: 'date',
      placeholder: 'Select date',
      tooltip: 'Expiration Date',
      format: (value: any) => formatDate(value),
    },
    {
      key: 'tier_ind',
      header: 'Drug Tier',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.costShareTierMap,
      searchable: true,
      placeholder: 'Select tier...',
      tooltip: 'Drug Tier',
      format: (value: any) =>
        value ? `${maps.costShareTierMap?.[value] || value}` : '--',
    },
    {
      key: 'pharmacy_channel_ind',
      header: 'Pharmacy Channel',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.pharmacyChannelMap,
      searchable: true,
      placeholder: 'Select channel...',
      tooltip: 'Pharmacy Channel',
      format: (value: any) => maps.pharmacyChannelMap?.[value] || value || '--',
    },
    {
      key: 'days_supply',
      header: 'Days Supply',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.costShareTierDaysSupplyMap,
      placeholder: 'Select days...',
      tooltip: 'Days Supply',
      format: (value: any) =>
        maps.costShareTierDaysSupplyMap?.[value] || value || '--',
    },
    {
      key: 'co_pay_amount',
      header: 'Copay Amount',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Enter amount...',
      tooltip: 'Copay Amount',
      format: (value: any) => formatCurrency(value),
    },
    {
      key: 'co_insurance_pct',
      header: 'Coinsurance %',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Enter %...',
      tooltip: 'Coinsurance %',
      format: (value: any) => formatPercentage(value),
    },
    {
      key: 'co_insurance_ltgt_ind',
      header: 'Lesser/Greater',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.coInsuranceLesserGreaterMap,
      placeholder: 'Select...',
      tooltip: 'Lesser/Greater',
      format: (value: any) =>
        maps.coInsuranceLesserGreaterMap?.[value] || value || '--',
    },
    {
      key: 'co_insurance_min_amount',
      header: 'Min Amount',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Min amount...',
      tooltip: 'Min Amount',
      format: (value: any) => formatCurrency(value),
    },
    {
      key: 'co_insurance_max_amount',
      header: 'Max Amount',
      editable: editable(true),
      editType: 'number',
      placeholder: 'Max amount...',
      tooltip: 'Max Amount',
      format: (value: any) => formatCurrency(value),
    },
    {
      key: 'network_status_ind',
      header: 'Network Status',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.networkStatusMap,
      placeholder: 'Select status...',
      tooltip: 'Network Status',
      format: (value: any) =>
        value ? `${maps.networkStatusMap?.[value] || '--'}` : '--',
    },
    {
      key: 'pre_packaged_ind',
      header: 'Prepackage Indicator',
      editable: editable(true),
      editType: 'select',
      editOptions: {
        '0': 'Standard',
        '1': 'Unbreakable',
      },
      placeholder: 'Select type...',
      tooltip: 'Prepackage Indicator',
      format: (value: any) => {
        if (value === '0') return 'Standard';
        if (value === '1') return 'Unbreakable';
        if (value === null || value === undefined || value === '') return '--';
        return value;
      },
    },
    // ESI-only fields
    ...(isESI
      ? [
          {
            key: 'drug_list_ind',
            header: 'Drug List',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiDrugListMap,
            searchable: false,
            placeholder: 'Select list...',
            tooltip: 'Drug List',
            format: (value: any) =>
              maps.esiDrugListMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_type_ind',
            header: 'Copay Type Indicator',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayTierMap,
            placeholder: 'Select type...',
            tooltip: 'Copay Type Indicator',
            format: (value: any) =>
              maps.esiCopayTierMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_channel_ind',
            header: 'Copay Channel Delivery System',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayChannelMap,
            placeholder: 'Select channel...',
            tooltip: 'Copay Channel Delivery System',
            format: (value: any) =>
              maps.esiCopayChannelMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_structure_ind',
            header: 'Copay Structure',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayStructureMap,
            placeholder: 'Select structure...',
            tooltip: 'Copay Structure',
            format: (value: any) =>
              maps.esiCopayStructureMap?.[value] || value || '--',
          },
          {
            key: 'esi_copay_network_ind',
            header: 'Copay Network',
            editable: editable(true),
            editType: 'select' as const,
            editOptions: maps.esiCopayNetworkMap,
            placeholder: 'Select network...',
            tooltip: 'Copay Network',
            format: (value: any) =>
              maps.esiCopayNetworkMap?.[value] || value || '--',
          },
        ]
      : []),
    {
      key: 'include_in_pbc',
      header: 'Include PBC',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.yesNoMap,
      placeholder: 'Select...',
      tooltip: 'Include PBC',
      format: (value: any) => {
        return formatYesNo(value);
      },
    },
    {
      key: 'include_in_pdx',
      header: 'Include in PDX',
      editable: editable(true),
      editType: 'select',
      editOptions: maps.yesNoMap,
      placeholder: 'Select...',
      tooltip: 'Include in PDX',
      format: (value: any) => {
        return formatYesNo(value);
      },
    },
  ];

  return baseColumns.map((column) => {
    let width = columnWidth;

    if (customColumnWidths?.[column.key]) {
      // Use custom width if provided
      width = customColumnWidths[column.key];
    } else if (autoSizeColumns) {
      // Auto-calculate width based on header text length with override support
      width = calculateColumnWidth(
        column.header,
        100,
        300,
        columnWidthOverrides
      );
    }

    return {
      ...column,
      width,
    };
  });
}

/**
 * React hook to memoize cost share columns for editable tables.
 */
export function useCostShareColumns(
  maps: any,
  isESI: boolean,
  columnWidth = '200px',
  showTierType = false,
  customColumnWidths?: Record<string, string>,
  autoSizeColumns = false,
  columnWidthOverrides?: Record<string, string>
): InlineEditColumn[] {
  return useMemo(
    () =>
      getCostShareColumns(
        maps,
        isESI,
        columnWidth,
        false,
        showTierType,
        customColumnWidths,
        autoSizeColumns,
        columnWidthOverrides
      ),
    [
      maps,
      isESI,
      columnWidth,
      showTierType,
      customColumnWidths,
      autoSizeColumns,
      columnWidthOverrides,
    ]
  );
}
