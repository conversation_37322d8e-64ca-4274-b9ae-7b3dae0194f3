import { useDisclosure, useToast } from '@chakra-ui/react';
import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import {
  ChangeRequest,
  cleanUrlParams,
  GENERAL_ITEM,
  PLAN_DESIGNS_BASE_PATH,
  PlanDesign,
  updateEditMode,
  updateParams,
} from 'apps/admin-portal/components/benAdmin';
import { ConfirmationPopup } from 'apps/admin-portal/components/benAdmin/organization/components/ConfirmationPopup';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import DynamicCollectionSection from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/DynamicCollectionSection';
import { useParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { IoIosCheckmark } from 'react-icons/io';
import { PiWarningFill, PiWarningOctagonFill } from 'react-icons/pi';

import { useValidationContext } from '../../../../Validations/ValidationContext';
import {
  syncPlanDesignStructure,
  syncValuesToNewPlanDesign,
} from '../../ClinicalDesigns/DynamicForm/paramsUtils';
import { NewPlanDesignModal } from './NewPlanDesign/NewPlanDesignModal';

/**
 * PlanListComponent that uses the DynamicCollectionSection
 */
const PlanListComponent: React.FC<{
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (id: string) => void;
}> = ({ formMethods, onUpdateActiveItem }) => {
  const [isCreating, setIsCreating] = useState(false);
  const [showWarningModal, setShowWarningModal] = useState(false);
  const changeRequest = JSON.parse(
    sessionStorage.getItem('selectedChangeRequest') || 'null'
  ) as ChangeRequest | null;
  const [pendingPlanDesign, setPendingPlanDesign] = useState<PlanDesign | null>(
    null
  );
  const [selectedIndex, setSelectedIndex] = useState<string>();
  const toast = useToast();
  //   const submitHandler = useSaveChangeRequestHandler(formMethods);
  const plan_id = formMethods.getValues('plan.plan_id');
  const { setValue } = formMethods;
  const params = useParams();

  const { isOpen, onOpen, onClose } = useDisclosure();

  const { useApiMutation, useApiQuery } = useBenAdmin();

  // Use mutation for creating plan design
  const { mutateAsync: createPlanDesign } = useApiMutation(
    'planDesign',
    'POST',
    {
      // Request for complete data structure in response
      include: ['plan_design_details', 'plan_features'],
    }
  );

  const {
    existingPlanDesignsFromChangeRequest: changeRequestPlanDesigns,
    isLoading: arePlanDesignsLoading,
  } = useApiQuery([
    {
      key: 'existingPlanDesignsFromChangeRequest',
      pathParams: {
        id: params?.changeRequestId,
      },
    },
  ]);

  const hasExistingPlanDesigns =
    formMethods.getValues(PLAN_DESIGNS_BASE_PATH).length > 0;

  // Function to check if a plan design has null values
  const hasNullValues = (planDesign: PlanDesign): boolean => {
    const checkForNulls = (obj: any): boolean => {
      if (obj === null) return true;
      if (typeof obj !== 'object') return false;

      if (Array.isArray(obj)) {
        return obj.some((item) => checkForNulls(item));
      }

      return Object.values(obj).some((value) => checkForNulls(value));
    };

    return checkForNulls(planDesign);
  };

  // Function to handle the warning modal confirmation
  const handleWarningConfirm = async () => {
    setShowWarningModal(false);
    if (pendingPlanDesign) {
      await proceedWithPlanDesignCreation(pendingPlanDesign);
    }
  };

  // Function to proceed with plan design creation after warning
  const proceedWithPlanDesignCreation = async (newPlanDesign: PlanDesign) => {
    try {
      const effectiveDate = changeRequest?.target_effective_date;

      // Get current plan designs array
      const currentPlanDesigns =
        formMethods.getValues(PLAN_DESIGNS_BASE_PATH) || [];

      // Generate a unique name for the new plan design
      if (!newPlanDesign.name) {
        const baseName = 'New Plan Design';
        let nameToUse = baseName;
        let counter = 2;

        const existingNames = currentPlanDesigns.map(
          (design: PlanDesign) => design.name
        );

        while (existingNames.includes(nameToUse)) {
          nameToUse = `${baseName} ${counter}`;
          counter++;
        }
        newPlanDesign.plan_design_details[0].effective_date =
          effectiveDate || '';
        newPlanDesign.plan_design_details[0].accumulation_other = [];
        newPlanDesign.plan_design_details[0].accumulation_moop = [];
        newPlanDesign.plan_design_details[0].accumulation_deductible = [];
        newPlanDesign.name = nameToUse;
        newPlanDesign.active_ind = 1;

        // Set effective dates on cost share tiers (copay tiers) to match plan design effective date
        if (
          newPlanDesign.plan_design_details[0].cost_share_tiers &&
          Array.isArray(newPlanDesign.plan_design_details[0].cost_share_tiers)
        ) {
          newPlanDesign.plan_design_details[0].cost_share_tiers.forEach(
            (tier: any) => {
              tier.effective_date = effectiveDate || '';
            }
          );
        }

        // Set effective dates on accumulator tiers to match plan design effective date
        if (
          newPlanDesign.plan_design_details[0].accumulation_deductible &&
          Array.isArray(
            newPlanDesign.plan_design_details[0].accumulation_deductible
          )
        ) {
          newPlanDesign.plan_design_details[0].accumulation_deductible.forEach(
            (tier: any) => {
              tier.effective_date = effectiveDate || '';
            }
          );
        }

        if (
          newPlanDesign.plan_design_details[0].accumulation_moop &&
          Array.isArray(newPlanDesign.plan_design_details[0].accumulation_moop)
        ) {
          newPlanDesign.plan_design_details[0].accumulation_moop.forEach(
            (tier: any) => {
              tier.effective_date = effectiveDate || '';
            }
          );
        }

        if (
          newPlanDesign.plan_design_details[0].accumulation_other &&
          Array.isArray(newPlanDesign.plan_design_details[0].accumulation_other)
        ) {
          newPlanDesign.plan_design_details[0].accumulation_other.forEach(
            (tier: any) => {
              tier.effective_date = effectiveDate || '';
            }
          );
        }
      }

      // Sync structure of existing plan designs to match the new one
      currentPlanDesigns.forEach((currentDesign: PlanDesign) => {
        syncPlanDesignStructure(currentDesign, newPlanDesign);
      });

      // Add the new plan design to the existing array
      const updatedPlanDesigns = [...currentPlanDesigns, newPlanDesign];
      const newIndex = updatedPlanDesigns.length - 1;

      // Set the updated array back to the form
      setValue(PLAN_DESIGNS_BASE_PATH, updatedPlanDesigns, {
        shouldValidate: true,
        shouldDirty: true,
      });

      // Now sync values after setValue using the new optimized approach
      // Sync when creating new plan designs and there are existing plan designs to inherit from
      if (updatedPlanDesigns.length > 1) {
        // Use the new optimized sync function that compares values across all plan designs
        syncValuesToNewPlanDesign(formMethods, newIndex);
      }

      toast({
        title: 'Plan design created successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      updateParams({ index: newIndex.toString() });
      await handleNavigation(GENERAL_ITEM);
      updateEditMode(true);
    } catch (error) {
      console.error('Failed to create plan design:', error);
      toast({
        title: 'Failed to create plan design',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      throw error;
    } finally {
      setIsCreating(false);
      setPendingPlanDesign(null);
    }
  };

  // Function to create a new plan design via API
  const handleCreatePlanDesign = async () => {
    if (isCreating) return;

    setIsCreating(true);
    try {
      const initialData = {
        plan_id,
      };

      const response = await createPlanDesign({
        pathParams: { id: plan_id },
        queryParams: {
          change_request_id: Number(params?.changeRequestId),
        },
        ...initialData,
      });

      const convertZerosToNulls = (obj: any): any => {
        if (obj === null || obj === undefined) return obj;
        if (obj === 0) return null;

        if (typeof obj !== 'object') return obj;

        if (Array.isArray(obj)) {
          return obj.map((item) => convertZerosToNulls(item));
        }

        const result: Record<string, any> = {};
        for (const key in obj) {
          if (Object.prototype.hasOwnProperty.call(obj, key)) {
            result[key] = ['include_in_pdx', 'include_in_pbc'].includes(key)
              ? obj[key]
              : convertZerosToNulls(obj[key]);
          }
        }

        return result;
      };

      const rawPlanDesign = response[0];
      const newPlanDesign = convertZerosToNulls(rawPlanDesign) as PlanDesign;

      // Check if the plan design has null values
      if (hasExistingPlanDesigns && hasNullValues(newPlanDesign)) {
        setPendingPlanDesign(newPlanDesign);
        setShowWarningModal(true);
        return;
      }

      // If no null values, proceed with creation
      await proceedWithPlanDesignCreation(newPlanDesign);
    } catch (error) {
      console.error('Failed to create plan design:', error);
      toast({
        title: 'Failed to create plan design',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      setIsCreating(false);
      throw error;
    }
  };

  // Clean up URL parameters when component mounts
  useEffect(() => {
    cleanUrlParams();
  }, []);

  /**
   * Handle save and exit action
   */
  //   const handleSaveAndExit = () => {
  //     // Submit the current form data
  //     const currentValues = formMethods.getValues();
  //     submitHandler(currentValues);
  //   };

  /**
   * Handle navigation to a different section
   */
  const handleNavigation = (destination: string) => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(destination);
    }
  };

  /**
   * Custom handler for "Add New" button
   */
  const handleAddItem = async () => {
    try {
      await handleCreatePlanDesign();
      // Navigation handled in handleCreatePlanDesign
    } catch (error) {
      // Error handling done in handleCreatePlanDesign
    }
  };

  /**
   * Custom handler for "Edit" button
   */
  const handleEditItem = (index: number) => {
    // Navigate to the GENERAL_ITEM
    handleNavigation(GENERAL_ITEM);

    // Update the edit mode to true
    updateEditMode(true);

    // Also update the index parameter
    updateParams({ index: index.toString() });
  };

  const { validationData } = useValidationContext();

  const getPlanDesignValidationStatus = (
    planIndex: number
  ): 'success' | 'warning' | 'error' => {
    if (!validationData?.results) return 'success';

    let hasError = false;
    let hasWarning = false;
    const PLANDESIGN_CONTEXT_IDS = Array.from({ length: 10 }, (_, i) =>
      (i + 12).toString()
    );

    Object.entries(validationData.results).forEach(([contextId, result]) => {
      if (!PLANDESIGN_CONTEXT_IDS.includes(contextId)) return;

      if (
        result?.errors?.some((error) =>
          error?.field?.includes(`plan_designs.${planIndex}.`)
        ) ||
        result?.validation_results?.errors?.some((error) =>
          error?.field?.includes(`plan_designs.${planIndex}.`)
        )
      )
        hasError = true;
      if (
        result?.warnings?.some((warning) =>
          warning?.field?.includes(`plan_designs.${planIndex}.`)
        ) ||
        result?.validation_results?.warnings?.some((warning) =>
          warning?.field?.includes(`plan_designs.${planIndex}.`)
        )
      )
        hasWarning = true;
    });

    if (hasError) return 'error';
    if (hasWarning) return 'warning';
    return 'success';
  };

  const getStatusIcon = (status: string) => {
    if (status === 'error')
      return <PiWarningOctagonFill color="red" size={20} />;
    if (status === 'warning') return <PiWarningFill color="orange" size={20} />;
    return <IoIosCheckmark color="green" size={30} />;
  };

  return (
    <>
      <DynamicCollectionSection
        basePath={PLAN_DESIGNS_BASE_PATH}
        formMethods={formMethods}
        addCustomVars={[['plan_id', plan_id]]}
        title="Plan Design List"
        description=""
        emptyMessage="You haven't added anything."
        skipMessage="To get started, create a new Plan Design option to start customizing it."
        modalTitle="Pharmacy Accumulators - Other Cap"
        // onSaveExit={handleSaveAndExit}
        createEmptyObject={false}
        // onAddItem={handleAddItem}
        onAddItem={onOpen}
        editButtonText="Edit Plan"
        onEditItem={handleEditItem}
        useAccordion={true}
        isAddButtonLoading={isCreating}
        hideContinueButton={true}
        getStatusIcon={getStatusIcon}
        getPlanDesignValidationStatus={getPlanDesignValidationStatus}
        additionalOptions={[
          {
            optionName: 'Make a Copy',
            isOptionDisabled: arePlanDesignsLoading,
            onOptionClick: (index) => {
              setSelectedIndex(String(index)); // need a string so 0 can be truthy
              onOpen();
            },
          },
        ]}
      />

      <NewPlanDesignModal
        isModalOpen={isOpen}
        onModalClose={onClose}
        onCreateBlankPlanDesign={handleAddItem}
        isCreateLoading={isCreating}
        formMethods={formMethods}
        existingPlanDesigns={changeRequestPlanDesigns}
        areExistingPlanDesignsLoading={arePlanDesignsLoading}
        setCopyFromListIndex={setSelectedIndex}
        copyFromListIndex={selectedIndex}
      />

      <ConfirmationPopup
        isOpen={showWarningModal}
        onClose={handleWarningConfirm}
        onConfirm={handleWarningConfirm}
        alertHeader="Plan Design Fields Differ"
        alertBody="Some fields differ between your plan designs. To prevent these fields from appearing blank, please update the Clinical Design."
        confirmButtonText="Continue"
        hideCancelButton={true}
      />
    </>
  );
};

export default PlanListComponent;
