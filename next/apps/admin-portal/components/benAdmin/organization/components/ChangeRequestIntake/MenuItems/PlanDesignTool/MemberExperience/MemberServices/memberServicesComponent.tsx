'use client';

import { Flex } from '@chakra-ui/react';
import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { useValidateByPageFromContext } from 'apps/admin-portal/components/benAdmin/organization/components/ChangeRequestIntake/Validations/ValidationContext';
import { useContinueHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useContinueHandler';
import { useHelpCenter } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useHelpCenter';
import { useValidationErrorsForForm } from 'apps/admin-portal/components/benAdmin/organization/hooks/ChangeRequestIntake/useValidationErrorsForForm';
// import { useSaveChangeRequestHandler } from 'apps/admin-portal/components/benAdmin/organization/hooks/useOrganizationHooks';
import GenericForm from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/GenericForm';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';

import { HelpCenter } from '../../../../IntakeComponents/HelpCenter/HelpCenter';
import {
  MEMBER_SERVICES_ITEM,
  MEMBER_SERVICES_REVIEW_ITEM,
  WELCOME_KIT_AND_LETTERS_ITEM,
} from '../../../../Navigation/navigationConstants';
import { getUIContextFromNavigationConstant } from '../../../../Navigation/uiContextEnum';
import { useMemberServicesForm } from './memberServicesForm';

interface MemberServicesComponentProps {
  formMethods: UseFormReturn<OrganizationDetails>;
  onUpdateActiveItem?: (id: string) => void;
}

const MemberServicesComponent: React.FC<MemberServicesComponentProps> = ({
  formMethods,
  onUpdateActiveItem,
}) => {
  const { getValues, setValue } = formMethods;
  const currentDetails = getValues();

  const { refetch, validationData, isLoading } =
    useValidateByPageFromContext(MEMBER_SERVICES_ITEM);

  const { helpCenterData, isFetching } = useHelpCenter(MEMBER_SERVICES_ITEM);

  const uiContextIndex =
    getUIContextFromNavigationConstant(MEMBER_SERVICES_ITEM);

  const { subCategories } = useMemberServicesForm(
    currentDetails as OrganizationDetails,
    setValue
  );

  //   const submitHandler = useSaveChangeRequestHandler(formMethods);

  //   const handleSaveAndExit = () => {
  //     // Submit the current form data
  //     const currentValues = formMethods.getValues();
  //     submitHandler(currentValues);
  //   };

  const { createContinueHandler, isContinueLoading } = useContinueHandler({
    refetch,
    onUpdateActiveItem,
    formMethods,
  });

  const handleContinue = createContinueHandler(
    MEMBER_SERVICES_ITEM,
    MEMBER_SERVICES_REVIEW_ITEM
  );

  const handleBack = () => {
    if (onUpdateActiveItem) {
      onUpdateActiveItem(WELCOME_KIT_AND_LETTERS_ITEM);
    }
  };

  const contextIndex = getUIContextFromNavigationConstant(MEMBER_SERVICES_ITEM);

  useValidationErrorsForForm({
    subCategories,
    formMethods,
    validationData,
    contextIndex,
  });

  // Use continue loading state or validation loading state, whichever is true
  const isProcessing = isContinueLoading || isLoading;
  return (
    <Flex justify="space-between">
      <GenericForm
        formName="Member Services Providers"
        formDescription=""
        formMethods={formMethods}
        subCategories={subCategories}
        onContinue={handleContinue}
        onBack={handleBack}
        // onSaveExit={handleSaveAndExit}
        isProcessing={isProcessing}
      />
      <HelpCenter
        validationResults={
          uiContextIndex
            ? validationData?.results?.[uiContextIndex]?.validation_results
            : undefined
        }
        helpContent={helpCenterData}
        isLoading={isFetching}
      />
    </Flex>
  );
};

export default MemberServicesComponent;
