import { useBenAdmin } from 'apps/admin-portal/app/_hooks/useBenAdmin';
import { createDynamicGroups } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';
import { UseFormReturn } from 'react-hook-form';

import { usePlanDesignIndex } from '../../../../hooks/PlanDesign/usePlanDesignIndex';
import { useIsESIProduct } from '../../../../hooks/useIsESIHook';
import { usePicklistMaps } from '../../../../maps/picklistMaps';
import { productNames } from '../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import {
  ACCUMULATORS_GENERAL_ITEM,
  COMPOUND_ITEM,
  DEDUCTIBLE_ITEM,
  DISPENSE_ITEM,
  GENERAL_ITEM,
  MAXIMUM_OUT_OF_POCKET_ITEM,
  OTHER_CAP_ITEM,
  STANDARD_ITEM,
  XML_ITEM,
} from '../../../ChangeRequestIntake/Navigation/navigationConstants';
import { getAllPlanDesigns } from './FieldGroup/allPlanDesigns';
import { getCompoundsFeilds } from './FieldGroup/compounds';
import { getDeductiblePAFields } from './FieldGroup/deductiblePAFieldData';
import { getDispenseAsWrittenFeilds } from './FieldGroup/dispenseAsWritten';
import { getGeneralFields } from './FieldGroup/generalFieldData';
import { getGeneralPAFields } from './FieldGroup/generalPAFieldData';
import { getMoopPAFields } from './FieldGroup/moopPAFieldData';
import { getOtherPAFields } from './FieldGroup/otherPAFieldData';
import { getStandardTierFields } from './FieldGroup/standard';
import { getUnbreakablePackageTierFields } from './FieldGroup/unbreakablePackage';
import { getXmlFields } from './FieldGroup/xmlFieldData';

interface GroupConfig {
  subtitle: string;
  fields: any[];
  columns: number;
  handleSubmit: any;
  formMethods: any;
  editable: boolean;
  tab: string;
}

export const usePlanDesignGroups = (formMethods: UseFormReturn<any>) => {
  const { handleSubmit, register, watch } = formMethods;
  const formData = watch();
  const isESI = useIsESIProduct(formMethods);

  // Get current plan design index from URL (default 0) for Plan Designs
  const { planDesignIndex } = usePlanDesignIndex();

  const productName = watch('plan.product.name');

  const planDesignGroups: Record<string, GroupConfig> = {};

  const { useApiQuery } = useBenAdmin();
  const { picklist: dawPicklist } = useApiQuery([
    {
      key: 'picklist',
      queryParams: { names: 'DispenseAsWritten' },
      options: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        cacheTime: 10 * 60 * 1000, // 10 minutes
      },
    },
  ]);

  // Helper function to create a standard group
  const createGroup = (
    key: string,
    subtitle: string,
    fields: any[],
    tab: string,
    columns = 2
  ) => {
    if (fields.length > 0) {
      planDesignGroups[key] = {
        subtitle,
        fields,
        columns,
        handleSubmit,
        formMethods,
        editable: true,
        tab,
      };
    }
  };
  const maps = usePicklistMaps();
  // Process dynamic groups (always included regardless of view mode)
  // In intake overview mode, show all groups; otherwise, filter by planDesignIndex
  const generalGroups = getGeneralFields(
    formData,
    planDesignIndex,
    handleSubmit,
    formMethods.register,
    formMethods,
    maps
  );
  const xmlGroups = getXmlFields(
    formData,
    planDesignIndex,
    handleSubmit,
    formMethods.register,
    formMethods,
    maps
  );

  const generalPAGroups = getGeneralPAFields(
    formData,
    planDesignIndex,
    handleSubmit,
    formMethods.register,
    formMethods,
    maps
  );

  const deductiblePAGroups = getDeductiblePAFields(
    formData,
    planDesignIndex,
    handleSubmit,
    formMethods.register,
    formMethods,
    maps
  );

  const moopPAGroups = getMoopPAFields(
    formData,
    planDesignIndex,
    handleSubmit,
    formMethods.register,
    formMethods,
    maps
  );

  const otherPAGroups = getOtherPAFields(
    formData,
    planDesignIndex,
    handleSubmit,
    formMethods.register,
    formMethods,
    maps
  );

  getAllPlanDesigns(formData, handleSubmit, register, formMethods);

  if (generalGroups.length > 0) {
    Object.assign(
      planDesignGroups,
      createDynamicGroups(generalGroups, 'generalGroup', (group) => ({
        ...group,
        subtitle: `${group.subtitle}`,
        tab: GENERAL_ITEM,
      }))
    );
  }

  if (xmlGroups.length > 0 && productName === productNames.ESI_360) {
    Object.assign(
      planDesignGroups,
      createDynamicGroups(xmlGroups, 'xmlGroup', (group) => ({
        ...group,
        subtitle: `${group.subtitle}`,
        tab: XML_ITEM,
      }))
    );
  }

  if (generalPAGroups.length > 0) {
    Object.assign(
      planDesignGroups,
      createDynamicGroups(generalPAGroups, 'generalPAGroup', (group) => ({
        ...group,
        subtitle: `${group.subtitle}`,
        tab: ACCUMULATORS_GENERAL_ITEM,
      }))
    );
  }

  if (deductiblePAGroups.length > 0) {
    Object.assign(
      planDesignGroups,
      createDynamicGroups(deductiblePAGroups, 'deductiblePAGroup', (group) => ({
        ...group,
        subtitle: `${group.subtitle}`,
        tab: DEDUCTIBLE_ITEM,
      }))
    );
  }

  if (moopPAGroups.length > 0) {
    Object.assign(
      planDesignGroups,
      createDynamicGroups(moopPAGroups, 'moopPAGroup', (group) => ({
        ...group,
        subtitle: `${group.subtitle}`,
        tab: MAXIMUM_OUT_OF_POCKET_ITEM,
      }))
    );
  }

  if (otherPAGroups.length > 0) {
    Object.assign(
      planDesignGroups,
      createDynamicGroups(otherPAGroups, 'otherPAGroup', (group) => ({
        ...group,
        subtitle: `${group.subtitle}`,
        tab: OTHER_CAP_ITEM,
      }))
    );
  }

  const standardFields = getStandardTierFields(
    formData,
    planDesignIndex,
    maps,
    isESI
  );
  const unbreakableFields = getUnbreakablePackageTierFields(
    formData,
    planDesignIndex,
    maps,
    isESI
  );
  const compoundsFields = getCompoundsFeilds(formData, planDesignIndex, maps);
  const dispenseAsWrittenFields = getDispenseAsWrittenFeilds(
    formData,
    planDesignIndex,
    dawPicklist,
    maps
  );

  // Combine standard and unbreakable into one Patient Pay group
  const patientPayFields = [...standardFields, ...unbreakableFields];
  createGroup(
    'patientPayGroup',
    'Patient Pay',
    patientPayFields,
    STANDARD_ITEM
  );
  createGroup(
    'compoundsGroup',
    'Patient Pay - Compounds',
    compoundsFields,
    COMPOUND_ITEM
  );
  createGroup(
    'dispenseAsWrittenGroup',
    'Patient Pay - Dispense as Written',
    dispenseAsWrittenFields,
    DISPENSE_ITEM
  );

  return { planDesign: planDesignGroups };
};
