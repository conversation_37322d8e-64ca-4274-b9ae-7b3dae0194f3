import {
  OrganizationDetails,
  PlanDesignDetail,
} from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldGroup } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';
import { z } from 'zod';

import { productNames } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/productNameConstants';
import {
  phoneValidation,
  yesNoValidation,
} from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/validations';
import { getGeneralPaths } from '../Config/generalConfig';
import { getXmlPaths } from '../Config/xmlConfig';

export function getGeneralFields(
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  handleSubmit: UseFormHandleSubmit<any>,
  register: UseFormRegister<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
): TemplateFieldGroup[] {
  // Early return if no plan designs available
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  // Get the plan design corresponding to the selected index
  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) {
    return [];
  }

  const planDesignDetails = planDesign?.plan_design_details || [];
  const planDesignConfig = getGeneralPaths(selectedIndex);

  const xmlPlanDesignConfig = getXmlPaths(selectedIndex);

  const productName = planData?.plan?.product?.name;

  // Map through each detail and create field configurations
  return planDesignDetails
    .map((designDetail: PlanDesignDetail) => {
      const esiDetail = designDetail?.plan_design_detail_esi?.[0];

      // Skip if no design detail
      if (!designDetail || !esiDetail) {
        return null;
      }

      // Return the field configurations for this detail
      return {
        subtitle: `General Information - ${planDesign?.name || ''}`,
        columns: 2,
        editable: true,
        handleSubmit,
        formMethods,
        register,
        fields: [
          {
            label: 'Benefit Name',
            value: planDesign?.name,
            name: planDesignConfig.name,
            type: 'input' as const,
            placeholder: 'Benefit Name',
            isRequired: true,
            validations: z
              .string()
              .max(255, 'Benefit Name must be less than 255 characters'),
          },
          {
            label: 'Benefit Order',
            value: planDesign.sort_seq,
            name: planDesignConfig.sort_seq,
            type: 'input' as const,
            placeholder: 'Benefit Order',
            infoText:
              'The order in which benefits will appear on the PDX (1,2,3, etc).',
            validations: z
              .number()
              .int('Must be an integer')
              .min(-32768, 'Value must be at least -32,768')
              .max(32767, 'Value must be at most 32,767'),
          },
          {
            label: 'Benefit Plan Type',
            value: planDesign.type_ind,
            name: planDesignConfig.type_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.planDesignTypeMap,
            isRequired: true,
            infoText: 'Denotes whether the benefit is a copay plan or HDHP.',
          },
          {
            label: 'Plan Design Benefit Period',
            value: designDetail.benefit_period_ind,
            name: planDesignConfig.benefit_period_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.benefitPeriodsMap,
            isRequired: true,
          },
          ...([
            productNames.ESI_360,
            productNames.CMK_DIRECT,
            productNames.ESI_DIRECT,
          ].includes(productName as productNames)
            ? [
                {
                  label: 'ESI BPLID',
                  value: designDetail.esi_bplid,
                  name: planDesignConfig.esi_bplid,
                  type: 'input' as const,
                  placeholder: 'ESI BPLID',
                  validations: z.string(),
                  isDisabled: true,
                },
              ]
            : []),
          {
            label: 'Effective Date',
            value: designDetail.effective_date,
            name: planDesignConfig.effective_date,
            type: 'datepicker' as const,
            validations: z.date(),
          },
          {
            label: 'End Date',
            value: designDetail.expiration_date,
            name: planDesignConfig.expiration_date,
            type: 'datepicker' as const,
            validations: z.date().optional(),
          },
          ...([
            productNames.CMK_360,
            productNames.ESI_360,
            productNames.OPT_360,
            productNames.IRX_360,
            productNames.CMK_DIRECT,
            productNames.ESI_DIRECT,
          ].includes(productName as productNames)
            ? [
                {
                  label: 'Qualified HDHP',
                  value: designDetail.qualified_hdhp_ind,
                  name: planDesignConfig.qualified_hdhp_ind,
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.yesNoMap,
                  infoText:
                    'Does the plan meet current IRS guidelines to be a qualified HDHP?',
                },
                {
                  label: 'Union Benefit',
                  value: designDetail.union_benefit_ind,
                  name: planDesignConfig.union_benefit_ind,
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.yesNoMap,
                },
                {
                  label: 'Healthcare Reform Status',
                  value: designDetail.healthcare_reform_status,
                  name: planDesignConfig.healthcare_reform_status,
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.healthcareReformStatusMap,
                  infoText:
                    'Indicates whether or not the plan is subject to ACA requirements.',
                },
                {
                  label: 'Overview Notes',
                  value: designDetail.overview_notes,
                  name: planDesignConfig.overview_notes,
                  type: 'textarea' as const,
                  placeholder: 'Overview Notes',
                  validations: z
                    .string()
                    .max(
                      2000,
                      'Overview Notes must be less than 2000 characters'
                    )
                    .optional(),
                  rows: 5,
                  customProps: {
                    minHeight: '120px',
                    overflow: 'hidden',
                  },
                },
                {
                  label: 'Diabetic Meds and Supplies Copays',
                  value: designDetail.diabetic_supplies_copay_setup_ind,
                  name: planDesignConfig.diabetic_supplies_copay_setup_ind,
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.diabeticSuppliesCopayMap,
                },
                {
                  label: 'Copays for Pre-Packaged Drugs',
                  value: designDetail.pre_packaged_copays_ind,
                  name: planDesignConfig.pre_packaged_copays_ind,
                  type: 'dropdownSelect' as const,
                  optionsMap: maps.prepackagedCopaysMap,
                  infoText:
                    'Copay amount for drugs dispensed in 90 Day, unbreakable packaging.',
                },
                {
                  label: 'Diabetic Meds and Supplies Description',
                  value: designDetail.diabetic_supplies_description,
                  name: planDesignConfig.diabetic_supplies_description,
                  type: 'textarea' as const,
                  placeholder: 'Description for diabetic meds and supplies',
                  validations: z.string().optional(),
                  rows: 5,
                  customProps: {
                    minHeight: '120px',
                    overflow: 'hidden',
                  },
                },
                {
                  label: 'Drug Specific Copay Note',
                  value: designDetail.drug_specific_copay_note,
                  name: planDesignConfig.drug_specific_copay_note,
                  type: 'input' as const,
                  placeholder: 'Drug Specific Copay Note',
                  validations: z.string().optional(),
                },
              ]
            : []),
          {
            label: 'Does Deductible Apply to Maximum Out of Pocket?',
            value: esiDetail.deductible_apply_to_moop_ind,
            name: xmlPlanDesignConfig.deductible_apply_to_moop_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.yesNoMap,
            validations: yesNoValidation,
          },
          {
            label: 'Accumulation Period',
            value: esiDetail.deductible_moop_accum_period_ind,
            name: xmlPlanDesignConfig.deductible_moop_accum_period_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.esiAccumPeriodMap,
            validations: z.string().optional(),
          },
          {
            label: 'Accumulator Start Date',
            value: esiDetail.accumulator_start_date,
            name: xmlPlanDesignConfig.accumulator_start_date,
            type: 'datepicker' as const,
            validations: z
              .date({
                invalid_type_error: 'End Date must be a valid date',
              })
              .optional()
              .nullable(),
          },
          {
            label: 'Compound Management Program',
            value: designDetail.compound_management_program_ind,
            name: planDesignConfig.compound_management_program_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.compoundManagementProgramMap,
          },
          {
            label: 'Non Compound Phone Number',
            value: esiDetail.non_compound_phone_number,
            name: xmlPlanDesignConfig.non_compound_phone_number,
            type: 'input' as const,
            placeholder: 'Non Compound Phone Number',
            infoText: 'Enter Non Compound Phone Number',
            validations: phoneValidation,
          },
          {
            label: 'Compound Phone Number',
            value: esiDetail.compound_phone_number,
            name: xmlPlanDesignConfig.compound_phone_number,
            type: 'input' as const,
            placeholder: 'Compound Phone Number',
            infoText: 'Enter Compound Phone Number',
            validations: phoneValidation,
          },
        ],
      };
    })
    .filter(Boolean) as TemplateFieldGroup[];
}
