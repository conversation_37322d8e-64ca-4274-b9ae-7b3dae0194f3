import {
  generatePaths,
  getBasePath,
  replacePlaceholder,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { PLAN_DESIGN_DETAILS_BASE_PATH } from './coreConfig';

// Base path for Deductible PA plan design details with placeholder "{i}"
export const PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH = `${PLAN_DESIGN_DETAILS_BASE_PATH}.accumulation_deductible.{i}`;

// Field keys
export const DEDUCTIBLE_PA_FIELDS = {
  priming_balances_ind: 'priming_balances_ind',
  apply_brand_only_ind: 'apply_brand_only_ind',
  apply_specialty_only_ind: 'apply_specialty_only_ind',
  apply_ind: 'apply_ind',
  integrated_ind: 'integrated_ind',
  embedded_ind: 'embedded_ind',
  notes: 'notes',
  apply_retail_mail_paper_ind: 'apply_retail_mail_paper_ind',
  pbc_order: 'pbc_order',
  accums_tier_name: 'accums_tier_name',
  effective_date: 'effective_date',
  expiration_date: 'expiration_date',
  carryover_phase_ind: 'carryover_phase_ind',
  describe_carryover_phase: 'describe_carryover_phase',
  benefit_period_length_ind: 'benefit_period_length_ind',
  benefit_period_length_other: 'benefit_period_length_other',
  specify_accum_period_ind: 'specify_accum_period_ind',
  family_plan_amount: 'family_plan_amount',
  individual_within_family_amount: 'individual_within_family_amount',
  copay_apply_during_deductible_phase_ind:
    'copay_apply_during_deductible_phase_ind',
  shared_ind: 'shared_ind',
  drug_type_status_ind: 'drug_type_status_ind',
  formulary_status_ind: 'formulary_status_ind',
  network_status_ind: 'network_status_ind',
  network_applicability_ind: 'network_applicability_ind',
  pharmacy_channel_ind: 'pharmacy_channel_ind',
  include_drug_list_ind: 'include_drug_list_ind',
  exclude_drug_list_ind: 'exclude_drug_list_ind',
  individual_plan_amount: 'individual_plan_amount',
  employee_1_dep_amount: 'employee_1_dep_amount',
  specialty_deductible_amount: 'specialty_deductible_amount',
  apply_brand_ind: 'apply_brand_ind',
  apply_retail_ind: 'apply_retail_ind',
  apply_specialty_ind: 'apply_specialty_ind',
  apply_generic_ind: 'apply_generic_ind',
  apply_mail_ind: 'apply_mail_ind',
  apply_diabetic_ind: 'apply_diabetic_ind',
};

// Type-safe path interface
export interface DeductiblePAPaths extends Record<string, string> {
  priming_balances_ind: string;
  apply_brand_only_ind: string;
  apply_specialty_only_ind: string;
  apply_ind: string;
  integrated_ind: string;
  embedded_ind: string;
  notes: string;
  apply_retail_mail_paper_ind: string;
  pbc_order: string;
  accums_tier_name: string;
  effective_date: string;
  expiration_date: string;
  carryover_phase_ind: string;
  describe_carryover_phase: string;
  benefit_period_length_ind: string;
  benefit_period_length_other: string;
  specify_accum_period_ind: string;
  family_plan_amount: string;
  individual_within_family_amount: string;
  copay_apply_during_deductible_phase_ind: string;
  shared_ind: string;
  drug_type_status_ind: string;
  formulary_status_ind: string;
  network_status_ind: string;
  network_applicability_ind: string;
  pharmacy_channel_ind: string;
  include_drug_list_ind: string;
  exclude_drug_list_ind: string;
  individual_plan_amount: string;
  employee_1_dep_amount: string;
  specialty_deductible_amount: string;
  apply_brand_ind: string;
  apply_retail_ind: string;
  apply_specialty_ind: string;
  apply_generic_ind: string;
  apply_mail_ind: string;
  apply_diabetic_ind: string;
}

const deductiblePAConfig: DeductiblePAPaths = {
  priming_balances_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.priming_balances_ind}`,
  apply_brand_only_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.apply_brand_only_ind}`,
  apply_specialty_only_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.apply_specialty_only_ind}`,
  apply_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.apply_ind}`,
  integrated_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.integrated_ind}`,
  embedded_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.embedded_ind}`,
  notes: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.notes}`,
  apply_retail_mail_paper_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.apply_retail_mail_paper_ind}`,
  pbc_order: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.pbc_order}`,
  accums_tier_name: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.accums_tier_name}`,
  effective_date: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.effective_date}`,
  expiration_date: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.expiration_date}`,
  carryover_phase_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.carryover_phase_ind}`,
  describe_carryover_phase: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.describe_carryover_phase}`,
  benefit_period_length_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.benefit_period_length_ind}`,
  benefit_period_length_other: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.benefit_period_length_other}`,
  specify_accum_period_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.specify_accum_period_ind}`,
  family_plan_amount: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.family_plan_amount}`,
  individual_within_family_amount: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.individual_within_family_amount}`,
  copay_apply_during_deductible_phase_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.copay_apply_during_deductible_phase_ind}`,
  shared_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.shared_ind}`,
  drug_type_status_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.drug_type_status_ind}`,
  formulary_status_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.formulary_status_ind}`,
  network_status_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.network_status_ind}`,
  network_applicability_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.network_applicability_ind}`,
  pharmacy_channel_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.pharmacy_channel_ind}`,
  include_drug_list_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.include_drug_list_ind}`,
  exclude_drug_list_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.exclude_drug_list_ind}`,
  individual_plan_amount: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.individual_plan_amount}`,
  employee_1_dep_amount: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.employee_1_dep_amount}`,
  specialty_deductible_amount: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.specialty_deductible_amount}`,
  apply_brand_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.apply_brand_ind}`,
  apply_retail_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.apply_retail_ind}`,
  apply_specialty_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.apply_specialty_ind}`,
  apply_generic_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.apply_generic_ind}`,
  apply_mail_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.apply_mail_ind}`,
  apply_diabetic_ind: `${PLAN_DESIGN_DEDUCTIBLE_PA_BASE_PATH}.${DEDUCTIBLE_PA_FIELDS.apply_diabetic_ind}`,
};

/**
 * getDeductiblePAPath
 * Returns a single path for the given field at the specified index.
 *
 * @param field - The name of the DeductiblePAPaths field
 * @param index - The index of the DeductiblePAPaths in the array
 */
export function getDeductiblePAPath(
  field: keyof DeductiblePAPaths,
  index: number
): string {
  return replacePlaceholder(deductiblePAConfig[field], '{i}', index.toString());
}

/**
 * getDeductiblePAPaths
 * Returns a getDeductiblePAPaths object where each path has its "{i}" placeholder
 * replaced by the given index.
 *
 * @param index - The index of the DeductiblePAPaths in the array
 */

export function getDeductiblePAPaths(index: number): DeductiblePAPaths {
  return generatePaths(
    deductiblePAConfig as Record<string, string>,
    index
  ) as DeductiblePAPaths;
}

/**
 * Returns the base path to use for add/edit logic.
 */
export function getDedectibleBasePath(
  isNewItem: boolean,
  itemIndex = -1
): string {
  return getBasePath(
    isNewItem,
    `${PLAN_DESIGN_DETAILS_BASE_PATH}[0].accumulation_deductible`,
    itemIndex
  );
}
