import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldConfig } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import { z } from 'zod';

import { DispenseAsWrittenOption } from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/PlanDesign/PatientPay/Dispense/dispenseForm';
import { getDispenseAsWrittenPaths } from '../Config/dispensesWrittenConfig';
import { getXmlPaths } from '../Config/xmlConfig';

export const getDispenseAsWrittenFeilds = (
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  dawPicklist: any,
  maps: Partial<PicklistMaps>
): Partial<TemplateFieldConfig>[] => {
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  // Get the plan design corresponding to the selected index
  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) {
    return [];
  }

  const planDesignConfig = getXmlPaths(selectedIndex);

  const dispenseOptions = (dawPicklist?.DispenseAsWritten || []).reduce(
    (acc: Record<string, string>, item: DispenseAsWrittenOption) => {
      acc[item.value] = item.label;
      return acc;
    },
    {}
  );

  const dispenseAsWrittenConfig = getDispenseAsWrittenPaths(selectedIndex);

  return [
    {
      label: 'Dispense as Written',
      name: dispenseAsWrittenConfig.dispense_as_written_ind,
      value: planDesign.plan_design_details?.[0]?.dispense_as_written_ind,
      optionsMap: dispenseOptions,
      type: 'dropdownSelect' as const,
      isRequired: true,
    },
    {
      label: 'Dispense as Written Description',
      name: dispenseAsWrittenConfig.dispense_as_written_description,
      value:
        planDesign.plan_design_details?.[0]?.dispense_as_written_description,
      type: 'textarea' as const,
      infoText:
        'Which copay structure is used and whether a penalty will apply, based on the Rx DAW code as specified by the prescriber.',
      placeholder: 'Enter Description',
      validations: z.string().max(255, 'Value cannot exceed 255 characters'),
    },
    {
      label: 'Dispense as Written Penalties Apply to Deductible',
      name: planDesignConfig.penalties_apply_to_deductible_ind,
      value:
        planDesign.plan_design_details?.[0]?.plan_design_detail_esi?.[0]
          ?.penalties_apply_to_deductible_ind,
      optionsMap: maps.yesNoViewBenefitMap,
      type: 'dropdownSelect' as const,
    },
    {
      label: 'Dispense as Written Penalties Apply to Maximum Out Of Pocket',
      name: planDesignConfig.penalties_apply_to_moop_ind,
      value:
        planDesign.plan_design_details?.[0]?.plan_design_detail_esi?.[0]
          ?.penalties_apply_to_moop_ind,
      optionsMap: maps.yesNoViewBenefitMap,
      type: 'dropdownSelect' as const,
    },
    {
      label: 'Dispense as Written Penalties Apply After Maximum Out of Pocket',
      name: planDesignConfig.penalties_apply_after_moop_ind,
      value:
        planDesign.plan_design_details?.[0]?.plan_design_detail_esi?.[0]
          ?.penalties_apply_after_moop_ind,
      optionsMap: maps.yesNoViewBenefitMap,
      type: 'dropdownSelect' as const,
    },
  ];
};
