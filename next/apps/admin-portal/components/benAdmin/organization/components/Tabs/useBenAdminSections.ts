import {
  OrganizationDetails,
  useClientProfileSections,
  useClinicalDesignsSections,
  useMemberServicesSections,
  usePlanDesignSections,
  useProductsAndServicesSections,
} from 'apps/admin-portal/components/benAdmin';
import { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

/**
 * Hook to generate all section data needed for BenAdminTabs
 * Uses direct form methods instead of extracting from components
 */
export const useBenAdminSections = (formMethods: UseFormReturn<any>) => {
  const { watch, setValue } = formMethods;

  // Only watch specific fields that are needed for the sections
  const watchedValues = watch(['features', 'plan', 'organization']);

  // Memoize the current values to prevent unnecessary re-renders
  const currentValues = useMemo(() => {
    return watchedValues as Partial<OrganizationDetails>;
  }, [watchedValues]);

  // Generate all sections using the same form methods
  const productsAndServicesSections = useProductsAndServicesSections();

  const planDesignSections = usePlanDesignSections();

  // All sections now use the same formMethods
  const memberExperienceSections = useMemberServicesSections(
    currentValues,
    setValue
  );

  const clinicalDesignSections = useClinicalDesignsSections(currentValues);

  const clientProfileSections = useClientProfileSections();

  // Memoize the return value to prevent unnecessary re-renders
  return useMemo(
    () => ({
      productsAndServicesSections,
      planDesignSections,
      memberExperienceSections,
      clinicalDesignSections,
      clientProfileSections,
      toggleName: 'Differs from Plan Design',
    }),
    [
      productsAndServicesSections,
      planDesignSections,
      memberExperienceSections,
      clinicalDesignSections,
      clientProfileSections,
    ]
  );
};
