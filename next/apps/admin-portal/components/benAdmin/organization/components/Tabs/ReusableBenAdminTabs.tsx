import { Box } from '@chakra-ui/react';
import { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { OrganizationDetails } from '../../../Models/interfaces';
import { useContactsHandler } from '../../hooks/useContactsHandler';
import BenAdminTabs from './BenAdminTabs';
import { useClientInformationFieldGroup } from './PlanDesign/ClientProfile/ClienProfileServiceLayer';
import { clientConfig } from './PlanDesign/ClientProfile/Config/clientConfig';
import { useClinicalDesignFieldGroups } from './PlanDesign/ClinicalDesign/ClinicalDesignServiceLayer';
import { useMemberExperienceFieldGroups } from './PlanDesign/MemberExperience/MemberExperienceServiceLayer';
import { usePlanDesignGroups } from './PlanDesign/PlanDesign/PlanDesignServiceLayer';
import { useProductsAndServicesGroups } from './PlanDesign/ProductsAndServices/ProductsAndServicesServiceLayer';
import { useBenAdminSections } from './useBenAdminSections';

type ReusableBenAdminTabsProps = {
  formMethods: UseFormReturn<any>;
  organizationDetails: OrganizationDetails;
  onUpdateActiveItem?: (section: string, tab?: string) => void;
};

/**
 * A reusable component that encapsulates the BenAdminTabsSection with all necessary data preparation
 * This allows reusing the tabs section without needing to redefine all the props each time
 */
export const ReusableBenAdminTabs = ({
  formMethods,
  organizationDetails,
  onUpdateActiveItem,
}: ReusableBenAdminTabsProps) => {
  // Extract organization information
  const orgName = formMethods.watch(clientConfig?.legal_entity_name);
  const orgId = organizationDetails?.organization?.organization_id;

  // Memoize the orgName to prevent unnecessary re-renders
  const memoizedOrgName = useMemo(() => orgName, [orgName]);

  const planId = organizationDetails?.plan?.plan_id;

  // Memoize planFeatures to prevent unnecessary re-renders
  const planFeatures = useMemo(
    () => ({
      plan: organizationDetails?.plan,
      features: organizationDetails?.features,
      picklists: organizationDetails?.picklists,
    }),
    [
      organizationDetails?.plan,
      organizationDetails?.features,
      organizationDetails?.picklists,
    ]
  );

  const {
    productsAndServicesSections,
    planDesignSections,
    memberExperienceSections,
    clinicalDesignSections,
    clientProfileSections,
    toggleName,
  } = useBenAdminSections(formMethods);

  // Prepare all the field groups
  const { clientProfile } = useClientInformationFieldGroup(
    memoizedOrgName,
    formMethods
  );

  const { memberExperience } = useMemberExperienceFieldGroups(formMethods);

  const { productsAndServices } = useProductsAndServicesGroups(formMethods);

  const { planDesign } = usePlanDesignGroups(formMethods);

  const { externalContacts, internalContacts } = useContactsHandler(
    orgId,
    formMethods
  );

  const { clinicalDesign } = useClinicalDesignFieldGroups(formMethods);

  // Memoize benAdminSections object to prevent unnecessary re-renders
  const benAdminSections = useMemo(
    () => ({
      productsAndServicesSections,
      planDesignSections,
      memberExperienceSections,
      clinicalDesignSections,
      clientProfileSections,
      toggleName,
    }),
    [
      productsAndServicesSections,
      planDesignSections,
      memberExperienceSections,
      clinicalDesignSections,
      clientProfileSections,
      toggleName,
    ]
  );

  return (
    <Box position="relative">
      <BenAdminTabs
        formMethods={formMethods}
        clientProfile={clientProfile}
        clinicalDesign={clinicalDesign}
        memberExperience={memberExperience}
        planDesign={planDesign}
        productsAndServices={productsAndServices}
        internalContacts={internalContacts}
        externalContacts={externalContacts}
        planId={planId}
        planFeatures={planFeatures}
        onUpdateActiveItem={onUpdateActiveItem}
        // Pass the sections and form methods to BenAdminTabs
        benAdminSections={benAdminSections}
      />
    </Box>
  );
};
