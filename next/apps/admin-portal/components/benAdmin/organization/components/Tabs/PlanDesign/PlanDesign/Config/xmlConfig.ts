import {
  generatePaths,
  replacePlaceholder,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { PLAN_DESIGN_DETAILS_BASE_PATH } from './coreConfig';

// Base path for ESI plan design details
export const PLAN_DESIGN_ESI_BASE_PATH = `${PLAN_DESIGN_DETAILS_BASE_PATH}.plan_design_detail_esi[0]`;

// Define field names relative to the ESI base path
export const XML_FIELDS = {
  shared_vendor_id: 'shared_vendor_id',
  network_applicability_ind: 'network_applicability_ind',
  vendor_policy_number: 'vendor_policy_number',
  spending_account_type_ind: 'spending_account_type_ind',
  xml_accums_complete_ind: 'xml_accums_complete_ind',
  hra_members_access_ind: 'hra_members_access_ind',
  hsa_admin_phone: 'hsa_admin_phone',
  hsa_medical_phone: 'hsa_medical_phone',
  claim_submission_type_ind: 'claim_submission_type_ind',
  insulin_method_ind: 'insulin_method_ind',
  exact_copy_ind: 'exact_copy_ind',
  source_entity: 'source_entity',
  deductible_apply_to_moop_ind: 'deductible_apply_to_moop_ind',
  deductible_moop_accum_period_ind: 'deductible_moop_accum_period_ind',
  accumulator_start_date: 'accumulator_start_date',
  non_compound_phone_number: 'non_compound_phone_number',
  compound_phone_number: 'compound_phone_number',
  penalties_apply_to_deductible_ind: 'penalties_apply_to_deductible_ind',
  penalties_apply_to_moop_ind: 'penalties_apply_to_moop_ind',
  penalties_apply_after_moop_ind: 'penalties_apply_after_moop_ind',
};

/**
 * Type definition for the xml paths.
 */
export interface XmlPaths extends Record<string, string> {
  shared_vendor_id: string;
  network_applicability_ind: string;
  vendor_policy_number: string;
  spending_account_type_ind: string;
  xml_accums_complete_ind: string;
  hra_members_access_ind: string;
  hsa_admin_phone: string;
  hsa_medical_phone: string;
  exact_copy_ind: string;
  source_entity: string;
  deductible_apply_to_moop_ind: string;
  deductible_moop_accum_period_ind: string;
  accumulator_start_date: string;
  non_compound_phone_number: string;
  compound_phone_number: string;
  penalties_apply_to_deductible_ind: string;
  penalties_apply_to_moop_ind: string;
  penalties_apply_after_moop_ind: string;
}

const xmlConfig: XmlPaths = {
  shared_vendor_id: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.shared_vendor_id}`,
  network_applicability_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.network_applicability_ind}`,
  vendor_policy_number: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.vendor_policy_number}`,
  spending_account_type_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.spending_account_type_ind}`,
  xml_accums_complete_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.xml_accums_complete_ind}`,
  hra_members_access_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.hra_members_access_ind}`,
  hsa_admin_phone: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.hsa_admin_phone}`,
  hsa_medical_phone: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.hsa_medical_phone}`,
  claim_submission_type_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.claim_submission_type_ind}`,
  insulin_method_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.insulin_method_ind}`,
  exact_copy_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.exact_copy_ind}`,
  source_entity: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.source_entity}`,
  deductible_apply_to_moop_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.deductible_apply_to_moop_ind}`,
  deductible_moop_accum_period_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.deductible_moop_accum_period_ind}`,
  accumulator_start_date: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.accumulator_start_date}`,
  non_compound_phone_number: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.non_compound_phone_number}`,
  compound_phone_number: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.compound_phone_number}`,
  penalties_apply_to_deductible_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.penalties_apply_to_deductible_ind}`,
  penalties_apply_to_moop_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.penalties_apply_to_moop_ind}`,
  penalties_apply_after_moop_ind: `${PLAN_DESIGN_ESI_BASE_PATH}.${XML_FIELDS.penalties_apply_after_moop_ind}`,
};

export function getXmlPath(field: keyof XmlPaths, index: number): string {
  return replacePlaceholder(xmlConfig[field], index);
}

export function getXmlPaths(index: number): XmlPaths {
  return generatePaths(xmlConfig as Record<string, string>, index) as XmlPaths;
}
