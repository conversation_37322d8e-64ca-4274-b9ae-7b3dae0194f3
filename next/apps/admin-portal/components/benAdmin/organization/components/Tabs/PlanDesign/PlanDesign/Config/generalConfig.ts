import {
  generatePaths,
  replacePlaceholder,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import {
  PLAN_DESIGN_BASE_PATH,
  PLAN_DESIGN_DETAILS_BASE_PATH,
} from './coreConfig';

// Define field names relative to their base paths
export const GENERAL_FIELDS = {
  // Fields at the plan design level
  name: 'name',
  sort_seq: 'sort_seq',
  type_ind: 'type_ind',

  // Fields at the plan design details level
  benefit_period_ind: 'benefit_period_ind',
  esi_bplid: 'esi_bplid',
  effective_date: 'effective_date',
  expiration_date: 'expiration_date',
  qualified_hdhp_ind: 'qualified_hdhp_ind',
  union_benefit_ind: 'union_benefit_ind',
  healthcare_reform_status: 'healthcare_reform_status',
  non_grandfathered_date: 'non_grandfathered_date',
  overview_notes: 'overview_notes',
  diabetic_supplies_copay_setup_ind: 'diabetic_supplies_copay_setup_ind',
  pre_packaged_copays_ind: 'pre_packaged_copays_ind',
  diabetic_supplies_description: 'diabetic_supplies_description',
  drug_specific_copay_note: 'drug_specific_copay_note',
  compound_management_program_ind: 'compound_management_program_ind',
};

/**
 * Type definition for the general paths.
 */
export interface GeneralPaths extends Record<string, string> {
  name: string;
  sort_seq: string;
  type_ind: string;
  benefit_period_ind: string;
  esi_bplid: string;
  effective_date: string;
  expiration_date: string;
  qualified_hdhp_ind: string;
  union_benefit_ind: string;
  healthcare_reform_status: string;
  non_grandfathered_date: string;
  overview_notes: string;
  diabetic_supplies_copay_setup_ind: string;
  pre_packaged_copays_ind: string;
  diabetic_supplies_description: string;
  drug_specific_copay_note: string;
  compound_management_program_ind: string;
}

/**
 * Base general config with a placeholder "{i}" in each path.
 */
const generalConfig: GeneralPaths = {
  // Plan design level fields
  name: `${PLAN_DESIGN_BASE_PATH}.${GENERAL_FIELDS.name}`,
  sort_seq: `${PLAN_DESIGN_BASE_PATH}.${GENERAL_FIELDS.sort_seq}`,
  type_ind: `${PLAN_DESIGN_BASE_PATH}.${GENERAL_FIELDS.type_ind}`,

  // Plan design details level fields
  benefit_period_ind: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.benefit_period_ind}`,
  esi_bplid: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.esi_bplid}`,
  effective_date: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.effective_date}`,
  expiration_date: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.expiration_date}`,
  qualified_hdhp_ind: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.qualified_hdhp_ind}`,
  union_benefit_ind: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.union_benefit_ind}`,
  healthcare_reform_status: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.healthcare_reform_status}`,
  non_grandfathered_date: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.non_grandfathered_date}`,
  overview_notes: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.overview_notes}`,
  diabetic_supplies_copay_setup_ind: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.diabetic_supplies_copay_setup_ind}`,
  pre_packaged_copays_ind: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.pre_packaged_copays_ind}`,
  diabetic_supplies_description: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.diabetic_supplies_description}`,
  drug_specific_copay_note: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.drug_specific_copay_note}`,
  compound_management_program_ind: `${PLAN_DESIGN_DETAILS_BASE_PATH}.${GENERAL_FIELDS.compound_management_program_ind}`,
};

/**
 * getGeneralPath
 * Returns a single path for the given field at the specified index.
 *
 * @param field - The name of the general field
 * @param index - The index of the general in the array
 */
export function getGeneralPath(
  field: keyof GeneralPaths,
  index: number
): string {
  return replacePlaceholder(generalConfig[field], index);
}

/**
 * getGeneralPaths
 * Returns a GeneralPaths object where each path has its "{i}" placeholder
 * replaced by the given index.
 *
 * @param index - The index of the General in the array
 */
export function getGeneralPaths(index: number): GeneralPaths {
  return generatePaths(
    generalConfig as Record<string, string>,
    index
  ) as GeneralPaths;
}
