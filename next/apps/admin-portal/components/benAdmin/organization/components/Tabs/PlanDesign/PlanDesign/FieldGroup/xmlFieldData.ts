import { OrganizationDetails } from 'apps/admin-portal/components/benAdmin/Models/interfaces';
import { PicklistMaps } from 'apps/admin-portal/components/benAdmin/organization/maps/picklistInterface';
import { TemplateFieldGroup } from 'apps/admin-portal/components/benAdmin/ReusableComponents/Models/types';
import {
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormReturn,
} from 'react-hook-form';
import { z } from 'zod';

import {
  alphaNumericValidation,
  yesNoValidation,
} from '../../../../ChangeRequestIntake/MenuItems/PlanDesignTool/validations';
import { getXmlPaths } from '../Config/xmlConfig';

// Validation schemas
const phoneValidation = z
  .string()
  .regex(/^[+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number')
  .optional();

const smallintValidation = z
  .string()
  .regex(/^[0-9]+$/, 'Please enter a valid number')
  .optional();

export function getXmlFields(
  planData: Partial<OrganizationDetails>,
  selectedIndex: number,
  handleSubmit: UseFormHandleSubmit<any>,
  register: UseFormRegister<any>,
  formMethods: UseFormReturn<any>,
  maps: Partial<PicklistMaps>
): TemplateFieldGroup[] {
  // Early return if no plan designs available
  const planDesigns = Array.isArray(planData?.plan?.plan_designs)
    ? planData.plan.plan_designs
    : [];

  // Get the plan design corresponding to the selected index
  const planDesign = planDesigns[selectedIndex];
  if (!planDesign) {
    return [];
  }

  const planDesignDetails = planDesign?.plan_design_details || [];
  const planDesignConfig = getXmlPaths(selectedIndex);

  // Map through each detail and create field configurations
  return planDesignDetails
    .map((designDetail) => {
      // Get the first ESI detail (if available)
      const esiDetail = designDetail?.plan_design_detail_esi?.[0];

      // Skip plans without ESI details
      if (!esiDetail) {
        return null;
      }

      // Return the field configurations for this detail
      return {
        subtitle: `XML Leave Behind - Info Provided by ESI - ${
          planDesign?.name || ''
        }`,
        columns: 2,
        editable: true,
        handleSubmit,
        formMethods,
        register,
        fields: [
          {
            label: 'Shared Vendor ID',
            value: esiDetail.shared_vendor_id,
            name: planDesignConfig.shared_vendor_id,
            type: 'input',
            placeholder: 'Enter Shared Vendor ID',
          },
          {
            label: 'Vendor Policy Number',
            value: esiDetail.vendor_policy_number,
            name: planDesignConfig.vendor_policy_number,
            type: 'input' as const,
            placeholder: 'Vendor Policy Number',
            infoText: 'Enter Vendor Policy Number',
            validations: z
              .string()
              .max(
                255,
                'Vendor Policy Number must be less than 255 characters'
              ),
          },
          {
            label: 'Spending Account Type',
            value: esiDetail.spending_account_type_ind,
            name: planDesignConfig.spending_account_type_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.spendingAccountTypeMap,
            infoText: 'Choose an option for Spending Account Type',
            validations: smallintValidation,
          },
          {
            label: 'XML Accums Complete',
            value: esiDetail.xml_accums_complete_ind,
            name: planDesignConfig.xml_accums_complete_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.xmlAccumsCompleteMap,
            infoText: 'Choose an option for XML Accums Complete',
            validations: yesNoValidation,
          },
          {
            label: 'HRA Members Access',
            value: esiDetail.hra_members_access_ind,
            name: planDesignConfig.hra_members_access_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.hraMembersAccessMap,
            infoText: 'Choose an option for HRA Member Access',
            validations: smallintValidation,
          },
          {
            label: 'HSA Admin Phone Number',
            value: esiDetail.hsa_admin_phone,
            name: planDesignConfig.hsa_admin_phone,
            type: 'input' as const,
            placeholder: 'HSA Admin Phone Number',
            infoText: 'Enter HSA Admin Phone Number',
            validations: phoneValidation,
          },
          {
            label: 'HSA Medical Phone Number',
            value: esiDetail.hsa_medical_phone,
            name: planDesignConfig.hsa_medical_phone,
            type: 'input' as const,
            placeholder: 'HSA Medical Phone Number',
            infoText: 'Enter HSA Medical Phone Number',
            validations: phoneValidation,
          },
          {
            label: 'Claims Submission Type',
            value: esiDetail.claim_submission_type_ind,
            name: planDesignConfig.claim_submission_type_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.esiClaimSubmissionTypeMap,
            infoText: 'Select Claims Submission Type',
            validations: smallintValidation,
          },
          {
            label: 'Insulin Method Retail and Mail',
            value: esiDetail.insulin_method_ind,
            name: planDesignConfig.insulin_method_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.esiInsulinMethodMap,
            infoText: 'Select Insulin Method Retail and Mail',
            validations: z.string().optional(),
          },
          {
            label: 'Exact Copy',
            value: esiDetail?.exact_copy_ind,
            name: planDesignConfig.exact_copy_ind,
            type: 'dropdownSelect' as const,
            optionsMap: maps.yesNoMap,
            validations: yesNoValidation,
          },
          {
            label: 'Source Entity',
            value: esiDetail.source_entity,
            name: planDesignConfig.source_entity,
            type: 'input' as const,
            placeholder: 'Source Entity',
            infoText: 'Enter Source Entity',
            validations: alphaNumericValidation,
          },
        ],
      };
    })
    .filter(Boolean) as TemplateFieldGroup[]; // Remove null entries
}
