import {
  generatePaths,
  getBasePath,
  replacePlaceholder,
} from 'apps/admin-portal/components/benAdmin/ReusableComponents/Components/Form/components/hooks/formHelpers';

import { PLAN_DESIGN_DETAILS_BASE_PATH } from './coreConfig';

// Use a placeholder in the base path for index substitution
export const PLAN_DESIGN_MOOP_PA_BASE_PATH = `${PLAN_DESIGN_DETAILS_BASE_PATH}.accumulation_moop.{j}`;

// Field definitions relative to base
export const MOOP_PA_FIELDS = {
  apply_ind: 'apply_ind',
  integrated_ind: 'integrated_ind',
  embedded_ind: 'embedded_ind',
  notes: 'notes',
  apply_retail_mail_paper_ind: 'apply_retail_mail_paper_ind',
  pbc_order: 'pbc_order',
  accums_tier_name: 'accums_tier_name',
  effective_date: 'effective_date',
  expiration_date: 'expiration_date',
  carryover_phase_ind: 'carryover_phase_ind',
  describe_carryover_phase: 'describe_carryover_phase',
  benefit_period_length_ind: 'benefit_period_length_ind',
  benefit_period_length_other: 'benefit_period_length_other',
  specify_accum_period_ind: 'specify_accum_period_ind',
  priming_balances_ind: 'priming_balances_ind',
  individual_plan_amount: 'individual_plan_amount',
  employee_1_dep_amount: 'employee_1_dep_amount',
  family_plan_amount: 'family_plan_amount',
  individual_within_family_amount: 'individual_within_family_amount',
  shared_ind: 'shared_ind',
  drug_type_status_ind: 'drug_type_status_ind',
  formulary_status_ind: 'formulary_status_ind',
  network_status_ind: 'network_status_ind',
  network_applicability_ind: 'network_applicability_ind',
  pharmacy_channel_ind: 'pharmacy_channel_ind',
  include_drug_list_ind: 'include_drug_list_ind',
  exclude_drug_list_ind: 'exclude_drug_list_ind',
  specialty_moop_amount: 'specialty_moop_amount',
};

export interface MoopPAPaths extends Record<string, string> {
  apply_ind: string;
  integrated_ind: string;
  embedded_ind: string;
  notes: string;
  apply_retail_mail_paper_ind: string;
  pbc_order: string;
  accums_tier_name: string;
  effective_date: string;
  expiration_date: string;
  carryover_phase_ind: string;
  describe_carryover_phase: string;
  benefit_period_length_ind: string;
  benefit_period_length_other: string;
  specify_accum_period_ind: string;
  priming_balances_ind: string;
  individual_plan_amount: string;
  employee_1_dep_amount: string;
  family_plan_amount: string;
  individual_within_family_amount: string;
  shared_ind: string;
  drug_type_status_ind: string;
  formulary_status_ind: string;
  network_status_ind: string;
  network_applicability_ind: string;
  pharmacy_channel_ind: string;
  include_drug_list_ind: string;
  exclude_drug_list_ind: string;
  specialty_moop_amount: string;
}

// Use placeholder "{j}" in path definitions
const moopPAConfig: MoopPAPaths = {
  apply_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.apply_ind}`,
  integrated_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.integrated_ind}`,
  embedded_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.embedded_ind}`,
  notes: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.notes}`,
  apply_retail_mail_paper_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.apply_retail_mail_paper_ind}`,
  pbc_order: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.pbc_order}`,
  accums_tier_name: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.accums_tier_name}`,
  effective_date: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.effective_date}`,
  expiration_date: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.expiration_date}`,
  carryover_phase_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.carryover_phase_ind}`,
  describe_carryover_phase: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.describe_carryover_phase}`,
  benefit_period_length_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.benefit_period_length_ind}`,
  benefit_period_length_other: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.benefit_period_length_other}`,
  specify_accum_period_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.specify_accum_period_ind}`,
  priming_balances_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.priming_balances_ind}`,
  individual_plan_amount: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.individual_plan_amount}`,
  employee_1_dep_amount: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.employee_1_dep_amount}`,
  family_plan_amount: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.family_plan_amount}`,
  individual_within_family_amount: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.individual_within_family_amount}`,
  shared_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.shared_ind}`,
  drug_type_status_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.drug_type_status_ind}`,
  formulary_status_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.formulary_status_ind}`,
  network_status_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.network_status_ind}`,
  network_applicability_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.network_applicability_ind}`,
  pharmacy_channel_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.pharmacy_channel_ind}`,
  include_drug_list_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.include_drug_list_ind}`,
  exclude_drug_list_ind: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.exclude_drug_list_ind}`,
  specialty_moop_amount: `${PLAN_DESIGN_MOOP_PA_BASE_PATH}.${MOOP_PA_FIELDS.specialty_moop_amount}`,
};

/**
 * getMOOPPAPath
 * Returns a single path for the given field at the specified index.
 *
 * @param field - The name of the MOOPPA field
 * @param index - The index of the MOOPPA in the array
 */
export function getMoopPAPath(field: keyof MoopPAPaths, index: number): string {
  return replacePlaceholder(moopPAConfig[field], '{j}', index.toString());
}

/**
 * getMoopPAPaths
 * Returns a getMoopPAPaths object where each path has its "{i}" placeholder
 * replaced by the given index.
 *
 * @param index - The index of the MOOPPA in the array
 */
export function getMoopPAPaths(index: number): MoopPAPaths {
  return generatePaths(
    moopPAConfig as Record<string, string>,
    index
  ) as MoopPAPaths;
}

/**
 * Get the root base path of a specific item index for dynamic forms.
 */
export function getMoopPABasePath(isNewItem: boolean, itemIndex = -1): string {
  return getBasePath(
    isNewItem,
    `${PLAN_DESIGN_DETAILS_BASE_PATH}[0].accumulation_moop`,
    itemIndex
  );
}
