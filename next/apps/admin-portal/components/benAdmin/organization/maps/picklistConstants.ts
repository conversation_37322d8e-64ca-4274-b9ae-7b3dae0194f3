/**
 * Picklist constants for use throughout the application
 * This file contains all the picklist names as constants to avoid string literals
 */

/**
 * Interface for picklist data structures
 */
export interface PicklistOption {
  value: string;
  label: string;
}

/**
 * Type for picklist groups - used for type safety in utility functions
 */
export type PicklistGroup = Record<string, string>;

// General Picklists
export const PICKLISTS = {
  // Common Indicators
  EXCLUDE: 'Exclude',
  STUDENT_AGE_INDICATOR: 'StudentAgeIndicator',
  DEPENDENT_AGE_INDICATOR: 'DependentAgeIndicator',
  YES_NO: 'YesNo',
  YES_NO_NA: 'YesNoNa',
  YES_NO_ESI: 'YesNoEsi',
  YES_OTHER_ADD_NOTE: 'YesOtherAddNote',
  YES_NO_VIEW_BENEFIT: 'YesNoViewBenefit',
  ACTIVE: 'Active',
  ACTIVE_INACTIVE: 'ActiveInactive',
  COVER_EXCLUDE: 'CoverExclude',
  SUCCESS_FAIL: 'SuccessFail',

  // Pharmacy Related
  PHARMACY_BILLING: 'PharmacyBilling',
  PHARMACY_PRICING: 'PharmacyPricing',
  PHARMACY_STATUS: 'PharmacyStatus',
  PHARMACY_OWNERSHIP: 'PharmacyOwnership',
  PHARMACY_CHANNEL: 'PharmacyChannel',
  PHARMACY_CHANNEL_ACCUM: 'PharmacyChannelAccum',
  PAPER_CLAIMS_PRICING: 'PaperClaimsPricing',
  PREPACKAGED_COPAY: 'PrePackagedCopay',
  PREPACKAGED_COPAYS: 'PrepackagedCopays',
  DIABETIC_SUPPLIES_COPAY: 'DiabeticSuppliesCopay',
  SPECIALTY_ARRANGEMENT: 'SpecialtyArrangement',
  DISPENSE_AS_WRITTEN: 'DispenseAsWritten',
  COMPOUND_COPAY: 'CompoundCopay',
  NETWORK_STATUS: 'NetworkStatus',

  // Cost Share Related
  COST_SHARE_TIER: 'CostShareTier',
  COST_SHARE_TYPE: 'CostShareType',
  COST_SHARE_TIER_DAYS_SUPPLY: 'CostShareTierDaysSupply',
  CO_INSURANCE_LESSER_GREATER: 'CoInsuranceLesserGreater',

  // Business/Industry Related
  LINE_OF_BUSINESS: 'LineOfBusiness',
  INDUSTRY: 'Industry',
  INDUSTRY_VERTICAL: 'IndustryVertical',
  TERMINATION_REASON: 'TerminationReason',
  LEGAL_ENTITY_TYPE: 'LegalEntityType',
  STATE: 'State',
  ERISA_STATUS: 'ErisaStatus',

  // ID Card Related
  ID_CARDS_RESPONSIBLE: 'IdCardsResponsible',
  ID_CARD_TYPE: 'IdCardType',
  EMPLOYEE_ID_SOURCE: 'EmployeeIdSource',
  ID_CARD_MAILING: 'IdCardMailing',
  MEMBER_PACKETS: 'MemberPackets',
  GRANDFATHER_TIME_FRAME: 'GrandfatherPaTimeframe',

  // Healthcare Related
  HEALTHCARE_REFORM_STATUS: 'HealthcareReformStatus',
  BENEFIT_PERIODS: 'BenefitPeriods',
  NETWORK_APPLICABILITY: 'NetworkApplicability',
  SPENDING_ACCOUNT_TYPE: 'SpendingAccountType',
  HRA_MEMBERS_ACCESS: 'HraMembersAccess',
  PLAN_DESIGN_TYPE: 'PlanDesignType',
  ACCUM_TRANSFER: 'AccumTransfer',
  COB_ALLOWED: 'CobAllowed',
  MEDICAID_SUBROGATION: 'MedicaidSubrogation',
  INTEGRATED: 'Integrated',
  EMBEDDED: 'Embedded',
  CARRYOVER_PHASE: 'CarryoverPhase',
  PENALTIES_APPLY: 'PenaltiesApply',
  BENEFIT_PERIOD_LENGTH: 'BenefitPeriodLength',
  ACCUM_DRUG_LIST: 'AccumDrugList',

  // Document Related
  DOCUMENT_INTERNAL: 'DocumentInternal',
  IMPLEMENTATION_TIMELINE: 'ImplementationTimeline',

  // Expression/Validation Related
  EXPRESSION_CONTEXT: 'ExpressionContext',
  EXPRESSION_ENTITY_SCOPE: 'ExpressionEntityScope',
  PLAN_VALIDATION_RULE_SCOPE: 'PlanValidationRuleScope',
  PLAN_VALIDATION_RULE_SEVERITY: 'PlanValidationRuleSeverity',

  // Drug and Formulary Related
  DRUG_TYPE_STATUS: 'DrugTypeStatus',
  FORMULARY_STATUS: 'FormularyStatus',
  FOREIGN_CLAIMS: 'ForeignClaims',

  // Medical Integration
  MEDICAL_INTEGRATION_TIER: 'MedicalIntegrationTier',
  MEDICAL_VENDOR: 'MedicalVendor',

  // System/Internal
  CDH_CLASS_CODE: 'CdhClassCode',
  SHARED_INDICATOR: 'SharedIndicator',

  // Other
  MONTHS: 'Months',
  OTHER_ACCUM_TYPE: 'OtherAccumType',
  PLAN_CLASS: 'PlanClass',
  PRIOR_AUTHORIZATION_REVIEWER: 'PriorAuthorizationReviewer',
  CARRIER_TO_CARRIER: 'CarrierToCarrier',
  TRANSITION_FILE: 'TransitionFile',
  HISTORICAL_CLAIMS: 'HistoricalClaims',
  COMPOUND_MANAGEMENT_PROGRAM: 'CompoundManagementProgram',
} as const;

// ESI Specific Picklists
export const ESI_PICKLISTS = {
  RETAIL_PHARMACY_NETWORK: 'ESI-RetailPharmacyNetwork',
  MAINTENANCE_PHARMACY_NETWORK: 'ESI-MaintenancePharmacyNetwork',
  SPECIALTY_PHARMACY_NETWORK: 'ESI-SpecialtyPharmacyNetwork',
  SPECIALTY_GRACE_FILLS_RETAIL: 'ESI-SpecialtyGraceFillsRetail',
  HOME_DELIVERY_PROGRAM: 'ESI-HomeDeliveryProgram',
  FORMULARY_NAME: 'ESI-FormularyName',
  IRS_HDHP_PREVENTATIVE_LIST: 'ESI-IrsHdhpPreventativeList',
  UTILIZATION_MANAGEMENT_BUNDLE: 'ESI-UtilizationManagementBundle',
  DRUG_LIST: 'ESI-DrugList',
  COPAY_TIER: 'EsiCopayTier',
  COPAY_CHANNEL: 'EsiCopayChannel',
  COPAY_STRUCTURE: 'EsiCopayStructure',
  COPAY_NETWORK: 'EsiCopayNetwork',
  NINETY_DAY_PROGRAM: 'EsiNinetyDayProgram',
  MANDATORY_SPECIALTY_DAYS_SUPPLY: 'EsiMandatorySpecialtyDaysSupply',
  MANDATORY_SPECIALTY_FILL_LIMIT: 'EsiMandatorySpecialtyFillLimit',
  RETAIL_DAYS_SUPPLY: 'EsiRetailDaysSupply',
  HOME_DELIVERY_FILL_LIMIT: 'EsiHomeDeliveryFillLimit',
  HOME_DELIVERY_PROGRAM_ESI: 'EsiHomeDeliveryProgram',
  PAPER_CLAIMS_COVERED: 'EsiPaperClaimsCovered',
  FOREIGN_CLAIMS: 'EsiForeignClaims',
  ACCUM_PERIOD: 'EsiAccumPeriod',
  DEPENDENT_AGE: 'EsiDependentAge',
  INSULIN_METHOD: 'EsiInsulinMethod',
  RETAIL_NETWORK: 'EsiRetailNetwork',
  CLAIM_SUBMISSION_TYPE: 'EsiClaimSubmissionType',
  ESI_SUBMISSION_TYPE: 'EsiSubmissionType',
  ESI_PROCESS_MODE: 'EsiProcessMode',
  XML_ACCUMS_COMPLETE: 'XmlAccumsComplete',
} as const;

// OptumRx Specific Picklists
export const OPT_PICKLISTS = {
  RETAIL_PHARMACY_NETWORK: 'OPT-RetailPharmacyNetwork',
  MAINTENANCE_PHARMACY_NETWORK: 'OPT-MaintenancePharmacyNetwork',
  SPECIALTY_PHARMACY_NETWORK: 'OPT-SpecialtyPharmacyNetwork',
  MANDATORY_MAIL_PROGRAM: 'OPT-MandatoryMailProgram',
  HOME_DELIVERY_PROGRAM: 'OPT-HomeDeliveryProgram',
  FORMULARY_NAME: 'OPT-FormularyName',
  ABORTIFACIENTS: 'OPT-Abortifacients',
  ACA_CONTRACEPTIVES: 'OPT-AcaContraceptives',
  IRS_HDHP_PREVENTATIVE_LIST: 'OPT-IrsHdhpPreventativeList',
  UTILIZATION_MANAGEMENT_BUNDLE: 'OPT-UtilizationManagementBundle',
} as const;

// Caremark Specific Picklists
export const CMK_PICKLISTS = {
  RETAIL_PHARMACY_NETWORK: 'CMK-RetailPharmacyNetwork',
  MAINTENANCE_PHARMACY_NETWORK: 'CMK-MaintenancePharmacyNetwork',
  SPECIALTY_PHARMACY_NETWORK: 'CMK-SpecialtyPharmacyNetwork',
  FORMULARY_NAME: 'CMK-FormularyName',
  COVER_BROADER_VACCINATION_NETWORK: 'CMK-CoverBroaderVaccinationNetwork',
  IRS_HDHP_PREVENTATIVE_LIST: 'CMK-IrsHdhpPreventativeList',
  UTILIZATION_MANAGEMENT_BUNDLE: 'CMK-UtilizationManagementBundle',
  STEP_THERAPY_OPTION: 'CMK-StepTherapyOption',
} as const;

// Product Options Picklists
export const PRODUCT_OPTIONS = {
  MCAP_CMK: 'McapProductOptionsCmk',
  MCAP_ESI: 'McapProductOptionsEsi',
  MCAP_OPT: 'McapProductOptionsOpt',
  PVP_ESI: 'PvpProductOptionsEsi',
  INTERNATIONAL_DRUG_SOURCING: 'InternationalDrugSourcingProductOptions',
  OPTIMIZE_MY_CARE: 'OptimizeMyCareProductOptions',
  PAP: 'PapProductOptions',
  SAVE_ON_SP_ADAPT: 'SaveOnSP Adapt', // Note the space in this constant
} as const;

// Miscellaneous Picklists
export const MISC_PICKLISTS = {
  MANDATORY_MAIL_GRACE_FILLS: 'MandatoryMailGraceFills',
  RED_LIST: 'RedList',
} as const;

// Extract value types for each picklist group
export type PicklistValues = typeof PICKLISTS[keyof typeof PICKLISTS];
export type ESIPicklistValues =
  typeof ESI_PICKLISTS[keyof typeof ESI_PICKLISTS];
export type OPTPicklistValues =
  typeof OPT_PICKLISTS[keyof typeof OPT_PICKLISTS];
export type CMKPicklistValues =
  typeof CMK_PICKLISTS[keyof typeof CMK_PICKLISTS];
export type ProductOptionValues =
  typeof PRODUCT_OPTIONS[keyof typeof PRODUCT_OPTIONS];
export type MiscPicklistValues =
  typeof MISC_PICKLISTS[keyof typeof MISC_PICKLISTS];

// Union type of all picklist values
export type AllPicklistValues =
  | PicklistValues
  | ESIPicklistValues
  | OPTPicklistValues
  | CMKPicklistValues
  | ProductOptionValues
  | MiscPicklistValues;

// Export all picklists as a combined object
export const ALL_PICKLISTS = {
  ...PICKLISTS,
  ...ESI_PICKLISTS,
  ...OPT_PICKLISTS,
  ...CMK_PICKLISTS,
  ...PRODUCT_OPTIONS,
  ...MISC_PICKLISTS,
} as const;

/**
 * Generate an array of picklist names for API calls
 * @param picklistGroups - Array of picklist group objects to extract values from
 * @returns Array of picklist name strings
 */
export const getPicklistNames = <T extends PicklistGroup>(
  picklistGroups: T[]
): string[] => {
  return picklistGroups.flatMap((group) => Object.values(group));
};

/**
 * Convert picklist response to options map format for dropdowns
 * @param picklistData - Picklist data from API response
 * @returns An object mapping option values to labels
 */
export const picklistToOptionsMap = (
  picklistData: PicklistOption[] | undefined
): Record<string, string> => {
  if (!picklistData || !Array.isArray(picklistData)) return {};

  return picklistData.reduce(
    (acc, { value, label }) => ({ ...acc, [value]: label }),
    {}
  );
};
