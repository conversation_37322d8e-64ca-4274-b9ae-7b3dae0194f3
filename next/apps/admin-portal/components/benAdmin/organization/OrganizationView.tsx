import { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { ChangeRequest, OrganizationDetails } from '../Models/interfaces';
import { TemplateFieldGroup } from '../ReusableComponents/Models/types';
import { ChangeRequestsSection } from './components/ChangeRequests/ChangeRequestSection';
import { GlobalAccountInfoCard } from './components/GlobalAccount/GlobalAccountInfocard';
import { useGlobalAccountInformationFieldGroup } from './components/GlobalAccount/GlobalAccountInformation';
import { OrganizationHeader } from './components/OrganizationHeader';
import { clientConfig } from './components/Tabs/PlanDesign/ClientProfile/Config/clientConfig';
import { ReusableBenAdminTabs } from './components/Tabs/ReusableBenAdminTabs';
import { useOrganizationApi } from './hooks/useOrganizationViewApis';

/**
 * The OrganizationView component displays high-level information about an organization
 * and integrates multiple sub-sections (Change Requests, Global Account Info, Tabs, etc.).
 * It expects organization details and form methods as props.
 */
export const OrganizationView = ({
  organizationDetails,
  formMethods,
  onUpdateActiveItem,
}: {
  organizationDetails: OrganizationDetails;
  formMethods: UseFormReturn<any>;
  onUpdateActiveItem?: (section: string, tab?: string) => void;
}) => {
  // --------------------------------------------------
  // 1) Basic Information Extraction
  // --------------------------------------------------
  // Derive the organization name and ID from the incoming organization details.
  // If no org_name is provided, we fall back to the legal entity name.
  const orgName = formMethods.watch(clientConfig?.legal_entity_name);
  const orgId = organizationDetails?.organization?.organization_id;

  // Memoize the orgName to prevent unnecessary re-renders
  const memoizedOrgName = useMemo(() => orgName, [orgName]);

  // --------------------------------------------------
  // 2) Fetch Data for Change Requests
  // --------------------------------------------------
  // The useOrganizationApi hook fetches default change requests associated with the organization.
  // This data is passed to the ChangeRequestsSection below.
  const { changeRequests: defaultChangeRequests } = useOrganizationApi(orgId);

  // You will need to call contacts api inside of useOrganizationsApi and pass it here to use the data
  //   const contactDetails = mockContactsData;

  // --------------------------------------------------
  // 3) Configure Field Groups for Different Sections
  // --------------------------------------------------
  // Global Account Field Group: Data shown in the GlobalAccountInfoCard.
  const globalAccountFieldGroup: TemplateFieldGroup =
    useGlobalAccountInformationFieldGroup(organizationDetails, formMethods);

  // --------------------------------------------------
  // 4) Render UI
  // --------------------------------------------------
  return (
    <>
      {/* 
        Header: Displays the organization name at the top of the page.
        Typically includes brand or org-specific title. 
      */}
      <OrganizationHeader orgName={memoizedOrgName} />

      {/* 
        Global Account Info Card: Shows high-level organization details.
        Useful for quick reference to core organization data (e.g., legal entity, address). 
      */}
      <GlobalAccountInfoCard fieldGroup={globalAccountFieldGroup} />

      {/* 
        Change Requests Section:
        Displays the organization's change requests, including logic for creating
        and editing requests. The "defaultChangeRequests" are fetched via useOrganizationApi.
      */}
      <ChangeRequestsSection
        allChangeRequests={defaultChangeRequests as ChangeRequest[]}
        organizationDetails={organizationDetails}
        orgId={orgId}
      />

      {/* 
        Reusable BenAdmin Tabs Section:
        Now using our reusable component which handles all the data preparation internally
      */}
      <ReusableBenAdminTabs
        formMethods={formMethods}
        organizationDetails={organizationDetails}
        onUpdateActiveItem={onUpdateActiveItem}
      />
    </>
  );
};
