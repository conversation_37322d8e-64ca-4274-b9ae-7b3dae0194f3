import { Box, Button, Flex, Heading, useDisclosure } from '@chakra-ui/react';
import {
  AddClient,
  CustomReactTable,
  Pagination,
  UploadClaim,
  UploadDispenseReport,
  UploadPrescriberOverride,
  UploadRejectReport,
} from '@next/admin/components';
import { useApi } from '@next/shared/api';
import { useCustomToast } from '@next/shared/hooks';
import { createColumnHelper } from '@tanstack/react-table';
import EditClient from 'libs/client/admin-portal/components/src/lib/340B/EditClient';
import { ConfirmationPopup } from 'libs/client/admin-portal/components/src/lib/Global/ConfirmationPopup';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

interface Client {
  E340BIDs?: string[];
  orgNo: number;
  name: string;
  serviceName: string;
  dispenseReport: boolean;
  actions?: any;
}

const Three40b = () => {
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  const apiInstance = useApi();
  const { getApi, methodApi, clients340b } = apiInstance;

  const searchParams = useSearchParams();
  const page = searchParams?.get('page');
  const size = searchParams?.get('size');
  const currentPageNumber = useMemo(() => (page ? +page : 1), [page]);
  const rowsPerPage = useMemo(() => (size ? +size : 10), [size]);

  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: isClientOpen,
    onOpen: onClientOpen,
    onClose: onClientClose,
  } = useDisclosure();
  const {
    isOpen: isClientEditOpen,
    onOpen: onClientEditOpen,
    onClose: onClientEditClose,
  } = useDisclosure();
  const {
    isOpen: isDispenseReportOpen,
    onOpen: onDispenseReportOpen,
    onClose: onDispenseReportClose,
  } = useDisclosure();
  const {
    isOpen: isRejectReportOpen,
    onOpen: onRejectReportOpen,
    onClose: onRejectReportClose,
  } = useDisclosure();
  const {
    isOpen: isPrescriberOverrideOpen,
    onOpen: onPrescriberOverrideOpen,
    onClose: onPrescriberOverrideClose,
  } = useDisclosure();

  const {
    isOpen: isConfirmOpen,
    onOpen: onConfirmOpen,
    onClose: onConfirmClose,
  } = useDisclosure();

  const showToast = useCustomToast();

  const onFileUpload = () => {
    showToast({
      title: 'File Uploaded Successfully',
      status: 'success',
    });
  };

  const onAddEditClient = (isEdit = false) => {
    showToast({
      title: isEdit ? 'Client Saved Successfully' : 'Client Added Successfully',
      status: 'success',
    });
    getApi('clients340b', {
      limit: rowsPerPage,
      offset: (currentPageNumber - 1) * rowsPerPage,
    });
  };

  useEffect(() => {
    getApi('clients340b', {
      limit: rowsPerPage,
      offset: (currentPageNumber - 1) * rowsPerPage,
    });
  }, [rowsPerPage, currentPageNumber, getApi]);

  const value = clients340b
    ? Array.isArray(clients340b.clients) && clients340b.clients.length > 0
      ? clients340b.clients.map((client: any) => ({
          E340BIDs: client['E340BIDs'],
          orgNo: client.orgNo,
          name: client.name,
          service: client.service,
          dispenseReport: client?.dispenseReport,
        }))
      : []
    : undefined;

  const onDelete = (OrgNo: number | undefined) => {
    onConfirmClose();
    methodApi('deleteClients340b', {
      restParams: { OrgNo: OrgNo },
      method: 'DELETE',
      sendType: 'application/x-www-form-urlencoded',
      onSuccess: () => {
        showToast({
          title: 'Client Deleted Successfully',
          status: 'success',
        });
        getApi('clients340b', {
          limit: rowsPerPage,
          offset: (currentPageNumber - 1) * rowsPerPage,
        });
      },
    });
  };
  const onDeleteDispenseReport = (OrgNo: number) => {
    methodApi('deleteDispenseReport340b', {
      restParams: { OrgNo: OrgNo },
      method: 'DELETE',
      sendType: 'application/x-www-form-urlencoded',
      onSuccess: () => {
        showToast({
          title: 'Dispense Report Deleted Successfully',
          status: 'success',
        });
        getApi('clients340b', {
          limit: rowsPerPage,
          offset: (currentPageNumber - 1) * rowsPerPage,
        });
      },
    });
  };

  const columnHelper = createColumnHelper<Client>();
  const columns = [
    columnHelper.accessor('name', {
      cell: (info) => info.getValue(),
      header: 'Organization Name',
    }),
    columnHelper.accessor('orgNo', {
      cell: (info) => info.getValue(),
      header: 'Organization ID',
    }),
    columnHelper.accessor('E340BIDs', {
      cell: (info) =>
        info.getValue()?.length > 1 ? 'MULTIPLE' : info.getValue()?.[0],
      header: '340B ID',
    }),
    columnHelper.accessor('serviceName', {
      cell: (info) => info.getValue(),
      header: 'Service',
    }),
    columnHelper.accessor('actions', {
      cell: (info) => (
        <Flex gap={2}>
          {!info.row.original?.dispenseReport ? (
            <Flex
              onClick={() => {
                setSelectedClient(info.row.original);
                onDispenseReportOpen();
              }}
              bg="green.500"
              color="white"
              py="3.9px"
              px="2"
              borderRadius="4"
              cursor="pointer"
            >
              Import Dispense Report
            </Flex>
          ) : (
            <Flex
              onClick={() => onDeleteDispenseReport(info.row.original.orgNo)}
              bg="red.500"
              color="white"
              py="3.9px"
              px="2"
              borderRadius="4"
              cursor="pointer"
            >
              Delete Dispense Report
            </Flex>
          )}

          <Flex
            bg="blue.600"
            color="white"
            py="3.9px"
            px="2"
            borderRadius="4"
            cursor="pointer"
            onClick={() => {
              setSelectedClient(info.row.original);
              onPrescriberOverrideOpen();
            }}
          >
            Dr Override
          </Flex>

          <Flex
            onClick={() => {
              setSelectedClient(info.row.original);
              onClientEditOpen();
            }}
            bg="gray.600"
            color="white"
            py="3.9px"
            px="2"
            borderRadius="4"
            cursor="pointer"
          >
            Edit
          </Flex>
          <Flex
            onClick={() => {
              setSelectedClient(info.row.original);
              onConfirmOpen();
            }}
            bg="red.500"
            color="white"
            py="3.9px"
            px="2"
            borderRadius="4"
            cursor="pointer"
          >
            Delete Client
          </Flex>
          <ConfirmationPopup
            isOpen={isConfirmOpen}
            onClose={onConfirmClose}
            onConfirm={() => onDelete(selectedClient?.orgNo)}
            alertHeader="Delete Client"
            alertBody="Are you sure you want to delete this client?"
          />
        </Flex>
      ),
      header: 'Actions',
    }),
  ];

  return (
    <Flex justifyContent="center" flexDirection="column" background="#fff">
      <Box
        as="div"
        padding="15px"
        display="flex"
        justifyContent="space-between"
        alignItems="center"
      >
        <Box as="div">
          <Heading fontWeight={700} fontSize="18px">
            340B
          </Heading>
        </Box>
        <Box>
          <Button colorScheme="green" onClick={onClientOpen}>
            Add Organization
          </Button>
          <Button colorScheme="blue" marginLeft="16px" onClick={onOpen}>
            External Claim Load
          </Button>
          <Button
            variant="solid"
            colorScheme="brand"
            bg="brand.700"
            marginLeft="16px"
            onClick={onRejectReportOpen}
          >
            Upload Reject Report
          </Button>
        </Box>
      </Box>
      <CustomReactTable columns={columns} data={value} />
      <Pagination totalCount={clients340b && clients340b.totalCount} />
      <UploadClaim
        isOpen={isOpen}
        onClose={onClose}
        onFileUpload={onFileUpload}
      />
      <AddClient
        isOpen={isClientOpen}
        onClose={onClientClose}
        onAddClient={onAddEditClient}
      />
      <EditClient
        isOpen={isClientEditOpen}
        onClose={onClientEditClose}
        data={selectedClient}
        onEditClient={onAddEditClient}
      />
      <UploadDispenseReport
        isOpen={isDispenseReportOpen}
        onClose={onDispenseReportClose}
        apiInstance={apiInstance}
        OrgNo={selectedClient?.orgNo}
      />
      <UploadRejectReport
        isOpen={isRejectReportOpen}
        onClose={onRejectReportClose}
      />
      <UploadPrescriberOverride
        isOpen={isPrescriberOverrideOpen}
        onClose={onPrescriberOverrideClose}
        apiInstance={apiInstance}
        E340BIDs={selectedClient?.E340BIDs}
      />
    </Flex>
  );
};

export default Three40b;
