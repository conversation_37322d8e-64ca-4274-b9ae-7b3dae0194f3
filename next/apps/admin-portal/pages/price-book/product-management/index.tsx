import {
  ChevronDownIcon,
  ChevronUpIcon,
  DownloadIcon,
  EditIcon,
  RepeatIcon,
  SearchIcon,
  ViewIcon,
} from '@chakra-ui/icons';
import {
  Alert,
  AlertIcon,
  Badge,
  Box,
  Button,
  Card,
  CardBody,
  Divider,
  Flex,
  Grid,
  Heading,
  HStack,
  IconButton,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Spinner,
  Stack,
  Table,
  Tag,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
  useToast,
  VStack,
} from '@chakra-ui/react';
import {
  exportProducts,
  getProducts,
  updateCommonlyUsed,
} from 'apps/admin-portal/components/pricing/lib/services/productService';
import { Product } from 'apps/admin-portal/components/pricing/lib/types/productManagement';
import {
  formatDate,
  formatPercent,
} from 'apps/admin-portal/components/pricing/lib/utils/formatters';
import {
  channels,
  filterLabels,
} from 'apps/admin-portal/components/pricing/lib/utils/productManagementConstants';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { FaHeart, FaRegHeart } from 'react-icons/fa';

const ProductManagement = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCardView, setIsCardView] = useState(false);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [isDownloadInProgress, setIsDownloadInProgress] = useState(false);
  const [showMore, setShowMore] = useState(false);

  const [filters, setFilters] = useState({
    pbm: '',
    formulary: '',
    clientSize: '',
    status: '',
    biosimilar: '',
    weightLoss: '',
    commonlyUsed: false,
    searchTerm: '',
  });

  const router = useRouter();
  const toast = useToast();

  // Fetch products on component mount
  useEffect(() => {
    fetchProducts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Apply filters whenever they change
  useEffect(() => {
    applyFilters();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [products, filters]);

  const fetchProducts = async () => {
    try {
      setIsLoading(true);
      const data = await getProducts();
      setProducts(data);
      setFilteredProducts(data);
    } catch (err: any) {
      setError(
        err.response?.data?.message ||
          'Failed to load products. Please try again.'
      );

      toast({
        title: 'Error loading products',
        description:
          err.response?.data?.message ||
          'An error occurred while loading products.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const isMatchingDiscount = (
    product: Product,
    normalizedSearchTerm: string
  ) => {
    const discounts = getDiscountsByChannel(product);
    const filteredDiscountKeys = Object.entries(discounts).filter(
      ([key, value]) =>
        key.toLowerCase().includes(normalizedSearchTerm.replace(' ', '')) ||
        value.label
          .toLowerCase()
          .includes(normalizedSearchTerm.replace(' ', '')) ||
        value.name.toLowerCase().includes(normalizedSearchTerm.replace(' ', ''))
    );
    return (
      filteredDiscountKeys.filter(
        (d) => d[1].brand !== null || d[1].generic !== null
      ).length > 0
    );
  };

  const isMatchingDate = (
    effectiveDate: string,
    searchTerm: string
  ): boolean => {
    const [year, month] = effectiveDate.split('-');
    const parts = searchTerm.split('/');

    if (parts.length === 1) {
      // Format: "YYYY"
      return parts[0] === year;
    }

    if (parts.length === 2) {
      // Format: "MM/YYYY"
      const [searchMonth, searchYear] = parts;
      return searchYear === year && searchMonth === month;
    }

    if (parts.length === 3) {
      // Format: "MM/DD/YYYY" — compare only month and year
      const [searchMonth, , searchYear] = parts;
      return searchYear === year && searchMonth === month;
    }

    return false; // Invalid format
  };

  const applyFilters = () => {
    let filtered = [...products];
    const activeFilterList: string[] = [];

    const strategies: Record<string, (product: Product) => boolean> = {
      pbm: (p) => p.pbm === filters.pbm,
      formulary: (p) => p.formulary === filters.formulary,
      clientSize: (p) => p.clientSize === filters.clientSize,
      status: (p) => p.status === filters.status,
      biosimilar: (p) => p.parameters?.biosimilar === filters.biosimilar,
      weightLoss: (p) =>
        p.parameters?.weight_loss_coverage === filters.weightLoss,
      commonlyUsed: (p) => p.isCommonlyUsed,
    };

    // Search term
    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase().trim();
      filtered = filtered.filter(
        (p) =>
          p.productName.toLowerCase().includes(term) ||
          p.pbm?.toLowerCase().includes(term) ||
          p.discountType?.toLowerCase().includes(term) ||
          isMatchingDate(p.effectiveDate, term) ||
          isMatchingDiscount(p, term)
      );
      activeFilterList.push(`Search: ${filters.searchTerm}`);
    }

    // Loop through all other filters
    Object.entries(filters).forEach(([key, value]) => {
      if (key === 'searchTerm' || value === '' || value === false) return;

      const strategy = strategies[key];
      if (strategy) {
        filtered = filtered.filter(strategy);
        const label =
          typeof value === 'boolean'
            ? `Commonly Used`
            : `${filterLabels[key]}: ${value}`;
        activeFilterList.push(label);
      }
    });

    setFilteredProducts(filtered);
    setActiveFilters(activeFilterList);
  };

  // Get unique values for filter dropdowns
  const uniquePbms = useMemo(() => {
    const set = new Set(products.map((product) => product.pbm));
    return Array.from(set).sort();
  }, [products]);

  const uniqueFormularies = useMemo(() => {
    const set = new Set(products.map((product) => product.formulary));
    return Array.from(set).sort();
  }, [products]);

  const uniqueClientSizes = useMemo(() => {
    const set = new Set(products.map((product) => product.clientSize));
    return Array.from(set).sort();
  }, [products]);

  const uniqueBiosimilars = useMemo(() => {
    const set = new Set(
      products.map((product) => product.parameters.biosimilar).filter(Boolean)
    );
    return Array.from(set).sort();
  }, [products]);

  const uniqueWeightLossCoverages = useMemo(() => {
    const set = new Set(
      products
        .map((product) => product.parameters?.weight_loss_coverage)
        .filter(Boolean)
    );
    return Array.from(set).sort();
  }, [products]);

  const clearFilters = () => {
    setFilters({
      pbm: '',
      formulary: '',
      clientSize: '',
      status: '',
      biosimilar: '',
      weightLoss: '',
      commonlyUsed: false,
      searchTerm: '',
    });
  };

  const handleFilterChange = (
    key: keyof typeof filters,
    value: string | boolean
  ) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleExport = async () => {
    setIsDownloadInProgress(true);

    toast({
      title: 'Generating file...',
      description: 'Please wait while we create your Excel file.',
      status: 'info',
      duration: 4000,
      isClosable: true,
    });
    try {
      const response = await exportProducts(filteredProducts);
      const url = response.downloadUrl;

      toast({
        title: 'Excel file ready!',
        description: 'Your file will begin downloading shortly.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Trigger download
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute(
        'download',
        `products-${new Date().toISOString().split('T')[0]}.xlsx`
      );
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      toast({
        title: 'Export failed',
        description: 'Could not generate Excel file.',
        status: 'error',
        duration: 3000,
      });
    } finally {
      setIsDownloadInProgress(false);
    }
  };

  // Get discount values from product for a specific channel
  const getChannelDiscounts = (product: Product, channelKey: string) => {
    if (!product.values?.[channelKey]) return { brand: null, generic: null };

    const channel = product.values[channelKey];
    return {
      brand: channel.brand?.discount,
      generic: channel.generic?.discount,
    };
  };

  // Return all discount pairs for various channels
  const getDiscountsByChannel = (product: Product) => {
    // Start with blended specialty as a special case
    const discounts: Record<
      string,
      {
        name: string;
        label: string;
        brand: number | null;
        generic: number | null;
      }
    > = {};

    // Handle regular channels
    channels.forEach((channel) => {
      const { brand, generic } = getChannelDiscounts(product, channel.key);
      discounts[channel.key] = {
        name: channel.name,
        label: channel.label,
        brand,
        generic,
      };
    });

    // Handle blended specialty as a special case if it exists
    if (product.values?.blendedSpecialty) {
      discounts.blendedSpecialty = {
        name: 'Blended Specialty',
        label: 'BS',
        brand: product.values.blendedSpecialty.nonLdd?.discount || null,
        generic: product.values.blendedSpecialty.ldd?.discount || null,
      };
    }

    return discounts;
  };

  // Format detail information
  const formatProductInfo = (product: Product) => {
    return (
      <VStack align="start" spacing={1}>
        <Text fontWeight="bold">{product.productName}</Text>
        <Text fontSize="sm" color="gray.600">
          <Text as="span" fontWeight="medium">
            PBM:
          </Text>{' '}
          {product.pbm}
        </Text>
        <Text fontSize="sm" color="gray.600">
          <Text as="span" fontWeight="medium">
            Effective:
          </Text>{' '}
          {formatDate(product.effectiveDate)}
          {product.expiryDate && ` to ${formatDate(product.expiryDate)}`}
        </Text>
        <Badge
          colorScheme={product.status === 'ACTIVE' ? 'green' : 'red'}
          fontSize="xs"
          mt={1}
        >
          {product.status}
        </Badge>
      </VStack>
    );
  };

  const handleViewDetails = (productId: string) => {
    router.push(
      `/price-book/product-management/productDetails?productId=${productId}`
    );
  };

  const handleEdit = (productId: string) => {
    router.push(
      `/price-book/product-management/productEdit?productId=${productId}`
    );
  };

  const isExpired = (expiryDate: string | null) => {
    return expiryDate ? new Date(expiryDate) < new Date() : false;
  };

  const toggleCommonlyUsed = async (
    productId: string,
    isCommonlyUsed: boolean
  ) => {
    setProducts((prev) =>
      prev.map((p) =>
        p.id === productId ? { ...p, isCommonlyUsed: isCommonlyUsed } : p
      )
    );
    try {
      await updateCommonlyUsed(productId, isCommonlyUsed);
      toast({
        title: `${isCommonlyUsed ? 'Added to' : 'Removed from'} Commonly Used`,
        status: 'success',
        duration: 3000,
      });
    } catch (error) {
      setProducts((prev) =>
        prev.map((p) =>
          p.id === productId ? { ...p, isCommonlyUsed: !isCommonlyUsed } : p
        )
      );
      toast({
        title: 'Failed to update Commonly Used',
        status: 'error',
        duration: 3000,
      });
    }
  };

  return (
    <Box p={4} bg="white" borderRadius="lg">
      <Heading mb={6}>Products</Heading>

      {/* Filters and Search */}
      <Box p={5} borderWidth="1px" borderRadius="lg" bg="white" mb={6}>
        <Flex direction={{ base: 'column', md: 'row' }} gap={4} mb={4}>
          <InputGroup flex="1">
            <InputLeftElement pointerEvents="none">
              <SearchIcon color="gray.300" />
            </InputLeftElement>
            <Input
              placeholder="Search by product name, PBM, Effective Date (format: mm/dd/yyyy, mm/yyyy or yyyy) etc."
              value={filters.searchTerm}
              onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
            />
          </InputGroup>

          <Select
            placeholder="Filter by PBM"
            value={filters.pbm}
            onChange={(e) => handleFilterChange('pbm', e.target.value)}
            width={{ base: 'full', md: '300px' }}
          >
            {uniquePbms.map((pbm) => (
              <option key={pbm} value={pbm}>
                {pbm}
              </option>
            ))}
          </Select>

          <HStack spacing={2} ml="auto">
            <Button
              leftIcon={isCardView ? <ViewIcon /> : <ViewIcon />}
              onClick={() => setIsCardView(!isCardView)}
              variant="outline"
            >
              {isCardView ? 'Table View' : 'Card View'}
            </Button>

            <Button
              leftIcon={<DownloadIcon />}
              onClick={handleExport}
              variant="outline"
              isLoading={isDownloadInProgress}
            >
              Export as Excel
            </Button>
          </HStack>
        </Flex>

        <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
          <Select
            placeholder="Filter by Formulary"
            value={filters.formulary}
            onChange={(e) => handleFilterChange('formulary', e.target.value)}
            flex="1"
          >
            {uniqueFormularies.map((formulary) => (
              <option key={formulary} value={formulary}>
                {formulary}
              </option>
            ))}
          </Select>

          <Select
            placeholder="Filter by Client Size"
            value={filters.clientSize}
            onChange={(e) => handleFilterChange('clientSize', e.target.value)}
            flex="1"
          >
            {uniqueClientSizes.map((size) => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </Select>

          <Select
            placeholder="Filter by Status"
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            width={{ base: 'full', md: '200px' }}
          >
            <option key={'ACTIVE'} value={'ACTIVE'}>
              ACTIVE
            </option>

            <option key={'INACTIVE'} value={'INACTIVE'}>
              INACTIVE
            </option>
          </Select>

          <Button
            leftIcon={<FaHeart />}
            variant={filters.commonlyUsed ? 'solid' : 'outline'}
            textColor={filters.commonlyUsed ? 'white' : 'black'}
            bg={filters.commonlyUsed ? 'blue.400' : 'white'}
            _hover={
              filters.commonlyUsed ? { bg: 'blue.200' } : { bg: 'gray.100' }
            }
            onClick={() =>
              handleFilterChange('commonlyUsed', !filters.commonlyUsed)
            }
          >
            Commonly Used
          </Button>

          <Button colorScheme="blue" onClick={applyFilters}>
            Apply Filters
          </Button>

          {activeFilters.length > 0 && (
            <Button variant="outline" onClick={clearFilters}>
              Clear Filters
            </Button>
          )}

          <Button
            variant="ghost"
            rightIcon={showMore ? <ChevronUpIcon /> : <ChevronDownIcon />}
            onClick={() => setShowMore(!showMore)}
          >
            More Filters
          </Button>
        </Flex>

        {showMore && (
          <Flex
            direction={{ base: 'column', md: 'row' }}
            justify="space-between"
            align="center"
            gap={4}
            mt="4"
            width="100%"
          >
            {/* Left side: Conditional Filters */}
            <HStack spacing={4} wrap="wrap">
              <Select
                placeholder="Filter by Biosimilar"
                width="200px"
                value={filters.biosimilar}
                onChange={(e) =>
                  handleFilterChange('biosimilar', e.target.value)
                }
              >
                {uniqueBiosimilars.map((value) => (
                  <option key={value} value={value}>
                    {value}
                  </option>
                ))}
              </Select>

              <Select
                placeholder="Filter by Weight Loss Coverage"
                width="300px"
                value={filters.weightLoss}
                onChange={(e) =>
                  handleFilterChange('weightLoss', e.target.value)
                }
              >
                {uniqueWeightLossCoverages.map((value) => (
                  <option key={value} value={value}>
                    {value}
                  </option>
                ))}
              </Select>
            </HStack>
          </Flex>
        )}

        {/* Active Filters */}
        {activeFilters.length > 0 && (
          <Flex mt={4} align="center">
            <Text fontWeight="bold" mr={2}>
              Active Filters:
            </Text>
            <Flex gap={2} flexWrap="wrap">
              {activeFilters.map((filter, index) => (
                <Tag
                  key={index}
                  size="md"
                  colorScheme="blue"
                  borderRadius="full"
                >
                  {filter}
                </Tag>
              ))}
            </Flex>
          </Flex>
        )}
      </Box>
      {/* Results Count and Refresh */}
      <Flex justify="space-between" align="center" mb={4}>
        <Text fontWeight="medium">
          Showing {filteredProducts.length} of {products.length} products
        </Text>
        <Button
          leftIcon={<RepeatIcon />}
          size="sm"
          variant="outline"
          onClick={fetchProducts}
          isLoading={isLoading}
        >
          Refresh
        </Button>
      </Flex>

      {/* Loading Spinner */}
      {isLoading ? (
        <Flex justify="center" align="center" h="200px">
          <Spinner size="xl" color="blue.500" />
        </Flex>
      ) : error ? (
        <Alert status="error" borderRadius="md">
          <AlertIcon />
          {error}
        </Alert>
      ) : filteredProducts.length === 0 ? (
        <Alert status="info" borderRadius="md">
          <AlertIcon />
          No products found matching your criteria. Try adjusting your filters.
        </Alert>
      ) : isCardView ? (
        /* Card View */
        <Grid
          templateColumns={{
            base: '1fr',
            md: 'repeat(2, 1fr)',
            lg: 'repeat(3, 1fr)',
          }}
          gap={6}
        >
          {filteredProducts.map((product) => {
            const discounts = getDiscountsByChannel(product);

            return (
              <Card
                key={product.id}
                borderRadius="lg"
                overflow="hidden"
                variant="outline"
              >
                <CardBody>
                  <Stack spacing={4}>
                    <Flex justify="space-between" align="flex-start">
                      <VStack align="start" spacing={1}>
                        <Heading size="md">{product.productName}</Heading>
                        <Text color="blue.600" fontWeight="medium">
                          {product.pbm}
                        </Text>
                        <Text fontSize="sm">
                          {formatDate(product.effectiveDate)}
                          {product.expiryDate &&
                            ` - ${formatDate(product.expiryDate)}`}
                        </Text>
                      </VStack>
                      <Badge
                        colorScheme={
                          product.status === 'ACTIVE' ? 'green' : 'red'
                        }
                        mt={1}
                      >
                        {product.status}
                      </Badge>
                    </Flex>

                    <Divider />

                    <Box>
                      <Heading size="sm" mb={2}>
                        Key Parameters
                      </Heading>
                      <Flex wrap="wrap" gap={2}>
                        <Tooltip label="Formulary" placement="top">
                          <Tag colorScheme="purple">{product.formulary}</Tag>
                        </Tooltip>
                        <Tooltip label="Client Size" placement="top">
                          <Tag colorScheme="teal">{product.clientSize}</Tag>
                        </Tooltip>
                        <Tooltip label="Discount Type" placement="top">
                          <Tag colorScheme="orange">{product.discountType}</Tag>
                        </Tooltip>
                      </Flex>
                    </Box>

                    <Box>
                      <Heading size="sm" mb={2}>
                        Channel Discounts
                      </Heading>
                      <Table size="sm" variant="simple">
                        <Thead>
                          <Tr>
                            <Th pl={0}>Channel</Th>
                            <Th isNumeric>Brand</Th>
                            <Th isNumeric>Generic</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {Object.values(discounts)
                            .filter(
                              (d) => d.brand !== null || d.generic !== null
                            )
                            .slice(0, 5) // Limit rows for card view
                            .map((discount, idx) => (
                              <Tr key={idx}>
                                <Td pl={0}>
                                  <Tooltip label={discount.name}>
                                    <Text>{discount.label}</Text>
                                  </Tooltip>
                                </Td>
                                <Td isNumeric>
                                  {discount.brand !== null
                                    ? formatPercent(discount.brand)
                                    : '-'}
                                </Td>
                                <Td isNumeric>
                                  {discount.generic !== null
                                    ? formatPercent(discount.generic)
                                    : '-'}
                                </Td>
                              </Tr>
                            ))}
                        </Tbody>
                      </Table>
                      {Object.values(discounts).filter(
                        (d) => d.brand !== null || d.generic !== null
                      ).length > 5 && (
                        <Text
                          fontSize="xs"
                          textAlign="right"
                          mt={1}
                          fontStyle="italic"
                        >
                          +{' '}
                          {Object.values(discounts).filter(
                            (d) => d.brand !== null || d.generic !== null
                          ).length - 5}{' '}
                          more channels
                        </Text>
                      )}
                    </Box>

                    <Flex justify="flex-end" mt={2}>
                      <IconButton
                        icon={<ViewIcon />}
                        aria-label="View"
                        mr={2}
                        colorScheme="blue"
                        variant="outline"
                        onClick={() => handleViewDetails(product.id)}
                      />
                      <IconButton
                        icon={<EditIcon />}
                        aria-label="Edit"
                        colorScheme="green"
                        variant="outline"
                        mr={2}
                        isDisabled={isExpired(product.expiryDate)}
                        onClick={() => handleEdit(product.id)}
                      />
                      <IconButton
                        icon={
                          product.isCommonlyUsed ? (
                            <FaHeart color="red" />
                          ) : (
                            <FaRegHeart />
                          )
                        }
                        aria-label="Commonly Used"
                        variant="outline"
                        colorScheme="red"
                        onClick={() =>
                          toggleCommonlyUsed(
                            product.id,
                            !product.isCommonlyUsed
                          )
                        }
                      />
                    </Flex>
                  </Stack>
                </CardBody>
              </Card>
            );
          })}
        </Grid>
      ) : (
        /* Table View */
        <Box overflowX="auto">
          <Table variant="simple" borderWidth="1px" borderRadius="lg">
            <Thead bg="gray.50">
              <Tr>
                <Th>PRODUCT INFO</Th>
                <Th>KEY PARAMETERS</Th>
                <Th>DISCOUNTS BY CHANNEL</Th>
                <Th width="100px">ACTIONS</Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredProducts.map((product) => {
                const discounts = getDiscountsByChannel(product);

                return (
                  <Tr key={product.id}>
                    <Td>{formatProductInfo(product)}</Td>
                    <Td>
                      <VStack align="start" spacing={1}>
                        <Tooltip label="Formulary" placement="top">
                          <Tag colorScheme="purple" size="sm">
                            {product.formulary}
                          </Tag>
                        </Tooltip>
                        <Tooltip label="Client Size" placement="top">
                          <Tag colorScheme="teal" size="sm">
                            {product.clientSize}
                          </Tag>
                        </Tooltip>
                        <Tooltip label="Discount Type" placement="top">
                          <Tag colorScheme="orange" size="sm">
                            {product.discountType}
                          </Tag>
                        </Tooltip>
                      </VStack>
                    </Td>
                    <Td>
                      <Table size="sm" variant="unstyled">
                        <Thead>
                          <Tr>
                            <Th pl={0} fontSize="xs" width="40px">
                              Type
                            </Th>
                            <Th fontSize="xs" isNumeric width="60px">
                              Brand
                            </Th>
                            <Th fontSize="xs" isNumeric width="60px">
                              Generic
                            </Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {Object.values(discounts)
                            .filter(
                              (d) => d.brand !== null || d.generic !== null
                            )
                            .map((discount, idx) => (
                              <Tr key={idx}>
                                <Td pl={0} py={1}>
                                  <Tooltip
                                    label={discount.name}
                                    placement="left"
                                  >
                                    <Text fontSize="xs" fontWeight="medium">
                                      {discount.label}
                                    </Text>
                                  </Tooltip>
                                </Td>
                                <Td py={1} isNumeric>
                                  <Text fontSize="xs">
                                    {discount.brand !== null
                                      ? formatPercent(discount.brand)
                                      : '-'}
                                  </Text>
                                </Td>
                                <Td py={1} isNumeric>
                                  <Text fontSize="xs">
                                    {discount.generic !== null
                                      ? formatPercent(discount.generic)
                                      : '-'}
                                  </Text>
                                </Td>
                              </Tr>
                            ))}
                          {Object.values(discounts).filter(
                            (d) => d.brand !== null || d.generic !== null
                          ).length === 0 && (
                            <Tr>
                              <Td colSpan={3} py={1} textAlign="center">
                                <Text fontSize="xs" fontStyle="italic">
                                  No discount data
                                </Text>
                              </Td>
                            </Tr>
                          )}
                        </Tbody>
                      </Table>
                    </Td>
                    <Td>
                      <HStack spacing={2}>
                        <Tooltip label="View Details">
                          <IconButton
                            icon={<ViewIcon />}
                            aria-label="View"
                            size="sm"
                            colorScheme="blue"
                            variant="ghost"
                            onClick={() => handleViewDetails(product.id)}
                          />
                        </Tooltip>
                        <Tooltip label="Edit Product">
                          <IconButton
                            icon={<EditIcon />}
                            aria-label="Edit"
                            size="sm"
                            colorScheme="green"
                            variant="ghost"
                            isDisabled={isExpired(product.expiryDate)}
                            onClick={() => handleEdit(product.id)}
                          />
                        </Tooltip>
                        <Tooltip
                          label={
                            product.isCommonlyUsed
                              ? 'Remove Commonly Used'
                              : 'Mark Commonly Used'
                          }
                        >
                          <IconButton
                            icon={
                              product.isCommonlyUsed ? (
                                <FaHeart color="red" />
                              ) : (
                                <FaRegHeart />
                              )
                            }
                            aria-label="Commonly Used"
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              toggleCommonlyUsed(
                                product.id,
                                !product.isCommonlyUsed
                              )
                            }
                          />
                        </Tooltip>
                      </HStack>
                    </Td>
                  </Tr>
                );
              })}
            </Tbody>
          </Table>
        </Box>
      )}

      {/* Add Product Button */}
      <Flex justify="center" mt={6}>
        <Button
          colorScheme="blue"
          size="md"
          //   onClick={() => navigate('/products/create')}
        >
          Create New Product
        </Button>
      </Flex>
    </Box>
  );
};

export default ProductManagement;
