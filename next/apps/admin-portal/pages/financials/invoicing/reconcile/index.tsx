import { Box, Flex, Heading } from '@chakra-ui/react';
import { FinancialsLayout } from '@next/admin/components';
import { useJavaApi } from '@next/shared/api';
import { ReconcileTableTypes, TABS_LIST } from '@next/shared/types';
import { BackButton } from '@next/shared/ui';
import ReconcileCard from 'libs/client/admin-portal/components/src/lib/FinancialManagement/Invoicing/ReconcileCard';
import { useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';

const ReconcileTable = () => {
  const searchParams = useSearchParams();
  const activeTab = searchParams?.get('activeTab');
  const vendorFeePeriodNo = searchParams?.get('vendorFeePeriodNo');

  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(25);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [fetchedData, setFetchedData] = useState<{
    Invalid: ReconcileTableTypes[];
    Held: ReconcileTableTypes[];
    Archived: ReconcileTableTypes[];
    Reconciled: ReconcileTableTypes[];
  }>({
    Invalid: [],
    Held: [],
    Archived: [],
    Reconciled: [],
  });

  const { getApi } = useJavaApi();

  const processStatusCodes = useMemo(
    () => TABS_LIST.find((item) => item.key === activeTab)?.processStatus,
    [activeTab]
  );

  const fetchData = useCallback(() => {
    if (!processStatusCodes || !page || !pageSize) return;

    const params: Record<string, any> = {
      processStatusCodes: processStatusCodes as string | number,
      page,
      pageSize,
    };

    if (
      vendorFeePeriodNo !== undefined &&
      vendorFeePeriodNo !== null &&
      vendorFeePeriodNo !== ''
    ) {
      params.vendorFeePeriodNo = Number(vendorFeePeriodNo);
    }

    getApi('reconcileData', params).then((res) => {
      const newData = res.payload || [];

      if (activeTab === '3') {
        const filteredReconcileData =
          newData.filter(
            (item: ReconcileTableTypes) =>
              item.reconciledUserName !== null &&
              item.processStatusName === 'Valid'
          ) || [];

        setTotalPages(Math.ceil(filteredReconcileData.length / pageSize));
      } else {
        setTotalPages(Math.ceil(res.resultSetSize / pageSize));
      }
      setFetchedData((prevData) => ({
        ...prevData,
        [activeTab === '0'
          ? 'Invalid'
          : activeTab === '1'
          ? 'Held'
          : activeTab === '2'
          ? 'Archived'
          : 'Reconciled']: res.payload || [],
      }));
    });
  }, [
    getApi,
    processStatusCodes,
    vendorFeePeriodNo,
    page,
    pageSize,
    activeTab,
  ]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const apiPaginationHandlers = useMemo(
    () => ({
      currentPage: page,
      totalPages: totalPages,
      pageSize: pageSize,
      pageSizeDropdownValues: [25, 50, 100, 250, 500],
      onPageChange: (newPage: number) => setPage(newPage),
      onPageSizeChange: (newPageSize: number) => {
        setPageSize(newPageSize);
        setPage(1);
      },
    }),
    [page, pageSize, totalPages]
  );

  const tabs = useMemo(
    () => [
      {
        title: 'Invalid',
        component: (
          <ReconcileCard
            key="Invalid"
            data={fetchedData.Invalid}
            tabTitle="Invalid"
            ApiData={getApi}
            apiPaginationHandlers={apiPaginationHandlers}
            onRetrySuccess={fetchData}
            vendorFeePeriodNo={
              vendorFeePeriodNo ? String(vendorFeePeriodNo) : null
            }
          />
        ),
        index: 0,
      },
      {
        title: 'Held',
        component: (
          <ReconcileCard
            key="Held"
            data={fetchedData.Held}
            tabTitle="Held"
            ApiData={getApi}
            apiPaginationHandlers={apiPaginationHandlers}
            onRetrySuccess={fetchData}
            vendorFeePeriodNo={
              vendorFeePeriodNo ? String(vendorFeePeriodNo) : null
            }
          />
        ),
        index: 1,
      },
      {
        title: 'Archived',
        component: (
          <ReconcileCard
            key="Archived"
            data={fetchedData.Archived}
            tabTitle="Archived"
            ApiData={getApi}
            apiPaginationHandlers={apiPaginationHandlers}
            onRetrySuccess={fetchData}
            vendorFeePeriodNo={
              vendorFeePeriodNo ? String(vendorFeePeriodNo) : null
            }
          />
        ),
        index: 2,
      },
      {
        title: 'Reconciled',
        component: (
          <ReconcileCard
            key="Reconciled"
            data={
              fetchedData.Reconciled.filter(
                (item: ReconcileTableTypes) =>
                  item.reconciledUserName !== null &&
                  item.processStatusName === 'Valid'
              ) || []
            }
            tabTitle="Reconciled"
            ApiData={getApi}
            apiPaginationHandlers={apiPaginationHandlers}
            onRetrySuccess={fetchData}
            vendorFeePeriodNo={
              vendorFeePeriodNo ? String(vendorFeePeriodNo) : null
            }
          />
        ),
        index: 3,
      },
    ],
    [
      fetchedData.Invalid,
      fetchedData.Held,
      fetchedData.Archived,
      fetchedData.Reconciled,
      getApi,
      apiPaginationHandlers,
      fetchData,
      vendorFeePeriodNo,
    ]
  );

  return (
    <Flex
      justifyContent="center"
      flexDirection="column"
      marginTop="10px"
      className="vendorFeeImports"
    >
      <Box>
        <Heading
          padding="0 15px 20px 0"
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          marginTop="-30px"
        >
          <BackButton label="Vendor Fee Reconciliation" />
        </Heading>
      </Box>
      <Box>
        <FinancialsLayout
          defaultIndex={0}
          tabs={tabs}
          vendorFeePeriodNo={
            vendorFeePeriodNo ? String(vendorFeePeriodNo) : null
          }
        />
      </Box>
    </Flex>
  );
};

export default ReconcileTable;
