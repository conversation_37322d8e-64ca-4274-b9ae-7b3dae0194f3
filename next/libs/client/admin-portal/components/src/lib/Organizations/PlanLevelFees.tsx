'use client';
import { Box, Button, Flex, Heading, Input, Text } from '@chakra-ui/react';
import { DownloadFile, GenericModal } from '@next/admin/components';
import { useJavaApi } from '@next/shared/api';
import {
  parseDateWithoutTimezone,
  usePlanlevelFeesHandler,
  useQueryParams,
} from '@next/shared/hooks';
import { useCustomToast } from '@next/shared/hooks';
import { CurrencyFormat } from '@next/shared/hooks';
import { DataTable } from '@next/shared/ui';
import { createColumnHelper } from '@tanstack/react-table';
import { Select } from 'chakra-react-select';
import { format } from 'date-fns';
import moment from 'moment';
import { ChangeEvent, useEffect, useState } from 'react';

interface FeesColumn {
  id: number;
  effectivePeriod: string | null;
  feeAmount: number;
  billed: string;
  action: any;
  planFeeNo: [];
}

export const PlanLevelFees = () => {
  const {
    removeTermination,
    setRemoveTermination,
    Values,
    handleDelete,
    selectedOption,
    handleSelectChange,
    FunctionalStates,
    setValue,
  } = usePlanlevelFeesHandler();

  const columnHelper = createColumnHelper<FeesColumn>();
  const [planFeeNo, setPlanFeeNo] = useState([]);
  const [invoicingList, setInvoicingList] = useState([]);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [filterDates, setFilterDates] = useState([]);
  const [allowInvoiceSelection, setAllowInvoiceSelection] =
    useState<boolean>(false);
  const [createPlanStartPeriod, setCreatePlanStartPeriod] = useState<any>(null);
  const [createPlanStartPeriodOptions, setCreatePlanStartPeriodOptions] =
    useState<Array<any>>([]);

  const {
    planName,
    feeName,
    feeAmount: queryFeeAmount,
    feeTypeName,
    dateEff,
    feeType,
    vendorId,
    planNo,
    feeNo,
  } = useQueryParams([
    'planName',
    'feeName',
    'feeAmount',
    'feeTypeName',
    'dateEff',
    'feeType',
    'vendorId',
    'planNo',
    'feeNo',
  ]);

  const [feeAmount, setFeeAmount] = useState(queryFeeAmount || '0.00');

  useEffect(() => {
    setAllowInvoiceSelection(feeTypeName === 'Lump Sum' || feeType === '6');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [feeTypeName]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        await fetchCreatePlanStartPeriodOptions();
      } catch (error) {
        console.error('Error fetching plan start period options:', error);
      }
    };
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterDates]);

  const handleFeeAmountChange = (event: ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;

    // Allow clearing the value or valid numeric input
    if (inputValue === '' || /^-?\d*(\.\d{0,2})?$/.test(inputValue)) {
      setFeeAmount(inputValue);
    }
  };

  const fetchCreatePlanStartPeriodOptions = async () => {
    const isBillingPeriodMonthList =
      feeType === '4' || feeType === '5' || feeType === '6';

    let billingPeriodList: Array<any> = [];
    let billingPeriodMonthlyList: Array<any> = [];

    if (isBillingPeriodMonthList) {
      billingPeriodMonthlyList = await getApi('membershipBillingPeriod', {
        vendorNo: vendorId,
        frequency: 2,
        showFuturePeriods: 'true',
      });

      billingPeriodList = (billingPeriodMonthlyList || [])
        .filter((item: any) => {
          if (['INVOICED'].includes(item.membershipBillingPeriodStatus)) {
            return false;
          }
          if (!item.dateFrom || !item.dateTo) {
            console.warn(
              `[fetchCreatePlanStartPeriodOptions] Invalid dates:`,
              item
            );
            return false;
          }
          return true;
        })
        .map((item: any) => {
          try {
            const dateFrom = parseDateWithoutTimezone(item.dateFrom);
            const dateTo = parseDateWithoutTimezone(item.dateTo);
            return {
              ...item,
              dateRange: `${format(dateFrom, 'MM/dd/yyyy')} - ${format(
                dateTo,
                'MM/dd/yyyy'
              )}`,
            };
          } catch (error) {
            console.error(
              `[fetchCreatePlanStartPeriodOptions] Error parsing dates:`,
              item,
              error
            );
            return null; // Skip this item
          }
        })
        .filter(Boolean); // Remove null entries
    } else {
      billingPeriodList = (filterDates || []).map((dateRange) => ({
        dateRange: dateRange,
      }));
    }

    billingPeriodList = billingPeriodList.slice(0, 80);

    const options = billingPeriodList.map((item: any) => ({
      label: item.dateRange,
      value: item.dateRange,
    })) as any[];

    setCreatePlanStartPeriodOptions(options);
  };

  const showToast = useCustomToast();

  const columnsFees = [
    columnHelper.accessor('action', {
      header: () => '',
      cell: (info) => {
        return (
          <DownloadFile
            id={info.row.original}
            data={ActionList(info.row.original) as []}
            actionHandler={() => {
              setPlanFeeNo(info.row.original.planFeeNo);
            }}
            placementValue="bottom-start"
          />
        );
      },
      enableSorting: false,
    }),
    columnHelper.accessor('effectivePeriod', {
      header: 'Effective Period',
      enableSorting: false,
    }),
    columnHelper.accessor('feeAmount', {
      header: 'Amount',
      cell: (info) => <Box>{CurrencyFormat(info.getValue())}</Box>,

      enableSorting: false,
    }),
    columnHelper.accessor('billed', {
      header: 'Billed?',
      enableSorting: false,
    }),
  ];

  const ActionList = (data: Record<string, any>) => {
    const effectiveDates = data?.effectivePeriod
      ? data.effectivePeriod.split('-')
      : null;

    if (!effectiveDates) {
      console.warn('Invalid effectivePeriod:', data?.effectivePeriod);
      return [];
    }

    const newList: any = [
      {
        clickHandler: () => {
          if (
            !createPlanStartPeriod &&
            createPlanStartPeriodOptions.length > 0
          ) {
            setCreatePlanStartPeriod(createPlanStartPeriodOptions[0]);
          }
          FunctionalStates('ChangeFeeAmount');
        },
        label: 'Change Fee',
      },
    ];

    if (effectiveDates.length === 1) {
      newList.push({
        clickHandler: () => {
          if (Values.isTermFeeVisible) {
            FunctionalStates('isTermFeeVisible');
          } else {
            FunctionalStates('termAction');
          }
        },
        label: 'Term Fee',
      });
    }

    if (
      effectiveDates.length === 2 &&
      data?.billed === 'No' &&
      PlanFeeData?.[0]?.effectivePeriod === data?.effectivePeriod
    ) {
      newList.push({
        clickHandler: () => {
          setRemoveTermination(true);
        },
        label: 'Remove Termination',
      });
    }
    // Avoid duplicates
    const isTermFeeAlreadyAdded = newList.some(
      (item: any) => item.label === 'Term Fee'
    );

    if (effectiveDates.length === 1 && !isTermFeeAlreadyAdded) {
      newList.push({
        clickHandler: () => {
          if (Values.isTermFeeVisible) {
            FunctionalStates('isTermFeeVisible');
          } else {
            FunctionalStates('termAction');
          }
        },
        label: 'Term Fee',
      });
    }

    if (data?.billed === 'No') {
      newList.push({
        clickHandler: () => {
          handleDelete();
        },
        label: 'Delete Fee',
      });
    }
    return newList;
  };

  const { PlanFeeData, getApi, methodApi } = useJavaApi(['PlanFeeData'], {
    planNo: planNo as string | number,
    feeNo: feeNo as string | number,
  });

  useEffect(() => {
    getApi(
      'planDropdown',
      {
        planNo: planNo,
        feeType: feeTypeName as string,
      },
      {
        onSuccess: (resp) => {
          setFilterDates(resp);
        },
      }
    );
    getApi('invoicingListPlanNo', {
      planNo: planNo,
    }).then((response) => {
      setInvoicingList(response);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleRemoveTerminate = () => {
    const planFeeNos = Array.isArray(planFeeNo) ? planFeeNo : [planFeeNo];
    if (!planFeeNos || planFeeNos.length === 0 || !planFeeNos[0]) {
      console.error('Invalid planFeeNo:', planFeeNo);
      showToast({
        title: 'Invalid plan fee selected.',
        status: 'error',
      });
      return;
    }

    methodApi('planfeesRemoveterm', {
      method: 'POST',
      data: {
        planFeeNos: [planFeeNo],
      },
      onSuccess: () => {
        getApi('PlanFeeData', {
          planNo: planNo as string | number,
          feeNo: feeNo as string | number,
        });
        showToast({
          title: 'Remove Terminate for Plan-Level Fee completed successfully.',
          status: 'success',
        });
      },
      onError: (err) => {
        const errorMsg =
          err?.data?.message || err?.message || 'Something went wrong.';
        showToast({
          title: errorMsg,
          status: 'error',
        });
      },
    });
    setRemoveTermination(false);
  };

  const ApplyPlan = async () => {
    const sanitizedFeeAmount = parseFloat(feeAmount.replace(/,/g, ''));

    if (isNaN(sanitizedFeeAmount)) {
      showToast({
        title: 'Please enter a valid fee amount.',
        status: 'error',
      });
      return;
    }

    methodApi('applyplan', {
      method: 'POST',
      data: {
        planNo: parseInt(planNo),
        feeNo: parseInt(feeNo),
        dateEff: moment(
          (createPlanStartPeriod?.value || '').split(' - ')[0]
        ).format('YYYY-MM-DD'),
        feeAmount: sanitizedFeeAmount,
        invoiceConfigNo: parseFloat(selectedInvoice?.value) || undefined,
      },
      onSuccess: () => {
        getApi('PlanFeeData', {
          planNo: planNo as string | number,
          feeNo: feeNo as string | number,
        });
        showToast({
          title: 'New Plan Fee created successfully.',
          status: 'success',
        });
      },
      onError: (err) => {
        showToast({
          title: err.data.message,
          status: 'error',
        });
      },
    });
  };

  const ChangePlan = async () => {
    const dateEff = selectedOption;
    const period = moment(dateEff, 'MM/DD/YYYY').format('YYYY-MM-DD');
    methodApi('applyChangePlan', {
      method: 'POST',
      data: {
        planFeeNos: [planFeeNo],
        dateEff: period,
        feeAmount: parseFloat(feeAmount?.replace(/,/g, '')),
      },

      onSuccess: () => {
        getApi('PlanFeeData', {
          planNo: planNo as string | number,
          feeNo: feeNo as string | number,
        });
        showToast({
          title: 'Change Plan Fee(s) completed successfully.',
          status: 'success',
        });
      },

      onError: (err) => {
        showToast({
          title: err?.data?.message,
          status: 'error',
        });
      },
    });
  };

  const TerminatePlan = async () => {
    const terminationPeriodMoment = moment(selectedOption, 'MM/DD/YYYY');
    const terminationDate = terminationPeriodMoment
      .subtract(1, 'day')
      .format('YYYY-MM-DD');
    methodApi('removeplanterminate', {
      method: 'POST',
      data: {
        planFeeNos: [planFeeNo],
        dateExp: terminationDate,
      },
      onSuccess: () => {
        getApi('PlanFeeData', {
          planNo: planNo as string | number,
          feeNo: feeNo as string | number,
        });
        showToast({
          title: '- Terminate Plan-Level Fee completed successfully.          ',
          status: 'success',
        });
      },

      onError: () => {
        showToast({
          title: '- Termination Period must be selected.',
          status: 'error',
        });
      },
    });
  };

  const DeletePlan = () => {
    methodApi('deleteplan', {
      method: 'DELETE',
      restParams: { planFeeNo: planFeeNo },
      onSuccess: () => {
        getApi('PlanFeeData', {
          planNo: planNo as string | number,
          feeNo: feeNo as string | number,
        });
        showToast({
          title: 'Deleted',
          status: 'success',
        });
      },
      onError: (error) => {
        showToast({
          title: error.data.message,
          status: 'error',
        });
      },
    });
  };

  const dateEff1 = dateEff ? parseDateWithoutTimezone(dateEff) : undefined;
  const today = new Date();
  const dateEffective =
    dateEff1 && dateEff1 > today ? dateEff1.toLocaleDateString() : null;

  return (
    <Flex
      justifyContent={'center'}
      flexDirection={'column'}
      boxShadow="0 2px 4px rgba(0, 0, 0, 0.2)"
    >
      <Box>
        <Heading
          size="md"
          fontSize={14}
          textAlign="center"
          fontStyle="italic"
          mb={0}
        >
          Plan-Level Fees
        </Heading>

        <Box>
          <Flex justifyContent={'end'} flexDirection={'row'} gap={9} p={4}>
            <Button
              colorScheme="blue"
              variant="solid"
              size="sm"
              onClick={() => FunctionalStates('planModal')}
            >
              Create Plan Fee
            </Button>
          </Flex>
        </Box>
      </Box>

      <Box>
        <DataTable columns={columnsFees} data={PlanFeeData?.reverse()} />
      </Box>

      <GenericModal
        title="Create Plan Fee"
        onClose={() => {
          FunctionalStates('planModal');
          setFeeAmount(queryFeeAmount || '0.00');
        }}
        isOpen={Values.planModal}
      >
        <Box>
          <Text my={2} fontWeight={600}>
            {planName} / {feeName}{' '}
            {dateEffective
              ? `(Eff. ${format(
                  parseDateWithoutTimezone(dateEffective),
                  'yyyy-MM-dd'
                )})`
              : null}
          </Text>
          <Text>Start Period :</Text>
          <Select
            onChange={setCreatePlanStartPeriod}
            value={createPlanStartPeriod}
            options={createPlanStartPeriodOptions}
            size={'sm'}
            maxMenuHeight={250}
            menuPosition="fixed"
            required
            chakraStyles={{
              menu: () => ({ zIndex: 999999 }),
            }}
          />

          <Box width={'100%'}>
            <Box>Amount :</Box>
            <Input
              size={'sm'}
              bgColor={'white'}
              placeholder={'0.00'}
              value={feeAmount}
              onChange={handleFeeAmountChange}
            />

            {allowInvoiceSelection && (
              <Box as="div">
                <Text>Select Invoice:</Text>
                <Select
                  onChange={(newValue: any) => setSelectedInvoice(newValue)}
                  options={
                    invoicingList?.map((item: any) => ({
                      label: item?.name,
                      value: item?.id,
                    })) as any[]
                  }
                  value={selectedInvoice}
                  defaultValue=""
                  size={'sm'}
                  maxMenuHeight={250}
                  menuPosition="fixed"
                  chakraStyles={{
                    container: (provided) => ({
                      ...provided,
                      width: '300px',
                    }),
                    menu: () => ({ zIndex: 999999 }),
                  }}
                />
              </Box>
            )}
            <Flex
              justifyContent={'end'}
              flexDirection={'row'}
              gap={4}
              paddingBlock={'1rem'}
            >
              <Button
                variant="solid"
                size="sm"
                px={4}
                onClick={() => {
                  FunctionalStates('planModal');
                  setValue(feeAmount);
                  setSelectedInvoice(null);
                  setCreatePlanStartPeriod(null);
                }}
              >
                Cancel
              </Button>
              <Button
                colorScheme="blue"
                variant="solid"
                size="sm"
                px={4}
                onClick={() => {
                  ApplyPlan();
                  FunctionalStates('planModal');
                }}
                isDisabled={
                  !(
                    ((allowInvoiceSelection && selectedInvoice) ||
                      !allowInvoiceSelection) &&
                    createPlanStartPeriod &&
                    feeAmount
                  )
                }
              >
                Apply
              </Button>
            </Flex>
          </Box>
        </Box>
      </GenericModal>

      <GenericModal
        title="Change Plan-Level Fee Amount?"
        onClose={() => {
          FunctionalStates('ChangeFeeAmount');
          handleSelectChange([]);
        }}
        isOpen={Values.ChangeFeeAmount}
      >
        <Box m={5}>
          <Text m={5} textAlign={'center'}>
            Enter the new fee amount and the start period.
            <br /> The existing fee will automatically terminate as part of this
            action.
          </Text>
          <Flex justifyContent={'flex-start'} alignItems="center">
            <Text color="red" fontSize="md" lineHeight="1" ml={7}>
              *
            </Text>
            <Text whiteSpace={'nowrap'} ml={1} mr={2}>
              Start Period:
            </Text>
            <Select
              options={createPlanStartPeriodOptions}
              defaultValue=""
              onChange={handleSelectChange}
              value={createPlanStartPeriod}
              size={'sm'}
              maxMenuHeight={250}
              required
              chakraStyles={{
                container: (provided) => ({
                  ...provided,
                  width: '300px',
                }),
                menu: () => ({
                  zIndex: 99999,
                  position: 'fixed',
                  width: '300px',
                }),
              }}
            />
          </Flex>

          <Flex m={5}>
            <Text whiteSpace={'nowrap'} ml={4} mr={2}>
              Fee Amount:
            </Text>
            <Input
              size={'sm'}
              bgColor={'white'}
              placeholder={'0.00'}
              value={feeAmount}
              onChange={(e) => setFeeAmount(e.target.value)}
            />
          </Flex>
        </Box>
        <Flex justifyContent={'end'} flexDirection={'row'} gap={4} p={4}>
          <Button
            colorScheme="blue"
            variant="solid"
            size="sm"
            px={8}
            onClick={() => {
              ChangePlan();
              FunctionalStates('ChangeFeeAmount');
            }}
          >
            Ok
          </Button>
          <Button
            colorScheme="blue"
            variant="solid"
            size="sm"
            px={8}
            onClick={() => {
              FunctionalStates('ChangeFeeAmount');
            }}
          >
            Cancel
          </Button>
        </Flex>
      </GenericModal>

      <GenericModal
        title="Terminate Plan-Level Fee?"
        onClose={() => FunctionalStates('termAction')}
        isOpen={Values.termAction}
      >
        <Box>
          <Text textAlign={'center'}>
            When do you want the fee to end? <br /> The selected period
            indicates the last effective period for which the fee will be
            billed.
          </Text>
          <Box m={5} display={'flex'}>
            <Text m={2}>Termination Period: </Text>
            <Select
              options={
                [
                  { label: 'Select an option', value: 'Select an option' },
                  ...(filterDates ?? []).map((each: string) => ({
                    label: each,
                    value: each,
                  })),
                ] as any[]
              }
              defaultValue=""
              onChange={handleSelectChange}
              size={'sm'}
              maxMenuHeight={150}
              chakraStyles={{
                container: (provided) => ({
                  ...provided,
                  width: '300px',
                }),
              }}
            />
          </Box>
        </Box>
        <Flex justifyContent={'end'} flexDirection={'row'} gap={4} p={4}>
          <Button
            colorScheme="blue"
            variant="solid"
            size="sm"
            px={8}
            onClick={() => {
              TerminatePlan();
              FunctionalStates('termAction');
            }}
          >
            Ok
          </Button>
          <Button
            colorScheme="blue"
            variant="solid"
            size="sm"
            px={8}
            onClick={() => FunctionalStates('termAction')}
          >
            Cancel
          </Button>
        </Flex>
      </GenericModal>

      <GenericModal
        title="Remove Termination Plan-Level Fee?"
        onClose={() => setRemoveTermination(false)}
        isOpen={removeTermination}
      >
        <Flex gap={4} p={4}>
          <Text>Do you wish to remove the termination date for this Fee?</Text>
          <Button
            colorScheme="blue"
            variant="solid"
            size="sm"
            px={8}
            onClick={() => {
              handleRemoveTerminate();
            }}
          >
            OK
          </Button>
          <Button
            colorScheme="blue"
            variant="solid"
            size="sm"
            px={8}
            onClick={() => setRemoveTermination(false)}
          >
            Cancel
          </Button>
        </Flex>
      </GenericModal>

      <GenericModal
        title="Confirmation"
        onClose={() => FunctionalStates('deleteAction')}
        isOpen={Values.deleteAction}
      >
        <Text>Delete the plan level fee?</Text>
        <Flex justifyContent={'end'} flexDirection={'row'} gap={4} p={4}>
          <Button
            colorScheme="blue"
            variant="solid"
            size="sm"
            px={4}
            width={'80px'}
            onClick={() => {
              handleDelete();
              DeletePlan();
            }}
          >
            OK
          </Button>
          <Button
            variant="solid"
            colorScheme="blue"
            size="sm"
            px={4}
            onClick={() => FunctionalStates('deleteAction')}
          >
            Cancel
          </Button>
        </Flex>
      </GenericModal>
    </Flex>
  );
};
