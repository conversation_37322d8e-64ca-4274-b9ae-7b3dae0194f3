import {
  Box,
  Checkbox,
  Divider,
  Flex,
  FormControl,
  FormHelperText,
  FormLabel,
  Input,
  Tooltip,
} from '@chakra-ui/react';
import { theme } from '@next/admin/constants';
import { useEffect } from 'react';
import { FieldError, useFormContext } from 'react-hook-form';
import { AiOutlineQuestionCircle } from 'react-icons/ai';

export const COB = ({ data }: { data: Record<string, any> }) => {
  const {
    register,
    watch,
    setValue,
    formState: { errors },
  } = useFormContext();

  useEffect(() => {
    setValue('isCOBSecondary', data?.isCOBSecondary ?? null, {
      shouldValidate: true,
    });
    setValue('cobSecondaryDate', data?.cobEffectiveDate || '');
    setValue('cobPrimaryDate', data?.cobEffectiveDate || '');
  }, [data, setValue]);

  const isCOBSecondary = watch('isCOBSecondary');

  return (
    <Box>
      <Flex gap={2} style={{ fontWeight: 'bold', margin: '5px' }}>
        <h1> COB Secondary</h1>
        <Tooltip
          label="Claims sent to secondary payers with claims adjudication information included from a prior or primary payer"
          fontSize="md"
          placement="top"
          hasArrow
        >
          <Flex marginTop={'5px'}>
            <AiOutlineQuestionCircle />
          </Flex>
        </Tooltip>
      </Flex>
      <Flex gap={4}>
        <FormControl>
          <Checkbox
            size="md"
            marginTop={5}
            isChecked={isCOBSecondary === true}
            {...register('isCOBSecondary')}
          >
            COB SECONDARY ?
          </Checkbox>
        </FormControl>
        {data?.isCOBMandatory ? (
          isCOBSecondary ? (
            <FormControl isInvalid={!!errors.cobSecondaryDate}>
              <Flex gap={2}>
                <FormHelperText color="red">*</FormHelperText>
                <FormLabel margin={'2px'}>COB Effective Date</FormLabel>
              </Flex>
              <Input
                type="date"
                placeholder="COB Effective Date"
                {...register('cobSecondaryDate', {
                  required: 'COB Effective Date is required',
                })}
                max="9999-12-31"
              />
              {errors.cobSecondaryDate && (
                <FormHelperText color="red">
                  <span>{(errors.cobSecondaryDate as FieldError).message}</span>
                </FormHelperText>
              )}
            </FormControl>
          ) : (
            <FormControl isInvalid={!!errors.cobPrimaryDate}>
              <Flex gap={2}>
                <FormHelperText color="red">*</FormHelperText>
                <FormLabel margin={'2px'}>COB Termination Date</FormLabel>
              </Flex>
              <Input
                type="date"
                placeholder="COB Termination Date"
                {...register('cobPrimaryDate', {
                  required: 'COB Termination Date is required',
                })}
                max="9999-12-31"
              />
              {errors.cobPrimaryDate && (
                <FormHelperText color="red">
                  <span>{(errors.cobPrimaryDate as FieldError).message}</span>
                </FormHelperText>
              )}
            </FormControl>
          )
        ) : (
          isCOBSecondary && (
            <FormControl isInvalid={!!errors.cobSecondaryDate}>
              <Flex gap={2}>
                <FormHelperText color="red">*</FormHelperText>
                <FormLabel margin={'2px'}>COB Effective Date</FormLabel>
              </Flex>
              <Input
                type="date"
                placeholder="COB Effective Date"
                {...register('cobSecondaryDate', {
                  required: 'COB Effective Date is required',
                })}
                max="9999-12-31"
              />
              {errors.cobSecondaryDate && (
                <FormHelperText color="red">
                  <span>{(errors.cobSecondaryDate as FieldError).message}</span>
                </FormHelperText>
              )}
            </FormControl>
          )
        )}
      </Flex>

      <Divider
        my={4}
        width={'100%'}
        borderWidth="1px"
        borderColor={theme.colors.brand.lightgrey}
      />
    </Box>
  );
};
