'use client';
import {
  Box,
  Button,
  Checkbox,
  Flex,
  Modal,
  <PERSON>dalBody,
  ModalCloseButton,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
  Select,
  Text,
  Tooltip,
} from '@chakra-ui/react';
import { VendorFeeHold } from '@next/admin/components';
import { VendorFeeArchive, VendorFeeRestore } from '@next/admin/components';
import { useJavaApi } from '@next/shared/api';
import {
  useCustomToast,
  useDownloadFile,
  useReconcile,
} from '@next/shared/hooks';
import { BenefitPlan, ReconcileTableTypes } from '@next/shared/types';
import { DataTable as Table } from '@next/shared/ui';
import { PaginationHandlers } from '@next/shared/ui';
import AssignFeeButton from 'libs/client/admin-portal/components/src/lib/FinancialManagement/Invoicing/AssignFeeButton';
import { ConfirmationPopup } from 'libs/client/admin-portal/components/src/lib/Global/ConfirmationPopup';
import { FC, useCallback, useEffect, useState } from 'react';

import VendorFeeRelease from './VendorFeeRelease';

interface ReconcileCardProps {
  data: ReconcileTableTypes[];
  tabTitle: string;
  ApiData?: any;
  apiPaginationHandlers: PaginationHandlers;
  onRetrySuccess: () => void;
  vendorFeePeriodNo: string | null;
}

interface Group {
  id: number;
  name: string;
}

const ReconcileCard: FC<ReconcileCardProps> = ({
  data = [],
  tabTitle,
  ApiData,
  apiPaginationHandlers,
  onRetrySuccess,
  vendorFeePeriodNo,
}) => {
  const [recompute, setRecompute] = useState<boolean>(false);
  const [assignGroup, setAssignGroup] = useState(false);
  const [showGroups, setShowGroups] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const handleFileDownload = useDownloadFile();
  const [includeInactiveOrgs, setIncludeInactiveOrgs] = useState<
    boolean | null
  >();

  const [checkedItems, setCheckedItems] = useState<string[]>([]);
  const [benefitPlans, setBenefitPlans] = useState<BenefitPlan[]>([]);
  const [groupList, setGroupList] = useState<Group[]>([]);
  const [selectedBenefit, setSelectedBenefit] = useState<undefined | string>(
    undefined
  );

  const [reconcileData, setReconcileData] = useState<ReconcileTableTypes[]>([]);

  // Centralized refresh function using useCallback
  const refreshData = useCallback(async () => {
    try {
      onRetrySuccess();
    } catch (error) {
      console.error('Error refreshing reconcile data:', error);
      setReconcileData([]); // Set empty array on error
    }
  }, [onRetrySuccess]);

  useEffect(() => {
    refreshData(); // Call refreshData whenever ApiData changes
  }, [refreshData]);

  const { Columns, assignName, vendorItemName, setAssignName } = useReconcile(
    checkedItems,
    setCheckedItems,
    apiPaginationHandlers.currentPage,
    apiPaginationHandlers.pageSize
  );

  const showToast = useCustomToast();
  const { methodApi, getApi } = useJavaApi();
  const resetCheckboxes = () => {
    setAssignName('');
    setCheckedItems([]);
  };

  const handleconfirmationpopup = () => {
    setIsLoading(true);
    const description = vendorFeePeriodNo
      ? `Vendor Fee Period ${vendorFeePeriodNo}`
      : 'all Vendor Fee Periods';
    methodApi('reconcileDataPost', {
      method: 'POST',
      restParams: { vendorFeePeriodNo: vendorFeePeriodNo },
      onSuccess: () => {
        setIsLoading(false);
        setRecompute(false);
        showToast({
          title: 'Retry Successful',
          description: `Retry Completed Successfully for ${description}`,
          status: 'success',
        });
        onRetrySuccess();
      },
      onError: (error) => {
        setIsLoading(false);
        showToast({
          title: error.data.message,
          status: 'error',
        });
      },
    });
  };

  const assignGroupHandler = (includeInactiveOrgs: boolean | null) => {
    setIncludeInactiveOrgs(includeInactiveOrgs);
    setAssignGroup(true);
    getApi(['getGroupList'] as any, {
      vendorFeeImportSchema: assignName,
      includeInactiveOrgs: includeInactiveOrgs
        ? includeInactiveOrgs.toString()
        : 'false',
    }).then((res: Group[]) => {
      setGroupList(res);
    });
  };

  const fetchGroups = (id: string) => {
    const selectedGroup: any[] = groupList.filter(
      (group: any) => group.name === id
    );
    if (selectedGroup.length > 0) {
      getApi('getBenefitPlans', {
        id: selectedGroup[0].id,
        inactiveOrg: includeInactiveOrgs
          ? includeInactiveOrgs.toString()
          : 'false',
      }).then((res) => {
        setBenefitPlans(res);
      });
    }
  };

  const handleDownload = async () => {
    let errorShown = false;

    methodApi('downloadReconcileExcel', {
      method: 'GET',
      restParams: { vendorFeePeriodNo },
      sendType: 'blob',
      onSuccess: (data, headers) => {
        const contentDisposition = headers['content-disposition'];

        const filenameMatch = contentDisposition?.match(/filename="?(.+?)"?$/);
        const fileName = filenameMatch
          ? filenameMatch[1]
          : 'downloaded_file.xlsx';
        handleFileDownload(data, 'spreadsheetml.sheet', fileName);
      },

      onError: (e) => {
        if (!errorShown) {
          errorShown = true;
          showToast({
            title: 'ERROR',
            description: e,
            status: 'error',
          });
        }
      },
    });
  };

  const sendRequest = () => {
    const body = {
      vendorFeeBillingNos: checkedItems,
      benefitPlanNo: selectedBenefit,
    };

    methodApi('assignGroup', {
      method: 'POST',
      data: body,
      onSuccess: async () => {
        try {
          await ApiData('reconcileData', { processStatusCodes: 9 });

          setAssignGroup(false);
          resetCheckboxes();

          showToast({
            title: 'Assign Group Successful',
            status: 'success',
          });

          onRetrySuccess();
        } catch (error) {
          console.error('Error refreshing data:', error);
        }
      },
      onError: (data) => {
        if (data && data.data && data.data.message) {
          showToast({
            title: 'API error',
            status: 'error',
            description: data.data.message,
          });
        }
      },
    });
  };

  // if  "invalidReasonCode": 1, then assign group is not possible
  let isAssignGroupButtonDisabled = true;
  if (checkedItems.length > 0 && data.length > 0) {
    for (const row of data) {
      for (const item of checkedItems) {
        if (row.vendorFeeBillingNo === item) {
          if (row.invalidReasonCode === 1) {
            isAssignGroupButtonDisabled = true;
            break;
          } else if (row.invalidReasonCode !== 1) {
            isAssignGroupButtonDisabled = false;
          }
        }
      }
    }
  }

  // if  "invalidReasonCode": 2, "unknown group", the assign fee button is not possible
  let isAssignFeeButtonDisabled = true;
  if (checkedItems.length > 0 && data.length > 0) {
    for (const row of data) {
      for (const item of checkedItems) {
        if (row.vendorFeeBillingNo === item) {
          if (row.invalidReasonCode === 2) {
            isAssignFeeButtonDisabled = true;
            break;
          } else if (row.invalidReasonCode !== 2) {
            isAssignGroupButtonDisabled = true;
            isAssignFeeButtonDisabled = false;
          }
        }
      }
    }
  }

  const onModalClose = () => {
    setBenefitPlans([]);
    setAssignGroup(false);
    setIncludeInactiveOrgs(false);
    setSelectedBenefit(undefined);
    setGroupList([]);
  };

  return (
    <Box>
      <Box
        as="div"
        padding="15px"
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        bg="white"
      >
        <Box as="div" fontWeight={600}>
          <Box>
            <Flex gap={2}>
              {tabTitle === 'Invalid' && (
                <>
                  <AssignFeeButton
                    checkedItems={checkedItems}
                    assignName={assignName}
                    Apidata={ApiData}
                    vendorItemName={vendorItemName}
                    isAssignFeeButtonDisabled={isAssignFeeButtonDisabled}
                    resetCheckboxes={resetCheckboxes}
                  />
                  <Flex gap={8}>
                    <Tooltip
                      label="Only available to line-items with reason of unknown group"
                      isDisabled={!isAssignGroupButtonDisabled}
                    >
                      <Button
                        size="xs"
                        bg="blackAlpha.50"
                        border="1px solid"
                        borderColor="blackAlpha.100"
                        py={'3.5'}
                        onClick={() => {
                          assignGroupHandler(false);
                        }}
                        isDisabled={isAssignGroupButtonDisabled}
                      >
                        Assign Group
                      </Button>
                    </Tooltip>

                    <VendorFeeHold
                      checkedItems={checkedItems}
                      setCheckedItems={resetCheckboxes}
                      ApiData={ApiData}
                      onRetrySuccess={() => {
                        refreshData(); // Refresh data on retry success
                        onRetrySuccess();
                      }}
                    />
                  </Flex>
                  <VendorFeeArchive
                    checkedItems={checkedItems}
                    setCheckedItems={resetCheckboxes}
                    ApiData={ApiData}
                    onRetrySuccess={() => {
                      refreshData(); // Refresh data on retry success
                      onRetrySuccess();
                    }}
                  />
                </>
              )}
              {tabTitle === 'Archived' && (
                <Flex gap={8}>
                  <VendorFeeRestore
                    checkedItems={checkedItems}
                    setCheckedItems={setCheckedItems}
                    ApiData={ApiData}
                    onRetrySuccess={() => {
                      refreshData(); // Refresh data on retry success
                      onRetrySuccess();
                    }}
                  />
                </Flex>
              )}
              {tabTitle === 'Held' && (
                <Flex gap={8}>
                  <VendorFeeRelease
                    checkedItems={checkedItems}
                    setCheckedItems={setCheckedItems}
                    Apidata={ApiData}
                    onRetrySuccess={() => {
                      refreshData(); // Refresh data on retry success
                      onRetrySuccess();
                    }}
                  />
                </Flex>
              )}
            </Flex>
          </Box>
        </Box>
        <Box>
          {tabTitle !== 'Held' && tabTitle !== 'Archived' ? (
            <Flex gap={2}>
              <Button
                colorScheme="blue"
                variant="solid"
                size="sm"
                px={4}
                onClick={() => {
                  setRecompute(true);
                }}
              >
                Retry
              </Button>
            </Flex>
          ) : tabTitle === 'Archived' && vendorFeePeriodNo && ApiData ? (
            <Button
              colorScheme="blue"
              variant="solid"
              size="sm"
              px={4}
              onClick={handleDownload}
            >
              Download
            </Button>
          ) : null}

          <ConfirmationPopup
            alertBody="Retry Reconciliation?"
            isOpen={recompute}
            onClose={() => setRecompute(false)}
            alertHeader="Confirmation"
            onConfirm={handleconfirmationpopup}
            isLoading={isLoading}
          />
        </Box>
      </Box>
      <Table
        columns={Columns}
        headerBackground="#ECECEC"
        fontSize="sm"
        data={reconcileData.length > 0 ? [...reconcileData] : [...data]} // Use updated reconcile data if available        usePagination={true}
        paginationHandlers={{ ...apiPaginationHandlers }}
        paginationSize={apiPaginationHandlers.pageSize}
        usePagination
      />
      <style>{`
          .vendorFeeImports .chakra-table thead tr > th:last-of-type > div {
            justify-content: flex-start;
          }
        `}</style>
      <Modal isOpen={assignGroup} onClose={onModalClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Assign Group</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Box as="div">
              <Flex marginBottom="15px">
                <Text minWidth="120px">PBM Vendor</Text>
                <Text>{assignName}</Text>
              </Flex>
              <Flex marginBottom="15px">
                <Text minWidth="120px">Organization: </Text>
                {groupList.length > 0 && (
                  <Select
                    size={'sm'}
                    bgColor={'white'}
                    maxWidth="400px"
                    onChange={(e) => {
                      setShowGroups(true);
                      fetchGroups(e.target.value);
                    }}
                    defaultValue={undefined}
                  >
                    <option value={undefined} key={0}>
                      {''}
                    </option>
                    {groupList.map((item: any) => (
                      <option
                        value={item.organizationNo}
                        key={item.organizationNo}
                      >
                        {item.name}
                      </option>
                    ))}
                  </Select>
                )}
              </Flex>
              <Flex>
                <Checkbox
                  mt={-3}
                  pb={5}
                  isChecked={includeInactiveOrgs || false}
                  onChange={(e) => {
                    assignGroupHandler(e.target.checked);
                  }}
                >
                  Show Inactive
                </Checkbox>
              </Flex>
              {showGroups && benefitPlans.length > 0 && (
                <Flex>
                  <Text minWidth="120px">Group Number: </Text>
                  <Select
                    size={'sm'}
                    bgColor={'white'}
                    maxWidth="400px"
                    onChange={(e) => setSelectedBenefit(e.target.value)}
                    defaultValue={undefined}
                  >
                    <option value={undefined} key={0}>
                      {''}
                    </option>
                    {benefitPlans
                      .filter(
                        (plan) =>
                          plan.vendorFeeImportSchema === assignName || null
                      )
                      .map((benefit) => (
                        <option
                          value={benefit.benefitPlanNo}
                          key={benefit.benefitPlanNo}
                        >
                          {benefit.groupNumber}
                        </option>
                      ))}
                  </Select>
                </Flex>
              )}
            </Box>
          </ModalBody>
          <ModalFooter>
            <Button
              colorScheme="blue"
              variant="ghost"
              type={'submit'}
              size="sm"
              marginRight="15px"
              onClick={onModalClose}
            >
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              variant="solid"
              type={'submit'}
              size="sm"
              onClick={() => {
                sendRequest();
              }}
              isDisabled={!selectedBenefit}
            >
              Apply
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};
export default ReconcileCard;
