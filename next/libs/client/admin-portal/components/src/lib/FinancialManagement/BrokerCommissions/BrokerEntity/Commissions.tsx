'use client';
import {
  Button,
  ButtonGroup,
  Checkbox,
  Flex,
  Link,
  Text,
} from '@chakra-ui/react';
import { TableHeaderFilter } from '@next/admin/components';
import { sortItemsCaseInsensitive } from '@next/admin/constants';
import { useJavaApi } from '@next/shared/api';
import { getDefaultDateString } from '@next/shared/helpers';
import { useBroker, useDate } from '@next/shared/hooks';
import { BrokerCommission, FMSNavigation } from '@next/shared/types';
import { DataTable as Table } from '@next/shared/ui';
import { CellContext, createColumnHelper } from '@tanstack/react-table';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

export const Commissions = () => {
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [showInactive, setShowInactive] = useState(false);
  const router = useRouter();
  const params = useParams();
  const { isActiveDates } = useDate();
  const columnHelper = createColumnHelper<BrokerCommission>();
  const commBrokerNo = useMemo(() => {
    if (params?.['id']) {
      return params?.['id']?.[0];
    }
    return '';
  }, [params]);

  const commBrokerName = useMemo(() => {
    if (params?.['id']) {
      return params?.['id']?.[1];
    }
    return '';
  }, [params]);

  const { commBrokerCommissionList: commBrokerCommissionListData = [] } =
    useJavaApi(['commBrokerCommissionList'], {
      commBrokerNo,
    });

  const { brokerEntity } = useBroker(commBrokerNo);

  const commBrokerCommissionList = useMemo(() => {
    return (commBrokerCommissionListData || []).map((record: any) => {
      return {
        ...record,
        dateEff: getDefaultDateString(record.dateEff),
        dateExp: getDefaultDateString(record.dateExp),
      };
    });
  }, [commBrokerCommissionListData]);

  const filteredCommissionList = useMemo(() => {
    return commBrokerCommissionList.filter((comm: BrokerCommission) => {
      const isActive = isActiveDates(comm.dateEff, comm.dateExp);
      return showInactive ? comm : isActive;
    });
  }, [commBrokerCommissionList, isActiveDates, showInactive]);

  const handleCommissionNavigate = (
    info: CellContext<BrokerCommission, string>
  ) => {
    const orgParams = new URLSearchParams();
    const bobParams = new URLSearchParams();
    switch (info.row.original.scope) {
      case 'Organization':
        orgParams.set('id', commBrokerNo?.toString() || '');
        orgParams.set('brokerName', commBrokerName || '');
        orgParams.set('edit', 'true');
        orgParams.set('orgEditable', info.row.original.editable.toString());
        orgParams.set(
          'commBrokerAssignmentNo',
          info.row.original.commBrokerAssignmentNo.toString()
        );
        orgParams.set('orgId', info.row.original.organizationNo.toString());
        orgParams.set('returnTo', window.location.pathname);
        router.push(
          `${
            FMSNavigation.VIEW_ORGANIZATION_COMMISSIONS
          }?${orgParams.toString()}`
        );
        break;
      case 'Book of Business':
        bobParams.set('id', commBrokerNo?.toString() || '');
        bobParams.set('edit', 'true');
        bobParams.set(
          'commBookOfBusinessNo',
          info.row.original.commBookOfBusinessNo.toString()
        );
        bobParams.set(
          'commConfigNo',
          info.row.original.commConfigNo.toString()
        );
        bobParams.set('returnTo', window.location.pathname);

        router.push(`${FMSNavigation.VIEW_BOB_COMMISSIONS}?${bobParams}`);
        break;
      default:
        return;
    }
  };

  useEffect(() => {
    if (
      !initialLoadComplete &&
      filteredCommissionList.length === 0 &&
      commBrokerCommissionList.length
    ) {
      setShowInactive(true);
      setInitialLoadComplete(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialLoadComplete, filteredCommissionList]);

  const FilterHeader = ({
    label,
    columnKey,
  }: {
    label: string;
    columnKey: any;
  }) => {
    return (
      <TableHeaderFilter
        triggerElement={<Text>{label}</Text>}
        column={columnKey}
      />
    );
  };

  const tableColumns: any = [
    columnHelper.accessor('organizationName', {
      cell: (info) => {
        if (info.getValue())
          return (
            <Text>{`${info.row.original.organizationName} / ${info.row.original.planName}`}</Text>
          );
      },
      header: (header) => (
        <FilterHeader label={'Organization/Plan'} columnKey={header.column} />
      ),
    }),
    columnHelper.accessor('scope', {
      cell: (info) => info.getValue(),
      header: (header) => (
        <FilterHeader label={'Scope'} columnKey={header.column} />
      ),
    }),
    columnHelper.accessor('commConfigName', {
      cell: (info) => {
        if (info.row.original.scope === 'Organization') {
          if (info.row.original.commConfigNo) {
            return (
              <Link
                onClick={() => handleCommissionNavigate(info)}
                color="blue.600"
              >
                {info.getValue()}
              </Link>
            );
          }
          return (
            <Link
              onClick={() => handleCommissionNavigate(info)}
              color="blue.600"
            >
              {info.row.original.commConfigNo
                ? info.getValue()
                : 'No Commission'}
            </Link>
          );
        } else {
          return (
            <Link
              onClick={() => handleCommissionNavigate(info)}
              color="blue.600"
            >
              {info.getValue()}
            </Link>
          );
        }
      },
      header: (header) => (
        <FilterHeader label={'Commission Name'} columnKey={header.column} />
      ),
      sortingFn: (prev, curr, columnId) => {
        return sortItemsCaseInsensitive(prev, curr, columnId);
      },
    }),
    columnHelper.accessor('editable', {
      cell: (info) => (info.getValue() ? 'Yes' : 'No'),
      header: () => (
        <Text position="relative" top="8px">
          Editable
        </Text>
      ),
    }),
    columnHelper.accessor('brokerName', {
      cell: (info) => info.getValue(),
      header: () => (
        <Text position="relative" top="8px">
          Broker Name
        </Text>
      ),
    }),
    columnHelper.accessor('dateEff', {
      header: () => (
        <Text position="relative" top="8px">
          Effective Date
        </Text>
      ),
    }),
    columnHelper.accessor('dateExp', {
      header: () => (
        <Text position="relative" top="8px">
          Expiration Date
        </Text>
      ),
    }),

    columnHelper.display({
      id: 'actions',
      header: (header) => (
        <Button
          size="xs"
          sx={{
            backgroundColor: 'white',
            borderRadius: '5px',
            float: 'right',
            border: '1px solid',
            borderColor: 'blue.700',
            color: 'blue.700',
          }}
          onClick={async () => {
            await router.replace(window.location.pathname);
            header.table.resetSorting();
            header.table.resetColumnFilters();
          }}
        >
          Reset
        </Button>
      ),
    }),
  ];

  const TableHeader = () => (
    <Flex justifyContent="space-between">
      <Flex flexDirection="column" gap={1}>
        <Text fontWeight="medium">
          Commissions ({filteredCommissionList.length})
        </Text>
        <Checkbox
          size="sm"
          isChecked={showInactive}
          onChange={(e) => setShowInactive(e.target.checked)}
        >
          Show Inactive
        </Checkbox>
      </Flex>
      <ButtonGroup>
        <Button
          color="white"
          backgroundColor="#3182CE"
          onClick={() => {
            const params = new URLSearchParams();
            params.set('id', commBrokerNo?.toString() || '');
            params.set(
              'brokerName',
              brokerEntity?.legalName
                ? brokerEntity?.legalName
                : brokerEntity?.name || ''
            );
            params.set('returnTo', window.location.pathname);
            router.push(
              `${FMSNavigation.VIEW_ORGANIZATION_COMMISSIONS}?${params}`
            );
          }}
        >
          Add Organization Commission
        </Button>
        <Button
          color="white"
          backgroundColor="#3182CE"
          onClick={() => {
            const params = new URLSearchParams();
            params.set('id', commBrokerNo?.toString() || '');
            params.set('brokerName', commBrokerName || '');
            params.set('returnTo', window.location.pathname);

            router.push(`${FMSNavigation.CREATE_BOB_COMMISSIONS}?${params}`);
          }}
        >
          Add Book of Business Commission
        </Button>
      </ButtonGroup>
    </Flex>
  );

  return (
    <Table
      headerText={<TableHeader />}
      containerStyles={{
        height: 'calc(100% - 140px)',
      }}
      usePagination
      paginationSize={10}
      headerBackground="#ececec"
      columns={tableColumns}
      data={filteredCommissionList}
      rowClick={() => undefined}
    />
  );
};
