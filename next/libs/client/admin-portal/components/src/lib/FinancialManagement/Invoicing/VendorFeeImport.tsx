'use client';
import { <PERSON>, Button, Checkbox, Flex, Text } from '@chakra-ui/react';
import { TableHeaderFilter } from '@next/admin/components';
import { useJavaApi } from '@next/shared/api';
import { PermissionRoleContext } from '@next/shared/contexts';
import { getDefaultDateString } from '@next/shared/helpers';
import { VendorFeeImport } from '@next/shared/types';
import { DataTable as Table } from '@next/shared/ui';
import { createColumnHelper } from '@tanstack/react-table';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';

const columnHelper = createColumnHelper<VendorFeeImport>();

export const VendorFeeImportTable = () => {
  const { vendorfeeperiods: vendorfeeperiodsData } = useJavaApi([
    'vendorfeeperiods',
  ]);

  const vendorfeeperiods = useMemo(() => {
    return (vendorfeeperiodsData || []).map((record: VendorFeeImport) => {
      return {
        ...record,
        fileDate: getDefaultDateString(record.fileDate),
      };
    });
  }, [vendorfeeperiodsData]);

  const [vendorFeeImports, setVendorFeeImports] =
    useState<VendorFeeImport[]>(vendorfeeperiods);
  const [, setReconcileTableOpen] = useState(false);
  const router = useRouter();
  const originalImports = useRef([]);

  const { canViewWithPerm } = useContext(PermissionRoleContext);

  const handleNavigateToDashboard = () => {
    router.push('/financials/invoicing/reconcile?activeTab=0');
    setReconcileTableOpen(true);
  };

  const navigateToImport = (query: VendorFeeImport) => {
    router.push(
      `/financials/invoicing/import-vendor-file?vendorFeePeriodNo=${query?.vendorFeePeriodNo}&month=${query.month}&fileName=${query?.fileName}&vendorFeeImportSchema=${query?.vendorFeeImportSchema}`
    );
  };

  const Columns = [
    columnHelper.accessor('vendorName', {
      header: (header) => (
        <TableHeaderFilter
          triggerElement={<Text>Vendor</Text>}
          column={header.column}
        />
      ),
    }),
    columnHelper.accessor('monthLabel', {
      cell: (info) => {
        return (
          <Text minHeight="35px" display="flex" alignItems="center">
            {info.getValue().replace('-', ' ')}
          </Text>
        );
      },
      header: (header) => (
        <TableHeaderFilter
          triggerElement={<Text>Month</Text>}
          column={header.column}
        />
      ),
    }),
    columnHelper.accessor('fileName', {
      header: () => <span>File Name</span>,
      enableSorting: false,
    }),
    columnHelper.accessor('fileDate', {
      header: () => <span>Import Date</span>,
      enableSorting: false,
    }),
    columnHelper.accessor('invoiced', {
      cell: (info) => (
        <Box
          textAlign="right"
          display="flex"
          justifyContent="flex-end"
          paddingRight="5px"
        >
          <Text marginRight="48px">{info.getValue() ? 'Yes' : 'No'}</Text>
          <Text
            color="#3f618f"
            fontWeight={500}
            _hover={{ textDecoration: 'underline', cursor: 'pointer' }}
            onClick={() => navigateToImport(info.row.original)}
          >
            View
          </Text>
        </Box>
      ),
      header: (header) => {
        return (
          <Box
            as="div"
            display="flex"
            alignItems="center"
            justifyContent="flex-end"
            textAlign="right"
          >
            <Text position="relative" right="30px">
              Invoiced
            </Text>
            <Button
              size="xs"
              sx={{
                backgroundColor: '#bae7ff',
                borderRadius: '5px',
                float: 'right',
              }}
              onClick={async () => {
                await router.replace(window.location.pathname);
                header.table.resetSorting();
                header.table.resetColumnFilters();
              }}
            >
              Reset
            </Button>
          </Box>
        );
      },
      enableSorting: false,
    }),
  ];

  const handleInvoiceCheckbox = (value: boolean) => {
    if (value) {
      setVendorFeeImports(originalImports.current);
    } else {
      setVendorFeeImports(
        vendorfeeperiods.filter(
          (vendor: VendorFeeImport) => vendor.invoiced === 0
        )
      );
    }
  };

  useEffect(() => {
    if (vendorfeeperiods && vendorfeeperiods.length > 0) {
      originalImports.current = vendorfeeperiods;
      setVendorFeeImports(
        vendorfeeperiods.filter(
          (vendor: VendorFeeImport) => vendor.invoiced === 0
        )
      );
    }
  }, [vendorfeeperiods]);

  return (
    <Flex
      justifyContent={'center'}
      flexDirection={'column'}
      marginTop="10px"
      className="vendorFeeImports"
    >
      <Box
        as="div"
        padding="15px"
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        bg="white"
      >
        <Box as="div" fontWeight={600}>
          <Text>Vendor Fee Import List</Text>
          <Checkbox
            ml="8px"
            size="sm"
            color="#949494"
            marginLeft="0"
            onChange={(e) => {
              handleInvoiceCheckbox(e.target.checked);
            }}
          >
            Show Invoiced
          </Checkbox>
        </Box>
        <Flex direction="row-reverse">
          {canViewWithPerm('editInvoicing') && (
            <>
              <Link href="/financials/invoicing/import-vendor-file">
                <Button
                  color="#fff"
                  fontSize="18px"
                  fontWeight={600}
                  lineHeight="28px"
                  background="#1c75b9"
                  padding="0 24px"
                  marginLeft="30px"
                >
                  Import File
                </Button>
              </Link>

              <Button type="button" onClick={handleNavigateToDashboard}>
                Reconcile
              </Button>
            </>
          )}
        </Flex>
      </Box>
      <Table
        columns={Columns}
        data={vendorFeeImports}
        headerBackground="#ececec"
        fontSize="sm"
        rowClick={() => undefined}
        paginationSize={20}
        usePagination
      />
      <style>{`
        .vendorFeeImports .chakra-table thead tr > th:last-of-type > div {
          justify-content: flex-end;
        }
      `}</style>
    </Flex>
  );
};
