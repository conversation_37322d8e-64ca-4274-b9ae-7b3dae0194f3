import { useUser } from '@auth0/nextjs-auth0/client';
import { Flex } from '@chakra-ui/react';
import { Block, TransferList } from '@next/shared/ui';
import { FC, useEffect, useState } from 'react';

type secureOrgs = {
  benefitGroups: [];
  brokerFirmId: string;
  brokerOrgId: string;
  brokerParentId: string;
  name: string;
  organizationNo: number;
  restrictBgGroups: boolean;
  selected: boolean;
}[];

type SecuredAccessProps = {
  secureAccessOrgs?: secureOrgs;
  disabled?: boolean;
  setUserSecuredOrgs: (items: any[]) => void;
  role: string;
  userInfo?: any;
};

const SecuredAccess: FC<SecuredAccessProps> = ({
  secureAccessOrgs,
  disabled,
  setUserSecuredOrgs,
  role,
  userInfo,
}) => {
  const { user } = useUser();
  const [selectedSecureOrgs, setSelectedSecureOrgs] = useState<any[]>();
  const [unSelectedSecureOrgs, setUnSelectedSecureOrgs] = useState<any[]>();

  useEffect(() => {
    if (Array.isArray(secureAccessOrgs)) {
      const selectedOrgs = secureAccessOrgs.filter(
        (data) => data.selected === true
      );
      const unSelectedOrgs = secureAccessOrgs.filter(
        (data) => data.selected === false
      );

      setSelectedSecureOrgs(selectedOrgs);
      setUnSelectedSecureOrgs(unSelectedOrgs);
      setUserSecuredOrgs(secureAccessOrgs);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [secureAccessOrgs]);

  const updateParentWithCombinedOrgs = (selected: any[], unSelected: any[]) => {
    const orgMap = new Map();

    unSelected.forEach((org) => {
      orgMap.set(org.organizationNo, { ...org, selected: false });
    });

    selected.forEach((org) => {
      orgMap.set(org.organizationNo, { ...org, selected: true });
    });

    const combinedOrgs = Array.from(orgMap.values());
    setUserSecuredOrgs(combinedOrgs);
  };

  const selfUser = () => {
    if (user && userInfo) {
      return (
        user?.email?.trim().toLowerCase() ===
        userInfo?.username?.trim().toLowerCase()
      );
    }
    return false;
  };

  if (!role) return null;
  return (
    <Block title="Secured Access">
      <Flex flexDirection="column" width="100%">
        <TransferList
          search
          title="Organizations"
          dataKey="name"
          selected={selectedSecureOrgs || []}
          unSelected={unSelectedSecureOrgs || []}
          setSelected={(data: any) => {
            const dataArray = Array.isArray(data) ? data : Object.values(data);
            const updatedSelected = dataArray.map((e: any) => ({
              ...e,
              selected: true,
            }));
            setSelectedSecureOrgs(updatedSelected);

            const selectedOrgNos = new Set(
              updatedSelected.map((org) => org.organizationNo)
            );
            const remainingUnselected = (unSelectedSecureOrgs ?? []).filter(
              (org) => !selectedOrgNos.has(org.organizationNo)
            );

            setUnSelectedSecureOrgs(remainingUnselected);
            updateParentWithCombinedOrgs(updatedSelected, remainingUnselected);
          }}
          setUnSelected={(data: any) => {
            const dataArray = Array.isArray(data) ? data : Object.values(data);
            const updatedUnSelected = dataArray.map((e: any) => ({
              ...e,
              selected: false,
            }));
            setUnSelectedSecureOrgs(updatedUnSelected);

            const unselectedOrgNos = new Set(
              updatedUnSelected.map((org) => org.organizationNo)
            );
            const remainingSelected = (selectedSecureOrgs ?? []).filter(
              (org) => !unselectedOrgNos.has(org.organizationNo)
            );

            setSelectedSecureOrgs(remainingSelected);
            updateParentWithCombinedOrgs(remainingSelected, updatedUnSelected);
          }}
          style={{ width: '100%' }}
          disabled={disabled || selfUser()}
        />
      </Flex>
    </Block>
  );
};

export default SecuredAccess;
