'use client';
import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogCloseButton,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Box,
  Button,
  createStandaloneToast,
  Flex,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalFooter,
  ModalHeader,
  ModalOverlay,
  Spacer,
  useDisclosure,
} from '@chakra-ui/react';
import { useJavaApi } from '@next/shared/api';
import {
  useLocalStorage,
  useRouteChangeValidatorOnFormUpdate,
} from '@next/shared/hooks';
import { FMSNavigation } from '@next/shared/types';
import { FormFields } from '@next/shared/ui';
import { usePathname, useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { getFormItems } from './FormItems';
import { getModifiedTierTableRowData } from './utils/getTierTableRowToAmount';
import { getFormDataForAPI } from './utils/processFormData';

const { toast } = createStandaloneToast();

export const AddEditStandardCommissionsLanding = () => {
  const router = useRouter();
  const { methodApi, getApi } = useJavaApi([]);
  const formInstance = useForm();
  const pathName = usePathname();
  const searchParams = useSearchParams();
  const [commissionGlAccounts, setCommissionGlAccounts] = useState<Array<any>>(
    []
  );
  const cancelRef = useRef(null);
  const {
    isOpen: warningIsOpen,
    onOpen: onWarningOpen,
    onClose: onWarningClose,
  } = useDisclosure();
  const [isLoading, setIsLoading] = useState(false);
  const [initialFormValue, setInitialFormValue] = useState<any>();
  const { isCommissionUsed } = initialFormValue || {};
  const initialFormState = useRef({});
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [pendingFormData, setPendingFormData] = useState(null);
  const [updatePromiseResolve, setUpdatePromiseResolve] = useState<
    unknown | null | string
  >(null);
  const usages: any = searchParams?.get('usages');

  //   const { isFormChanged } = useRouteChangeValidatorOnFormUpdate({
  //     initialFormValue,
  //     formInstance,
  //   });
  const { watch } = formInstance;
  const { setLocalStorageItem } = useLocalStorage();
  const calculationTypeValue = watch('calculationType');
  const rateTypeValue = watch('rateType');
  const calculationFrequencyValue = watch('calculationFrequency');
  const paymentFrequencyValue = watch('paymentFrequency');
  const commissionId = useMemo(() => {
    if (!pathName?.includes('edit')) {
      return '';
    }
    const splitPaths = pathName.split('/');
    return splitPaths[splitPaths.length - 2];
  }, [pathName]);
  const isEditMode = !!commissionId;

  const { isForceRouteChange } = useRouteChangeValidatorOnFormUpdate({
    initialFormValue,
    formInstance,
  });

  useEffect(() => {
    if (isEditMode) {
      initialFormState.current = formInstance.getValues();
    }
    getCommissionGlAccounts();
    getStandardCommissionDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formInstance]);

  const getCommissionGlAccounts = async () => {
    const response = await getApi('commissionGlAccounts');
    setCommissionGlAccounts(response);
  };

  const getStandardCommissionDetails = async () => {
    if (!isEditMode) {
      setInitialFormValue({});
      return;
    }
    setIsLoading(true);
    try {
      const standardCommissionDetails = await getApi(
        'standardCommissionAccount',
        { id: commissionId }
      );
      const response = await getApi('standardCommissionEditable', {
        id: commissionId,
      });

      standardCommissionDetails.tiers =
        standardCommissionDetails.tiers &&
        getModifiedTierTableRowData(standardCommissionDetails.tiers);
      standardCommissionDetails.isCommissionUsed = !response.editable;
      formInstance.reset(standardCommissionDetails as any);
      setInitialFormValue(standardCommissionDetails);
      setIsLoading(false);
    } catch (e) {
      toast({
        title: 'Error!',
        description: 'Error in loading commission data',
        status: 'error',
        duration: 3000,
      });
    }
  };
  const commissionGlAccountOptions = useMemo(() => {
    return commissionGlAccounts
      ?.sort(
        (
          { commTypeNo: commTypeNo1 }: any,
          { commTypeNo: commTypeNo2 }: any
        ) => {
          return commTypeNo2 > commTypeNo1 ? -1 : 1;
        }
      )
      .map((commission: any) => {
        return {
          value: commission.commTypeNo,
          label: `${commission.commTypeNo} - ${commission.name}`,
        };
      });
  }, [commissionGlAccounts]);

  const formItems = useMemo(
    () =>
      getFormItems({
        calculationTypeValue,
        rateTypeValue,
        calculationFrequencyValue,
        paymentFrequencyValue,
        commissionGlAccountOptions,
        isLoading,
        isCommissionUsed,
      }),
    [
      calculationTypeValue,
      rateTypeValue,
      calculationFrequencyValue,
      paymentFrequencyValue,
      commissionGlAccountOptions,
      isLoading,
      isCommissionUsed,
    ]
  );
  const gotoListPage = (ignoreFormCheck: any) => {
    sessionStorage.setItem('formSubmitted', 'true');
    isForceRouteChange.current = ignoreFormCheck === true;
    if (searchParams?.get('custom')) {
      const fromUrl = searchParams?.get('from') || '';
      const idParam = searchParams?.get('id');
      const brokerName = searchParams?.get('brokerName');
      const separator = fromUrl.includes('?') ? '&' : '?';
      router.replace(
        `${fromUrl}${separator}id=${idParam}&brokerName=${brokerName}`
      );
    } else {
      router.push(`${FMSNavigation.BROKER_ENTITIES}?activeTab=1`);
    }
  };

  const handleCancel = () => {
    if (initialFormState.current) {
      formInstance.reset(initialFormState.current);
    }
    if (isEditMode) {
      gotoListPage(false);
    } else {
      gotoListPage(true);
    }
  };

  const createStandardCommission = async (formData: any) => {
    const data = {
      ...getFormDataForAPI({ formData, rateTypeValue, formItems }),
      sharedConfigIndicator: searchParams?.get('custom') ? 0 : 1,
    };

    await methodApi('addUpdateStandardCommissionAccount', {
      data,
      notificationTitle: searchParams?.get('custom')
        ? 'Created Standard Commission'
        : 'Created Custom Commission Assignment',
      method: 'POST',
      onSuccess: (result) => {
        if (searchParams?.get('custom')) {
          setLocalStorageItem('custom_commission_no', {
            customCommConfig: result.commConfigNo,
          });
        }
      },
    });
  };

  const showUpdateToast = (
    title: string,
    description: string,
    status: any | undefined,
    duration = 3000
  ) => {
    setTimeout(() => {
      toast({ title, description, status: status, duration });
    }, 1000);
  };

  const resetFormAndNavigate = (cancel: boolean) => {
    setPendingFormData(null);
    onClose();
    if (!cancel) {
      gotoListPage(true);
    }
  };

  const handleUpdateApiCall = async (data: any) => {
    try {
      await methodApi('addUpdateStandardCommissionAccount', {
        data,
        notificationTitle: searchParams?.get('custom')
          ? 'Update Standard Commission'
          : 'Update Custom Commission Assignment',
        method: 'POST',
      });
      showUpdateToast(
        'Success!',
        'Successfully Updated Custom Commission!',
        'success'
      );
    } catch (error) {
      console.error('Error updating commission assignment:', error);
      showUpdateToast(
        'Error',
        'There was an issue updating the commission assignment. Please try again.',
        'error'
      );
    }
  };

  const handleConfirmUpdate = async () => {
    if (!pendingFormData) return;

    const data = {
      ...getFormDataForAPI({
        formData: pendingFormData,
        rateTypeValue,
        formItems,
      }),
      sharedConfigIndicator: initialFormValue.sharedConfigIndicator,
      commConfigNo: initialFormValue.commConfigNo,
      versionNumber: initialFormValue.versionNumber,
    };

    await handleUpdateApiCall(data);

    if (updatePromiseResolve === null) {
      setUpdatePromiseResolve('confirmed');
    }

    resetFormAndNavigate(false);
  };

  const handleCancelUpdate = () => {
    showUpdateToast(
      'Updated Not Saved',
      'The update was not saved.',
      'warning'
    );
    getStandardCommissionDetails();
    setUpdatePromiseResolve('cancelled');
    resetFormAndNavigate(true);
  };

  const updateStandardCommission = async (formData: any) => {
    setPendingFormData(formData);
    onOpen();

    return new Promise((resolve) => {
      setUpdatePromiseResolve(() => resolve);
    });
  };

  const onSubmitForm = async (formData: any) => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      if (!isEditMode) {
        await createStandardCommission(formData);
      } else {
        await updateStandardCommission(formData);
      }

      const toastTitle = searchParams?.get('custom')
        ? isEditMode
          ? 'Edit Custom Commission'
          : 'Create Custom Commission'
        : isEditMode
        ? 'Edit Standard Commission'
        : 'Create Standard Commission';

      showUpdateToast(toastTitle, 'Successfully Saved Commission!', 'success');
      setInitialFormValue(formData);
      setUpdatePromiseResolve(null);
      resetFormAndNavigate(false);
    } catch (error) {
      console.error('Error handling the form submission:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const showToastMessage = () => {
    setTimeout(() => {
      toast({
        title: 'Delete Standard Commission',
        description: 'success!',
        status: 'success',
        duration: 3000,
      });
    }, 2000);
  };

  const onDelete = async () => {
    setIsLoading(true);
    try {
      await methodApi('updateDeleteStandardCommissionAccount', {
        method: 'DELETE',
        notificationTitle: 'Delete Standard Commission',
        restParams: { id: commissionId },
      });
      onWarningClose();
      gotoListPage(true);
      showToastMessage();
    } catch (e) {
      // Do nothing
    } finally {
      setIsLoading(false);
    }
  };

  const renderDeleteConfirmation = () => {
    return (
      <AlertDialog
        leastDestructiveRef={cancelRef}
        onClose={onWarningClose}
        isOpen={warningIsOpen}
        isCentered
        closeOnOverlayClick={false}
        closeOnEsc={false}
      >
        <AlertDialogOverlay />

        <AlertDialogContent>
          <AlertDialogHeader>Confirm Removal</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to delete this record?
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button ref={cancelRef} onClick={onWarningClose}>
              No
            </Button>
            <Button
              colorScheme="red"
              ml={3}
              onClick={onDelete}
              isDisabled={isLoading}
            >
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  return (
    <>
      {/*  @ts-ignore - Ignored this due to the error from the react hook form */}
      <FormProvider {...formInstance}>
        <form onSubmit={formInstance.handleSubmit(onSubmitForm)}>
          <Box>
            <FormFields formItems={formItems} />
          </Box>
          <Flex minWidth="max-content" mt={'20px'}>
            {isEditMode && (
              <Button
                variant="solid"
                size="sm"
                color="#FFFFFF"
                onClick={onWarningOpen}
                isDisabled={isLoading || usages > 0}
                backgroundColor="#f9423b"
              >
                Delete
              </Button>
            )}
            <Spacer />
            <Flex alignItems="center" gap="2">
              <Button
                variant="solid"
                type={'button'}
                size="sm"
                onClick={handleCancel}
                isDisabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                colorScheme="blue"
                variant="solid"
                type={'submit'}
                size="sm"
                isDisabled={isLoading}
              >
                Apply
              </Button>
            </Flex>
            <Modal isOpen={isOpen} onClose={handleCancelUpdate}>
              <ModalOverlay />
              <ModalContent>
                <ModalHeader>Confirm Update</ModalHeader>
                <ModalCloseButton />
                <ModalBody>
                  Changes cannot be undone. Are you sure you want to continue?
                </ModalBody>
                <ModalFooter>
                  <Button variant="ghost" onClick={handleCancelUpdate}>
                    Cancel
                  </Button>
                  <Button
                    colorScheme="blue"
                    mr={3}
                    onClick={handleConfirmUpdate}
                  >
                    Yes
                  </Button>
                </ModalFooter>
              </ModalContent>
            </Modal>
          </Flex>
        </form>
      </FormProvider>
      {renderDeleteConfirmation()}
    </>
  );
};
