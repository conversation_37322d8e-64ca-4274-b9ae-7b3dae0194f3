import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON><PERSON>on,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>er,
  DrawerOverlay,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  Input,
  Select,
  Text,
  Tooltip,
} from '@chakra-ui/react';
import { FILE_UPLOAD_TOOLTIP_MESSAGES, theme } from '@next/admin/constants';
import { useCustomToast } from '@next/shared/hooks';
import React from 'react';
import { useForm } from 'react-hook-form';
import { AiOutlineQuestionCircle } from 'react-icons/ai';

type UploadPrescriberOverrideProps = {
  isOpen: boolean;
  onClose: () => void;
  apiInstance: Record<string, any>;
  E340BIDs: string[] | undefined;
};

export const UploadPrescriberOverride: React.FC<
  UploadPrescriberOverrideProps
> = ({ isOpen, onClose, apiInstance, E340BIDs }) => {
  const {
    handleSubmit,
    register,
    formState: { isSubmitting, errors },
    reset,
    watch,
  } = useForm({
    mode: 'onChange',
  });

  const { methodApi } = apiInstance;
  const showToast = useCustomToast();

  const onSubmit = async (values: any) => {
    if (values.file) {
      const formData = new FormData();
      formData.append('file', values.file[0]);

      showToast({
        title: 'Request Received: Uploading Prescriber Override List',
        status: 'info',
      });

      methodApi('prescriberOverride340b', {
        method: 'POST',
        body: formData,
        restParams: { E340BID: watch('E340BID') },
        sendType: 'multipart/form-data',
        onSuccess: () => {
          showToast({
            title: 'File Uploaded Successfully',
            status: 'success',
          });
        },
        onError(res: Record<string, any>) {
          showToast({
            title: res?.detail?.split(';')?.[1]?.trim() || 'Failed to Upload',
            status: 'error',
          });
        },
      });
    }

    reset();
    onClose();
  };

  return (
    <Drawer isOpen={isOpen} placement="right" onClose={onClose} size={'lg'}>
      <DrawerOverlay />
      <DrawerContent>
        <DrawerHeader
          borderBottom={`1px solid ${theme.colors.brand.lightgray}`}
        >
          <DrawerCloseButton />
          Upload File
        </DrawerHeader>

        <DrawerBody background={theme.colors.brand.lightestgray}>
          <Flex
            backgroundColor={theme.colors.brand.white}
            borderRadius={6}
            boxShadow="rgba(0, 0, 0, 0.12) 0px 4px 4px"
            padding={4}
          >
            <form
              onSubmit={handleSubmit((values) => onSubmit(values))}
              style={{ width: '100%' }}
            >
              <Flex alignItems="center" gap={8} flexDirection={'column'}>
                <FormControl flex={6} isInvalid={!!errors.file}>
                  <HStack marginBottom={2} spacing={1} alignItems={'center'}>
                    <FormLabel margin={0} htmlFor="name">
                      <Text as="span" marginRight={2} color="red">
                        *
                      </Text>
                      File Upload
                    </FormLabel>
                    <Tooltip
                      label={FILE_UPLOAD_TOOLTIP_MESSAGES.upload}
                      hasArrow
                      placement="top"
                      bg={theme.colors.brand.black}
                      borderRadius={5}
                    >
                      <span>
                        <AiOutlineQuestionCircle size={18} />
                      </span>
                    </Tooltip>
                  </HStack>
                  <Input
                    id="file"
                    type="file"
                    _hover={{
                      border: `1px solid #3182ce`,
                    }}
                    {...register('file')}
                  />
                  <FormErrorMessage>
                    {errors && 'File is required'}
                  </FormErrorMessage>
                </FormControl>
                <FormControl flex={6} isInvalid={!!errors.E340BIDs}>
                  <HStack marginBottom={2} spacing={1} alignItems={'center'}>
                    <FormLabel margin={0}>
                      <Text as="span" marginRight={2} color="red">
                        *
                      </Text>
                      340B ID
                    </FormLabel>
                  </HStack>
                  <Flex gap={'2'} flexDir={'column'}>
                    <Select
                      placeholder="Select option"
                      id="E340BID"
                      {...register('E340BID', {
                        required: true,
                      })}
                    >
                      {E340BIDs?.map((id, index) => (
                        <option key={index} value={id}>
                          {id}
                        </option>
                      ))}
                    </Select>
                  </Flex>

                  <FormErrorMessage>
                    {errors && '340B ID is required'}
                  </FormErrorMessage>
                </FormControl>
              </Flex>

              <Divider marginY={4} />

              <Flex alignItems="center" justifyContent="flex-end" mt={4}>
                <ButtonGroup spacing="4">
                  <Button
                    onClick={() => {
                      reset();
                      onClose();
                    }}
                    variant="outline"
                    type="reset"
                    colorScheme="blue"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="solid"
                    isLoading={isSubmitting}
                    type="submit"
                    colorScheme="blue"
                    isDisabled={!(watch('file') && watch('file')?.length > 0)}
                  >
                    Upload
                  </Button>
                </ButtonGroup>
              </Flex>
            </form>
          </Flex>
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  );
};
