export type PortalApi = {
  [key in ROUTES]?: any;
};

type ROUTES =
  | keyof typeof GET_ROUTES
  | keyof typeof METHOD_ROUTES
  | keyof typeof PERMISSIONS_GET;

export type RequestBody = {
  sendType?:
    | 'application/x-www-form-urlencoded'
    | 'multipart/form-data'
    | 'blob';
  method?: 'POST' | 'GET' | 'PATCH' | 'DELETE' | 'PUT';
  body?: any;
  restParams?: Record<string, any>;
  onSuccess?: (res: any) => void;
  onError?: (res: any) => void;
  refetch?: { time: number }; //in seconds -- WARNING: probably will not work if key is duplicated in GET and METHOD routes, will fix in future version - KB
  returnHeaders?: boolean;
};

// TO ADD ROUTE. JUST ADD TO ENUM FOR SERVICE
// TO ADD SERVICE CREATE NEW ENUM AND ADD TO GET OR METHOD RESPECTIVELY

//******************GET*********************
export enum EVALUATE_GET {
  evalGetOpps = 'evaluate-service/v1/opps?sfid={sfOppId}&employer={employer}&broker={broker}&fa={fa}&owner={owner}&spa={spa}&scope={scope}&status={sfStage}&limit={limit}&offset={offset}',
  evalGetOppsNoSF = 'evaluate-service/v1/opps?employer={employer}&broker={broker}&fa={fa}&owner={owner}&spa={spa}&scope={scope}&status={sfStage}&limit={limit}&offset={offset}',
  evalGetOppByID = 'salesforce-service/v1/opportunities/{SFID}',
  evalGetOppByName = 'salesforce-service/salesforce/v1/opportunities?employerName={name}',
  evalSelectOpp = 'evaluate-service/v1/opps/{id}',
  evalGetOppCreds = 'evaluate-service/v1/opps/{id}/pricing/credits',
  evalGetOppFees = 'evaluate-service/v1/opps/{id}/pricing/fees',
  evalGetOppContacts = 'evaluate-service/v1/opps/{id}/contacts',
  evalGetFaJob = 'evaluate-service/v1/opps/fa_jobs/{id}/{fileId}',
  evalGetFile = 'evaluate-service/v1/opps/{id}/fa/{fileId}',
  evalGetFileStats = 'evaluate-service/v1/opps/{id}/fa/{fileId}/incumbentStats',
  evalGetFileInc = 'evaluate-service/v1/opps/{id}/incumbentRenewal',
}
export enum EVALUATE_METHODS {
  evalPostContractInfo = 'evaluate-service/v1/opps/{id}/contract-info', //{"discounts":[{"code":"contractTermLength","value":"3"},{"code":"contractEffDate","value":"2024-09-09T00:00:00.000Z"},{"code":"ssgOrDsg","value":"no"},{"code":"retailBrand","value":"1"},{"code":"retailGeneric","value":"1"},{"code":"retailGuarantee","value":"1"},{"code":"retailFee","value":"1"},{"code":"r90Brand","value":"a"},{"code":"r90Generic","value":"1"},{"code":"r90Guarantee","value":"1"},{"code":"r90Fee","value":"1"},{"code":"mailBrand","value":"1"},{"code":"mailGeneric","value":"1"},{"code":"mailGuarantee","value":"1"},{"code":"mailFee","value":"1"},{"code":"specialtyAwp","value":"1"},{"code":"specialtyGeneric","value":"1"},{"code":"specialtyLD","value":"1"},{"code":"specialtyMailDispFee","value":"1"},{"code":"specialtyRetailDispFee","value":"1"},{"code":"retail","value":"0"}]}
  evalPostFees = 'evaluate-service/v1/opps/{id}/fees', //[{amount: 10,name: "Administrative Fee",type: "PEPM"}]
  evalPostCredits = 'evaluate-service/v1/opps/{id}/credits', //[{"PBM":"","amount":1,"id":"","name":"Medical ASO Fee Credit","type":"PEPM"},{"name":"Rebate Fee Credit","type":"PEPM","amount":2}]
  evalPostInsights = 'evaluate-service/v1/opps/{id}/insights', //[{"id":"*************","ownedBy":"<EMAIL>","message":"test"}]
  evalPostIncAgg = 'evaluate-service/v1/opps/{id}/incumbentAggregates', //{"rebate":600,"isRebateUsed":false,"totalMember":55155,"isTotalMemberUsed":false,"gross":5554,"isGrossUsed":false}
  evalPostContactList = 'evaluate-service/v1/opps/{id}/contacts', //[{"contactName":"Erinn Beekman1","email":"<EMAIL>","id":29,"phone":"************","position":"Strategic Account Executive","prospectOpportunityID":123496,"updatedDate":"2023-04-13T17:40:53Z"}]
  evalPostPricingFees = 'evaluate-service/v1/opps/{id}/pricing/fees', //[{"amount":200,"id":"","name":"Electronic Pharmacy Benefit Eligibility Verification Fee","pbm":"CVS_CAREMARK","type":"PMPM"},{"amount":555,"id":"","name":"CareMark","pbm":"CVS_CAREMARK","type":"PEPM"},{"amount":555,"id":"","name":"ESI","pbm":"EXPRESS_SCRIPTS","type":"PEPM"},{"amount":555,"id":"","name":"Optum","pbm":"OPTUMRX","type":"PEPM"},{"name":"All","type":"PEPM","amount":554,"pbm":"ALL"}]
  evalPostPricingCredits = 'evaluate-service/v1/opps/{id}/pricing/credits', //[{"amount":555,"id":"","name":"CareMark Credit","pbm":"CVS_CAREMARK","type":"PEPM"},{"amount":555,"id":"","name":"ESI Credit","pbm":"EXPRESS_SCRIPTS","type":"PEPM"},{"amount":555,"id":"","name":"Optum Credit","pbm":"OPTUMRX","type":"PEPM"},{"name":"All Credit","type":"PEPM","amount":554,"pbm":"ALL"}]
  evalPostUploadFile = 'evaluate-service/v1/opps/{id}/upload-file',
  updateSalesForce = 'evaluate-service/v1/opps/sf-update',
}

export enum PORTAL_GET {
  homeAlert = 'portal-service/v1/alerts?alertSourceId={sourceId}&returnType={returnType}',
  organizationsListing = 'portal-service/v1/organizations?name={name}&processStatus={processStatus}&effectiveDate={effectiveDate}&expirationDate={terminationDate}&limit={limit}&offset={offset}',
  organizationDetail = 'portal-service/v1/organizations/{orgId}',
  organizations = 'portal-service/v1/organizations?limit=100&offset={orgOffset}&name={orgName}',
  organizationsAll = 'portal-service/v1/organizations?limit=10000&offset={orgOffset}&name={orgName}',
  orgAllUsers = 'portal-service/v1/users?orgNo={orgNo}&active=true&limit=100&offset={userOffset}&name={userName}',
  organizationGroupFees = 'portal-service/v1/organizations/{orgId}/groups/{groupNo}/fees',
  organizationGroups = 'portal-service/v1/organizations/{orgId}/groups',
  organizationPlans = 'portal-service/v1/organizations/{orgId}/plans/coverages',
  organizationInvoices = 'portal-service/v1/organization/{orgId}/invoice-config-list',
  organizationInvoiceConfigs = 'portal-service/v1/organizations/{orgId}/invoices?invoiceConfigNo={invoiceConfigNo}&invoiceNo={invoiceNo}&limit={limit}&offset={offset}',
  organizationLocations = 'portal-service/v1/organizations/{orgId}/locations',
  organizationMeddsubsidy = 'portal-service/v1/organizations/{orgId}/meddsubsidy',
  employees = 'portal-service/v1/organizations/{orgId}/employees?birthDate={birthDate}&effectiveDate={effectiveDate}&expirationDate={expirationDate}&name={name}&ssn={ssn}&alternateId={alternateId}&status={status}&groupNo={groupNo}&limit={limit}&offset={offset}',
  employeeView = 'portal-service/v1/organizations/{orgId}/employees/{empId}',
  employeeGroupOptions = 'portal-service/v1/organizations/{orgId}/employees/{empId}/dependents/{dependentId}/alternate-group-options',
  dependentView = 'portal-service/v1/organizations/{orgId}/employees/{empId}/dependents/{dependentId}',
  employeeViewGroupAssignment = 'portal-service/v1/organizations/{orgId}/employees/{empId}/group-assignments',
  medDSubsidyget = `portal-service/v1/organizations/{orgId}/meddsubsidy`,
  // eslint-disable-next-line
  employeeViewGroup = 'portal-service/v1/organizations/{orgId}/groups',
  employeeViewDependents = 'portal-service/v1/organizations/{orgId}/employees/{empId}/dependents',
  employeeViewCoverages = 'portal-service/v1/organizations/{orgId}/employees/{empId}/coverages',
  employeeSSN = 'portal-service/v1/organizations/{orgId}/employees/{empId}/ssn',
  getDependentSSN = 'portal-service/v1/organizations/{orgId}/employees/{empId}/dependents/{dependentId}/ssn',
  organizationsCount = 'portal-service/v1/organizations/count?limit=4&offset=0',
  employeesCount = 'portal-service/v1/employees/count',
  filesListing = 'portal-service/v1/files?limit={limit}&offset={offset}',
  fileDownload = 'portal-service/v1/files/{fileId}/download',
  fileDetails = 'portal-service/v1/files/{fileId}',
  filesCount = 'portal-service/v1/files?limit=5&offset=0',
  employeeListing = 'portal-service/v1/employees?birthDate={birthDate}&effectiveDate={effectiveDate}&expirationDate={expirationDate}&name={name}&ssn={ssn}&alternateId={alternateId}&status={status}&orgID={orgID}&groupNo={groupNo}&limit={limit}&offset={offset}',
  dependentListing = 'portal-service/v1/dependents?birthDate={birthDate}&effectiveDate={effectiveDate}&expirationDate={expirationDate}&name={name}&ssn={ssn}&alternateId={alternateId}&status={status}&orgID={orgID}&groupNo={groupNo}&limit={limit}&offset={offset}',
  // eslint-disable-next-line
  orgEmployeeListing = 'portal-service/v1/organizations/{orgId}/employees?birthDate={birthDate}&effectiveDate={effectiveDate}&expirationDate={expirationDate}&firstName={firstName}&lastName={lastName}&ssn={ssn}&alternateId={alternateId}&status={status}&groupNo={groupNo}&limit={limit}&offset={offset}',
  orgDependentListing = 'portal-service/v1/organizations/{orgId}/dependents?birthDate={birthDate}&effectiveDate={effectiveDate}&expirationDate={expirationDate}&firstName={firstName}&lastName={lastName}&ssn={ssn}&alternateId={alternateId}&status={status}&groupNo={groupNo}&limit={limit}&offset={offset}',
  getUser = 'portal-service/v1/users/{userId}',
  test = 'portal-service/v1/user/{userId}',
  getCoverages = 'portal-service/v1/organizations/{orgId}/employees/{employeeNo}/coverages',
  getActiveCoverage = 'portal-service/v1/organizations/{orgId}/employees/{employeeNo}/activeCoverage',
  getTempIdInfo = 'portal-service/v1/organizations/{orgId}/employees/{employeeNo}/tempIDCard',
  getPriorAuth = 'portal-service/v1/priorauth/agadia?organizationNo={organizationNo}&alternateId={alternateId}&employeeNo={employeeNo}&firstName={firstName}&lastName={lastName}&dateOfBirth={dateOfBirth}&includeDependents=true&isDependent={isDependent}&dependentList={dependentList}',
  memberIdHistory = 'portal-service/v1/organizations/{orgId}/employees/{empId}/idHistory',
  incentivePrograms = 'portal-service/v1/organizations/{orgId}/groups/{groupNo}/incentive-programs',
  divisions = 'portal-service/portal/v1/organizations/{orgId}/employees/{empId}/division-history',
  benefitPackageHistory = 'portal-service/portal/v1/organizations/{orgId}/employees/{empId}/benefit-package-history',
  benefitPackages = 'portal-service/portal/v1/organizations/{orgId}/benefit-packages',
  employeeDetail = 'portal-service/portal/v1/organizations/{orgId}/employees/{empId}',
  groupDivisions = 'portal-service/portal/v1/organizations/{orgId}/groups/{groupNo}/divisions',
  memberChangeHistory = 'portal-service/portal/go-auth/v1/datanet/memberchangehistory?employeeId={empId}',
}
export enum AUTHORIZATION_GET {
  orgSpecificUsers = 'authorization-service/v1/ap-manage-users/user/org/{orgId}',
  getUserProfile = 'authorization-service/v1/userprofile',
  adminGetContact = 'authorization-service/v1/account-settings',
  getUsers = 'authorization-service/v1/ap-manage-users',
  getUserRoles = 'authorization-service/v1/ap-manage-users/create/roles',
  getUserByRole = 'authorization-service/v1/ap-manage-users/create/{role}',
  getAuthUser = 'authorization-service/v1/ap-manage-users/{userId}',
  getEditUser = `authorization-service/v1/ap-manage-users/user/{userNo}`,
  getReportRecipients = `authorization-service/v1/reporting/apUsers?slug={permSlug}`,
  usersList = `authorization-service/v1/ap-manage-users/user/external/org/{orgId}`,
  getUserTermAndCondition = `authorization-service/authorization/go-auth/v1/ap-manage-users/user/tos`,
  //   getCommissionAccess = 'authorization-service/v1/ap-manage-users/bms-access/commission',
  getCommissionAccess = 'authorization-service/v1/ap-manage-users/user/commissions/eligible-selections',
  getUserCommissionAccess = 'authorization-service/v1/ap-manage-users/user/commissions/eligible-selections?userId={userID}',
  getUserBrokerAccess = 'authorization-service/v1/ap-manage-users/user/commissions/eligible-brokers',
  shareUser = 'authorization-service/authorization/go-auth/v1/reporting/shareUsers?reportSlug={reportSlug}&orgNo={orgId}',
}
export enum PROTECT_GET {
  getContactReasons = 'protect-service/v1/contact-reasons',
  getTherapyconsideration = 'protect-service/v1/therapy-considerations',
  getInapplicability = 'protect-service/v1/inapplicability-reasons',
  getDuplicateError = 'protect-service/v1/duplicate-subreasons',
  getCaseStatuses = 'protect-service/v1/case-statuses',
  getCaseTypes = 'protect-service/v1/case-types',
  getCaseReasonCodes = 'protect-service/v1/reason-codes?limit=50',
  getCaseNoteTypes = 'protect-service/v1/note-types?limit=50',
  getProtectCases = 'protect-service/v1/case-intervention-queues?case_type_id={type}&limit={limit}&offset={offset}',
  getProCases = 'protect-service/v1/case-intervention-queues?case_type_id={id}&is_active=true&limit=',
  // eslint-disable-next-line
  getRetroCases = 'protect-service/v1/case-intervention-queues?case_type_id={id}&is_active=true&limit=',
  getFollowUpCases = 'protect-service/v1/case-followup-queues?limit=',
  getCaseNotes = 'protect-service/v1/case-notes?case_master_id={id}&limit=&sort=id:desc',
  getProtectCondition = 'protect-service/v1/health-condition/{id}',
  getProtectConditionDrugs = 'protect-service/v1/drug-conditions?health_condition_id={id}',
  getProtectConditionDrugsByName = 'protect-service/v1/drug-conditions?drug_name={drug_name}&limit=0',
  getProtectConditions = 'protect-service/v1/health-conditions?limit={limit}&offset={offset}',
  getProtectConditionsOrdered = 'protect-service/v1/health-conditions?limit=0&sort=name:asc',
  getProtectConditionLists = 'protect-service/v1/health-conditions',
  getProtectInterventionLists = 'protect-service/v1/intervention-lists?limit={limit}&offset={offset}&sort=name:asc',
  getProtectIntervention = 'protect-service/v1/intervention-list/{id}',
  getProtectInterventionDrugs = 'protect-service/v1/intervention-list/{id}/target-drugs',
  getProtectInterventionDrug = 'protect-service/v1/intervention-list/{id}/target-drug/{drugId}',
  getProtectProvider = 'protect-service/v1/provider/{id}',
  getProtectSearchProviders = 'protect-service/v1/providers?npi={npi}&provider_first_name={firstName}&provider_last_name={lastName}&sort=provider_last_name:asc',
  getProtectMemberConditions = 'protect-service/v1/member-conditions?member_profile_id={profileId}',
  getProtectMemberProfile = 'protect-service/v1/member-profile/{id}',
  getProtectMemberCaseHistory = 'protect-service/v1/member-profile/{id}/case-history?limit={limit}&offset={offset}',
  getProtectCaseMasters = 'protect-service/v1/case-masters?limit={limit}&offset={offset}',
  getProtectCase = 'protect-service/v1/case-master/{id}',
  getCaseAccess = 'protect-service/v1/case-master/{id}/access',
  getProtectCaseDocument = 'protect-service/v1/case-documents/{docId}',
  getProtectCaseStatuses = 'protect-service/v1/case-statuses?is_active=true&limit=0',
  getProtectCaseStatusIdMappings = 'protect-service/v1/case_ids_mapping?code=CSCS&target=STOP_ACTION&key_id={caseStatusId}&case_type_id={caseTypeId}',
  getProtectReasonCodes = 'protect-service/v1/reason-codes?limit=0',
  getProtectReasonCodeIdMappings = 'protect-service/v1/case_ids_mapping?code=CSRC&target=STOP_ACTION&key_id={caseStatusId}&case_type_id={caseTypeId}',
  getProtectFollowUps = 'protect-service/v1/case-follow-ups',
  getProtectUsers = 'protect-service/v1/protect-users',
  getProtectUser = 'protect-service/v1/protect-users?email={email}',
  getProtectUserById = 'protect-service/v1/protect-user/{id}',
  getProtectCaseDocuments = 'protect-service/v1/case-documents?case_master_id={caseId}&limit=',
  getProtectCaseLock = 'protect-service/v1/case-lock/{caseId}',
}
export enum FMS_GET {
  commissionGlAccounts = 'fms-service/commtypes/',
  glIds = 'fms-service/glaccounts/',
}
export enum MSR_GET {
  getMemberListing = `msrsearch-service/v1/records?memberId={memberId}&birthdate={birthDate}&firstname={firstName}&lastname={lastName}&organizationNo={orgID}&limit={limit}&offset={offset}`,
}
export enum PLAN_PROVISION_GET {
  getPBC = 'planprovision-service/plans/v1/pbc?employeeNo={employeeNo}&dependentNo={dependentNo}',
  getIsProtect = 'planprovision-service/plans/v1/protect/{orgId}',
  getGroupInfo = 'planprovision-service/plans/v1/groupinfo/{orgId}',
  getPAReview = 'planprovision-service/plans/v1/priorauth/{orgId}',
}
export enum KEYSTONE_FILE_GET {
  getProtectDrugSearchResults = '/keystonefile-service/v1/drug-details?sort=ndc:desc&q={query}&distinct=gpi_10&offset=0&limit=10',
  getDrugByNDC = 'keystonefile-service/v1/drugs/{ndc}',
}
export enum EOC_GET {
  getEOCDocs = 'eoc-service/v1/eoc/GetEOC/{eocId}/{eocEventId}/docs',
}
export enum CLAIMS_HUB_GET {
  memberGetClaims = 'claimshub-service/v1/claims?organizationNo={orgID}&employeeNo={employeeNo}&dependentNo={dependentNo}&limit={limit}&offset={offset}&dependentList={dependentList}',
  clients340b = 'claimshub-service/v1/clients?limit={limit}&offset={offset}',
  validateLocalScripts = `claimshub-service/v1/localscripts/validate/{id}?pricingOption={pricingOption}`,
}
export enum MEMBER_HUB_GET {
  memberGetConfidentiality = 'membershub-service/v1/member/confidentiality?organizationNo={orgNo}&employeeNo={empNo}&dependentNo={depNo}&birthDate={birthDate}&testCase={testCase}',
}
export enum SALESFORCE_GET {
  pharmacyBrokerDetails = 'salesforce-service/v1/brokers/details',
  pharmacyBrokerProspects = 'salesforce-service/v1/brokers/prospects',
  pharmacyEmployerContacts = 'salesforce-service/v1//brokers/employercontacts?accountId={accountId}',
  pharmacyClientsList = 'salesforce-service/v1/brokers/clients',
}
export enum REPORTING_GET {
  getLandingReport = 'reporting-service/v1/report/getLandingReport',
  reportsList = 'reporting-service/v1/report/getReports?limit={limit}&offset={offset}&categoryId={categoryId}&reportfriendlyname={reportfriendlyname}&sortBy={sortBy}&order={order}',
  getReportDataUsingId = 'reporting-service/v1/report/{reportId}',
  reportingCategories = 'reporting-service/v1/report/reportCategories',
  activitySaved = `reporting-service/v1/report/saveReports?limit={limit}&offset={offset}`,
  activityScheduled = `reporting-service/v1/report/scheduledReport?limit={limit}&offset={offset}`,
  getReportDownloads = `reporting-service/v1/report/saveReports/download?offset={offset}&limit={limit}&keyword={keyword}&orgNo={orgNo}&runDate={runDate}`,
  getActivityScheduled = `reporting-service/v1/report/scheduledReport`,
  reportingEligiblitySummaryFilters = 'reporting-service/v1/report/{reportID}/filters?orgNo={orgId}',
  runReport = `reporting-service/v1/report/runReport?reportId={reportId}`,
  getFavourites = 'reporting-service/v1/report/favorites?limit={limit}&offset={offset}&keyword={keyword}&orgNo={orgNo}&categoryId={categoryId}',
  saveReportById = 'reporting-service/v1/report/favorites?saveReportId={saveReportID}',
  editScheduledReport = 'reporting-service/v1/report/favorites/{saveReportId}/schedule',
  getUpcomingScheduleReports = 'reporting-service/v1/report/favorites/schedules',
}
export enum COMMUNICATION_MANAGER_GET {
  commManagerLogs = 'communicationmanager-service/memberinterventionLogs?organizationNo={organizationNo}&alternateId={alternateId}&employeeNo={employeeNo}&firstName={firstName}&lastName={lastName}&dateOfBirth={dateOfBirth}&includeDependents=true&isDependent={isDependent}&dependentList={dependentList}',
  getAOR = 'communicationmanager-service/aorSignature?memberID={memberID}&orgID={organizationNo}&employeeNo={employeeNo}&dependentNo={dependentNo}&aorID={aorID}',
}

//******************METHOD*********************//
export enum PORTAL_METHOD {
  fileUpload = 'portal-service/v1/files/ap',
  fileUpdate = 'portal-service/v1/files/ap/{fileId}',
  fileDelete = 'portal-service/v1/files/{fileId}',
  fileDownload = 'portal-service/v1/files/{fileId}/download',
  organizationInvoiceDownload = 'portal-service/v1/organizations/{orgId}/invoices/{invoiceNo}/report?type={type}&format={format}',
  emailTempCard = 'portal-service/v1/organizations/{orgId}/employees/{employeeNo}/emailTempCard',
  removeTermination = 'portal-service/v1/organizations/{id}/employees/{empId}/termination',
  reinstateTermination = 'portal-service/v1/organizations/{id}/employees/{empId}/reinstatement',
  updateEmployeeView = 'portal-service/v1/organizations/{orgId}/employees/{empId}',
  createEmployee = 'portal-service/v1/organizations/{id}/employees',
  createDependent = 'portal-service/v1/organizations/{id}/employees/{empId}/dependents',
  employeeDependentTermination = 'portal-service/v1/organizations/{orgId}/employees/{empId}/dependents/{dependentId}/termination',
  employeeDependentReinstatement = 'portal-service/v1/organizations/{orgId}/employees/{empId}/dependents/{dependentId}/reinstatement',
  updateDependentView = 'portal-service/v1/organizations/{orgId}/employees/{empId}/dependents/{dependentId}',
  updateDependentSSN = 'portal-service/v1/organizations/{orgId}/employees/{empId}/dependents/{dependentId}/ssn',
  updateGroupAssignment = 'portal-service/v1/organizations/{orgId}/employees/{empId}/group',
  employeeNewCardRequest = 'portal-service/v1/organizations/{orgId}/employees/{empId}/coverages/{coverageNo}/new-card',
  addNewDivision = 'portal-service/portal/v1/organizations/{orgId}/employees/{empId}/divisions',
  updateBenefitPacakage = 'portal-service/v1/organizations/{orgId}/employees/{empId}/benefit-package',
  invoiceClaimExtract = 'portal-service/portal/v1/organizations/{OrgNo}/invoices/{invoiceNo}/report?type=InvoiceClaimExtract&format=Text',
}
export enum AUTHORIZATION_METHOD {
  resetOwnPassword = 'authorization-service/v1/auth0-management/password-reset-email-self',
  adminPostContact = 'authorization-service/v1/account-settings',
  resetPhoneMFA = 'authorization-service/v1/auth0-management/reset-mfa',
  createUser = 'authorization-service/v1/ap-manage-users/user',
  postEditUser = 'authorization-service/v1/ap-manage-users/user?userId={userNo}',
  postUserTermAndCondition = `authorization-service/authorization/go-auth/v1/ap-manage-users/user/tos`,
  postCommissionsAccess = 'authorization-service/v1/ap-manage-users/user/commissions',
  newUserRequestForm = 'authorization-service/v1/ap-manage-users/newUserRequest',
  unlockAccount = 'authorization-service/v1/auth0-management/user-blocks?email={email}',
}
export enum PROTECT_METHOD {
  deleteProtectTargetDrug = 'protect-service/v1/intervention-list/{id}/target-drug/{drugId}',
  deleteProtectAltDrug = 'protect-service/v1/intervention-list/{id}/target-drug/{drugId}/alt-drug/{altDrugId}',
  postProtectAltDrug = 'protect-service/v1/intervention-list/{id}/target-drug/{drugId}/alt-drugs',
  postProtectTargetDrug = 'protect-service/v1/intervention-list/{id}/target-drugs',
  postProtectInterventionList = 'protect-service/v1/intervention-list',
  postProtectCondition = 'protect-service/v1/health-condition',
  postProtectProvider = 'protect-service/v1/provider',
  postProtectInteraction = 'protect-service/v1/interactions',
  postProtectMemberProfile = 'protect-service/v1/member-profile',
  putProtectCaseMaster = 'protect-service/v1/case-master/{id}',
  postProtectCaseLock = 'protect-service/v1/case-lock',
  searchProtectCases = 'protect-service/v1/case-search',
  postOrDeleteProtectConditionDrugs = 'protect-service/v1/drug-conditions',
  putProtectCondition = 'protect-service/v1/health-condition/{id}',
  postProtectMemberConditions = 'protect-service/v1/member-conditions',
  deleteProtectMemberCondition = 'protect-service/v1/member-condition/{id}',
  downloadCaseDoc = 'protect-service/v1/case-documents/{docId}/download',
  uploadCaseDocument = 'protect-service/v1/case-master/{caseId}/case-documents',
}
export enum FMS_METHOD {
  postCommissionGlAccounts = '/fms-service/commtypes/{id}/',
}
export enum PLAN_PROVISION_METHOD {}
export enum KEYSTONE_FILE_METHOD {}
export enum EOC_METHOD {
  getEOCPriorAuths = 'eoc-service/v1/enterprise/AgadiaPriorAuthorization',
  getEOCDoc = 'eoc-service/v1/eoc/GetEOCDoc/{docId}',
}
export enum CLAIMS_HUB_METHOD {
  addClients340b = 'claimshub-service/v1/clients',
  editClients340b = 'claimshub-service/v1/clients/{id}',
  deleteClients340b = 'claimshub-service/v1/clients?OrgNo={OrgNo}',
  uploadFile340b = 'claimshub-service/v1/import/claims/upload',
  uploadDispenseReport340b = 'claimshub-service/v1/import/340BTruePrice/{OrgNo}',
  deleteDispenseReport340b = 'claimshub-service/v1/import/340BTruePrice?OrgNo={OrgNo}',
  uploadRejectReport340b = 'claimshub-service/v1/import/rejectReport',
  getLocalScripts = `claimshub-service/v1/localscripts/{id}?pricingOption={pricingOption}`,
  prescriberOverride340b = `claimshub-service/v1/import/340BPrescriberOverride/{E340BID}`,
}

export enum MEMBER_HUB_METHOD {
  memberGetCommunicationPreferences = 'membershub-service/v1/member/GetCommunicationPref',
  memberUpdateCommunicationPreferences = 'membershub-service/v1/member/UpdateCommsPref',
  memberGetConfidentiality = 'membershub-service/v1/member/confidentiality',
}
export enum SALESFORCE_METHOD {
  pharmacyMakeBrokerCertified = 'salesforce-service/v1/brokers/certify?isCertified={isCertified}',
  pharmacyUpdateBrokerClientStatus = 'salesforce-service/v1/brokers/accept?productAssociationId={productAssociationId}&orgName={orgName}&effectiveDate={effectiveDate}&deductible={deductible}&rxbClientStatus={rxbClientStatus}&enrolledInProtect={enrolledInProtect}&receivesCommission={receivesCommission}&employerContactId={employerContactId}&accountId={accountId}',
}
export enum REPORTING_METHOD {
  saveReport = 'reporting-service/v1/report/saveReports',
  shareReport = 'reporting-service/v1/report/saveReports/share',
  postReportDownloads = `reporting-service/v1/report/saveReports/download`,
  deleteReport = 'reporting-service/v1/report/saveReports/{id}',
  postScheduleReport = `reporting-service/v1/report/favorites/schedule`,
  runReport = `reporting-service/v1/report/runReport?reportId={reportId}`,
  generateReport = `reporting-service/v1/report/generateReport?reportId={reportId}`,
  postFavourites = 'reporting-service/v1/report/favorites',
  editFavouriteConfiguration = 'reporting-service/v1/report/favorites/configuration',
  shareDownloadReport = 'reporting-service/v1/report/shareReport',
  deleteScheduleReport = 'reporting-service/v1/report/favorites/schedule?saveReportId={scheduleId}',
}
export enum COMMUNICATION_MANAGER_METHOD {
  postAOR = 'communicationmanager-service/aorSignature',
  downloadAOR = 'communicationmanager-service/downloadAorSignature?aorID={aorId}',
  deleteAOR = 'communicationmanager-service/aorSignature?memberID={memberID}&orgID={orgID}&employeeNo={employeeNo}&aorID={aorID}',
}
export enum ATHENA_METHOD {
  getAthenaBenefitGroup = 'athena-service/v1/enterprise/BenefitGroup',
  getAthenaMember = 'athena-service/v1/enterprise/Member',
  getAthenaEnrollment = 'athena-service/v1/enterprise/Tracking',
  getAthenaPriorAuths = 'athena-service/v1/enterprise/AgadiaPriorAuthorization',
  getAthenaClaims = 'athena-service/v1/enterprise/protect/Claim',
  getAthenaNDC = 'athena-service/v1/enterprise/AgadiaNDC',
  getAthenaOrganization = 'athena-service/v1/enterprise/Organization',
}
export enum MEMBER_ISSUES_METHOD {
  memberContactUs = 'memberissues-service/v1/contact-form',
  memberNotes = 'memberissues-service/v1/records/{memberId}?limit={limit}',
  memberNotesAPi = 'memberissues-service/v1/records/planNo={planNo}&employeeNo={employeeNo}&dependentNo={dependentNo}&organizationNo={organizationNo}&actorId={actorId}&callId={callId}&phoneNo={phoneNo}&name={name}&communicationMethod={communicationMethod}&relationship{relationship}&memberIssueTypeNo={memberIssueTypeNo}&memberIssueSubTypeNo={memberIssueSubTypeNo}&eocs={eocs}&comment={comment}&furtherActionNeeded={furtherActionNeeded}',
  memberIssue = 'memberissues-service/v1/records/{issueNo}?isIssue={isIssue}',
  issueTypes = 'memberissues-service/v1/issueTypes',
  pendingNotes = 'memberissues-service/v1/records/{userNo}?&isAgent=true&isPending=true&limit={limit}&offset={offset}&sort=organizationName:asc',
}
export enum MEMBER_SEARCH_METHOD {
  memberSearch = 'msrsearch-service/v1/records?organizationNo={organizationNo}&firstname={firstname}&lastname={lastname}&dateOfBirth={dateOfBirth}&memberId={memberId}&sort={sort}&limit={limit}&offset={offset}',
}

//**********ADDITIVE***********//
export const GET_ROUTES = {
  ...PORTAL_GET,
  ...AUTHORIZATION_GET,
  ...PROTECT_GET,
  ...FMS_GET,
  ...MSR_GET,
  ...PLAN_PROVISION_GET,
  ...KEYSTONE_FILE_GET,
  ...EOC_GET,
  ...CLAIMS_HUB_GET,
  ...SALESFORCE_GET,
  ...REPORTING_GET,
  ...EVALUATE_GET,
  ...MEMBER_HUB_GET,
  ...COMMUNICATION_MANAGER_GET,
  ...MEMBER_SEARCH_METHOD,
  ...MEMBER_ISSUES_METHOD,
} as const;

export const METHOD_ROUTES = {
  ...PORTAL_METHOD,
  ...AUTHORIZATION_METHOD,
  ...PROTECT_METHOD,
  ...FMS_METHOD,
  ...PLAN_PROVISION_METHOD,
  ...KEYSTONE_FILE_METHOD,
  ...EOC_METHOD,
  ...CLAIMS_HUB_METHOD,
  ...MEMBER_HUB_METHOD,
  ...SALESFORCE_METHOD,
  ...REPORTING_METHOD,
  ...COMMUNICATION_MANAGER_METHOD,
  ...ATHENA_METHOD,
  ...MEMBER_ISSUES_METHOD,
  ...EVALUATE_METHODS,
} as const;

export enum PERMISSIONS_GET {
  getNav = 'getNav',
  showComponent = 'showComponent',
  eligQlikAppId = 'eligQlikAppId',
  env = 'env',
}

// enum METHOD_ROUTES {
//   test = '/employees',
// }
