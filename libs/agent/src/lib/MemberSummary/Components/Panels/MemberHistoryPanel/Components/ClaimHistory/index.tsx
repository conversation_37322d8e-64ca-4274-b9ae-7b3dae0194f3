import { CopyOutlined } from '@ant-design/icons';
import { pbmURLs } from '@optimize/constants';
import { theme } from '@optimize/ui';
import { Col, Row } from 'antd';

import { getBrowserRequirement } from '../../../../../../Utils';

function generateLink(pbm) {
  const pbmLinkObject = pbmURLs.filter((item) => pbm.includes(item.pbmName));

  return pbmLinkObject[0].url;
}

const filterCoverages = (coverages) => {
  const pbms = ['Express Scripts', 'Optum', 'Caremark', 'Illuminate Rx'];
  const uniquePBM = new Set();
  coverages = coverages.filter((coverage) => {
    if (uniquePBM.has(coverage.name)) {
      return false;
    } else {
      uniquePBM.add(coverage.name);
      return true;
    }
  });
  return coverages.filter((coverage) => {
    return pbms.some((pbm) => coverage.name.includes(pbm));
  });
};

export const ClaimHistory = ({ coverages }) => {
  return (
    <Row style={{ padding: '1.5em' }}>
      {!!filterCoverages(coverages).length && (
        <Col span={4}>
          <span style={{ fontWeight: 'bold' }}>PBM</span>
        </Col>
      )}
      <Col>
        {filterCoverages(coverages).map((coverage, i) => {
          return (
            <div key={i}>
              <p style={{ fontWeight: 'bold', marginBottom: '.25em' }}>{coverage.name}</p>
              <a href={generateLink(coverage.name)} target="_blank" rel="noreferrer">
                View Claims in PBM System
              </a>
              <span style={{ color: theme.colors.primary, paddingLeft: '1em' }}>
                <CopyOutlined
                  onClick={() => {
                    navigator.clipboard.writeText(generateLink(coverage.name));
                  }}
                />
              </span>
              <p style={{ fontSize: '.8em' }}>{getBrowserRequirement(coverage.name)}</p>
            </div>
          );
        })}
      </Col>
      {!filterCoverages(coverages).length && (
        <Col>
          <p style={{ fontWeight: 'bold', marginBottom: '.25em' }}>No PBM Coverage</p>
        </Col>
      )}
    </Row>
  );
};
